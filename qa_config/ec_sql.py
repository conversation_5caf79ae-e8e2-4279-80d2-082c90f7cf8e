from weeeTest import log
from weeeTest.utils import json

from qa_config.secret import get_secret
from qa_config.basic_db import ConnectDatabase


class EcDB(object):
    try:
        db_config = get_secret()
    except Exception as e:
        log.info("获取secret失败" + str(e))

    def __init__(self):
        try:
            self.ec_cd = ConnectDatabase(host="weee.db.tb1.sayweee.net", user=self.db_config["db_ec_username"],
                                         password=self.db_config["db_ec_password"], db_name="weee",
                                         return_type="json")
        except Exception as e:
            log.info("你没有配置访问数据库的用户名和密码" + str(e))

    def get_user_id(self, email):
        """ 通过email 获取用户id"""
        try:
            sql = f""" select * from weee.user where email = '{email}' """
            print(sql)
            # sql = f"""select * from weee.user where email = '<EMAIL>' """

            results = self.ec_cd.query_data(sql)
            log.info(f'请求参数：{email}；请求结果：账号ID：{results[0][0]}')
            return results[0][0]
        except TypeError:
            return False

    def get_sales_org_id_zipcode(self):
        """ 销售组织id与zipcode"""
        try:
            sql = f"""
            select sales_org_id,zipcode from weee_comm.gb_zipcode_area WHERE sales_org_id !=301 group by sales_org_id """
            results = self.ec_cd.query_data(sql)
            log.info(f'请求参数：请求结果：账号ID：{results}')
            return results
        except TypeError:
            return False

    def get_discount(self, type, vendor_id):
        """ 通过vendor_id 获取商家折扣 type:discount_all,discount_partial,discount_special_price,discount_lightning"""
        try:
            sql = f""" select * from  weee_mkpl.marketplace_discount where type = '{type}' and  vendor_id='{vendor_id}' """
            results = self.ec_cd.query_data(sql)
            log.info(f'请求参数：{type},{vendor_id}；请求结果：{results[0][0]}')
            return results[0][0]
        except TypeError:
            return False

    def refund_order(self, order_id):
        """ 通过更新订单数据实现申请售后功能 """
        try:
            sql1 = f""" UPDATE weee_comm.gb_order  SET invoice_id = '{order_id}', status  = 'F',close_time =DATE_SUB(CURRENT_TIMESTAMP(),INTERVAL 1 DAY) WHERE id ='{order_id}' """
            # print(sql1)
            sql2 = f""" UPDATE weee_comm.gb_order_product SET delivery_id='22',original_delivery_id='22' where order_id ='{order_id}' """
            # print(sql2)
            sql3 = f""" UPDATE weee_comm.gb_order_ext SET sort_date=DATE_SUB(CURRENT_TIMESTAMP(),INTERVAL 1 DAY) where order_id='{order_id}' """
            # print(sql3)
            results1 = self.ec_cd.update_data(sql1)
            results2 = self.ec_cd.update_data(sql2)
            results3 = self.ec_cd.update_data(sql3)
            log.info(f'请求参数：{order_id}；请求结果：{results1}')
            return results1, results2, results3
        except TypeError:
            return False
    def to_review_order(self, order_id):
        """ 通过更新订单数据实现待晒单功能 """
        try:
            sql1 = f""" UPDATE weee_comm.gb_order  SET invoice_id = '{order_id}', status  = 'F',close_time =DATE_SUB(CURRENT_TIMESTAMP(),INTERVAL 1 DAY) WHERE id ='{order_id}' """
            results1 = self.ec_cd.update_data(sql1)
            log.info(f'请求参数：{order_id}；请求结果：{results1}')
            return results1
        except TypeError:
            return False
    def today_order(self, order_id):
        """ 通过更新订单数据实现今日订单功能 """
        try:
            sql1 = f""" UPDATE weee_comm.gb_deal SET status = 'C',delivery_date = DATE(CONVERT_TZ(NOW(), @@session.time_zone, 'America/Los_Angeles')) WHERE id IN (SELECT deal_id FROM weee_comm.gb_order WHERE id ='{order_id}') """
            print(sql1)
            sql2 = f""" UPDATE weee_comm.gb_order set invoice_id ='7897890' where id ='{order_id}' """
            print(sql2)
            sql3 = f""" UPDATE  weee_comm.gb_order_ext set shipping_status='5' where order_id='{order_id}' """
            print(sql3)
            results1 = self.ec_cd.update_data(sql1)
            results2 = self.ec_cd.update_data(sql2)
            results3 = self.ec_cd.update_data(sql3)
            log.info(f'请求参数：{order_id}；请求结果：{results1}')
            return results1, results2, results3
        except TypeError:
            return False

    def add_on_order(self, order_id):
        """ 通过更新订单数据实现addon功能 """
        try:
            sql1 = f""" UPDATE weee_comm.gb_order  SET invoice_id = '78889923', status  = 'F',close_time =DATE_SUB(CURRENT_TIMESTAMP(),INTERVAL 1 DAY) WHERE id ='{order_id}' """
            print(sql1)
            sql2 = f""" UPDATE weee_comm.gb_order_product SET delivery_id='22',original_delivery_id='22' where order_id ='{order_id}' """
            print(sql2)
            sql3 = f""" UPDATE weee_comm.gb_order_ext SET sort_date=DATE_SUB(CURRENT_TIMESTAMP(),INTERVAL 1 DAY) where order_id='{order_id}' """
            print(sql3)
            results1 = self.ec_cd.update_data(sql1)
            results2 = self.ec_cd.update_data(sql2)
            results3 = self.ec_cd.update_data(sql3)
            log.info(f'请求参数：{order_id}；请求结果：{results1}')
            return results1, results2, results3
        except TypeError:
            return False

    def island_order(self, order_id):
        """ 通过更新订单数据实现灵动岛功能 """
        try:
            sql1 = f""" UPDATE weee_comm.gb_order SET deal_id = (SELECT gd.id FROM weee_comm.gb_deal gd WHERE gd.sales_org_id = 1 
            AND gd.delivery_date = DATE(DATE_ADD(CONVERT_TZ(CONCAT(CURDATE(), ' 00:00:00'), 'Asia/Shanghai', 'America/Los_Angeles'), INTERVAL 1 DAY)))
            WHERE id = '{order_id}' """
            print(sql1)
            sql2 = f""" UPDATE weee_comm.gb_order SET  status='P', invoice_id=31490458, tracking_number='15465772' 
            WHERE id ='{order_id}'"""
            print(sql2)
            sql3 = f""" UPDATE weee_comm.gb_order_ext SET sort_date = DATE(CONVERT_TZ(NOW(), 'Asia/Shanghai', 'America/Los_Angeles')),
            shipping_status = 4 WHERE order_id ='{order_id}' """
            print(sql3)
            results1 = self.ec_cd.update_data(sql1)
            results2 = self.ec_cd.update_data(sql2)
            results3 = self.ec_cd.update_data(sql3)
            log.info(f'请求参数：{order_id}；请求结果：{results1}')
            return results1, results2, results3
        except TypeError:
            return False

    def get_carrier_name(self, carrier_id):
        """ 通过carrier_id 获取物流名称"""
        try:
            sql = f""" select name from weee_comm.carrier where id = '{carrier_id}' """
            print(sql)
            results = self.ec_cd.query_data(sql)
            print(results)
            log.info(f'请求参数：{carrier_id}；请求结果：物流名称：{results[0][0]}')
            return results[0][0]
        except TypeError:
            return False
if __name__ == '__main__':
    ec = EcDB()
    # ec_sql = ec.get_user_id(email="<EMAIL>")
    ec_sql = ec.get_sales_org_id_zipcode()
    # ec_sql = ec.update_order(order_id="42606128")
