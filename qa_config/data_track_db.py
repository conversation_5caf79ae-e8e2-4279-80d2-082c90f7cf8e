import pymysql
from dbutils.pooled_db import PooledDB


class MySQLDB:
    def __init__(self, config):
        self.pool = PooledDB(
            creator=pymysql,
            maxconnections=10,
            mincached=2,
            **config
        )

    def __enter__(self):
        self.conn = self.pool.connection()
        self.cursor = self.conn.cursor(cursor=pymysql.cursors.DictCursor)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is None:
            self.conn.commit()
        else:
            self.conn.rollback()
        self.cursor.close()
        self.conn.close()

    def execute(self, sql, params=None):
        try:
            affected_rows = self.cursor.execute(sql, params or ())
            return affected_rows
        except pymysql.OperationalError:
            self.reconnect()
            return self.cursor.execute(sql, params)

    def fetchall(self):
        return self.cursor.fetchall()

    def fetchone(self):
        return self.cursor.fetchone()

    def reconnect(self):
        try:
            self.conn.ping(reconnect=True)
        except Exception as e:
            print(f"Reconnect failed: {str(e)}")


# 使用示例
if __name__ == "__main__":
    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': 'your_password',
        'database': 'test_db',
        'charset': 'utf8mb4'
    }

    with MySQLDB(config) as db:
        # 创建表
        db.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(50) NOT NULL,
                email VARCHAR(100) UNIQUE
            )
        """)

        # 插入数据（参数化防止注入）
        db.execute(
            "INSERT INTO users (name, email) VALUES (%s, %s)",
            ('Alice', '<EMAIL>')
        )

        # 查询数据
        db.execute("SELECT * FROM users WHERE name LIKE %s", ('A%',))
        print(db.fetchall())
