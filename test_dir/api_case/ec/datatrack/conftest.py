import json
import os

import allure
import clickhouse_connect
import pytest
import weeeTest
from allure import step
from pytest_assume.plugin import assume
import datetime
from clickhouse_connect.driver.client import Client
from .HttpRequest import HttpRequest as HR

from weeeTest import log

# def pytest_generate_tests(metafunc):
#     print("name===>", metafunc.definition.keywords.node.name)
#     if "test_88880000_dynamic" in metafunc.definition.keywords.node.name:
#         metafunc.parametrize("platform, page_key", [["ios", 'ios_home']])


@pytest.fixture(scope="session")
def time_range(request):

    if os.getenv("begin") and os.getenv("end"):
        begin, end = os.getenv("begin"), os.getenv("end")
    else:
        begin = (datetime.datetime.now() + datetime.timedelta(days=-1)).strftime("%Y-%m-%d") + " 00:00:00"
        end = (datetime.datetime.now() + datetime.timedelta(days=-1)).strftime("%Y-%m-%d") + " 23:59:59"
        log.info("begin===>" + begin)
        log.info("end===>" + end)
        # begin = "2025-02-10 12:00:00"
        # end = "2025-02-10 23:59:59"

    return {
        "begin": begin,
        "end": end
    }

@pytest.fixture(scope="session")
def analysis_time_scope(request):

    if os.getenv("begin_date"):
        begin_date = os.getenv("begin_date")
        begin_scope = [
            {"begin": begin_date + " 00:00:00", "end": begin_date + " 01:00:00"},
            {"begin": begin_date + " 01:00:00", "end": begin_date + " 02:00:00"},
            {"begin": begin_date + " 02:00:00", "end": begin_date + " 03:00:00"},
            {"begin": begin_date + " 03:00:00", "end": begin_date + " 04:00:00"},
            {"begin": begin_date + " 04:00:00", "end": begin_date + " 05:00:00"},
            {"begin": begin_date + " 05:00:00", "end": begin_date + " 06:00:00"},
            {"begin": begin_date + " 06:00:00", "end": begin_date + " 07:00:00"},
            {"begin": begin_date + " 07:00:00", "end": begin_date + " 08:00:00"},
            {"begin": begin_date + " 08:00:00", "end": begin_date + " 09:00:00"},
            {"begin": begin_date + " 09:00:00", "end": begin_date + " 10:00:00"},
            {"begin": begin_date + " 10:00:00", "end": begin_date + " 11:00:00"},
            {"begin": begin_date + " 11:00:00", "end": begin_date + " 12:00:00"},
            {"begin": begin_date + " 12:00:00", "end": begin_date + " 13:00:00"},
            {"begin": begin_date + " 13:00:00", "end": begin_date + " 14:00:00"},
            {"begin": begin_date + " 14:00:00", "end": begin_date + " 15:00:00"},
            {"begin": begin_date + " 15:00:00", "end": begin_date + " 16:00:00"},
            {"begin": begin_date + " 16:00:00", "end": begin_date + " 17:00:00"},
            {"begin": begin_date + " 17:00:00", "end": begin_date + " 18:00:00"},
            {"begin": begin_date + " 18:00:00", "end": begin_date + " 19:00:00"},
            {"begin": begin_date + " 19:00:00", "end": begin_date + " 20:00:00"},
            {"begin": begin_date + " 20:00:00", "end": begin_date + " 21:00:00"},
            {"begin": begin_date + " 21:00:00", "end": begin_date + " 22:00:00"},
            {"begin": begin_date + " 22:00:00", "end": begin_date + " 23:00:00"},
            {"begin": begin_date + " 23:00:00", "end": begin_date + " 23:59:59"}
        ]

    else:
        orgin_data = "2024-09-05"
        begin_scope = [
            {"begin": orgin_data + " 00:00:00", "end": orgin_data + " 01:00:00"},
            {"begin": orgin_data + " 01:00:00", "end": orgin_data + " 02:00:00"},
            {"begin": orgin_data + " 02:00:00", "end": orgin_data + " 03:00:00"},
            {"begin": orgin_data + " 03:00:00", "end": orgin_data + " 04:00:00"},
            {"begin": orgin_data + " 04:00:00", "end": orgin_data + " 05:00:00"},
            {"begin": orgin_data + " 05:00:00", "end": orgin_data + " 06:00:00"},
            {"begin": orgin_data + " 06:00:00", "end": orgin_data + " 07:00:00"},
            {"begin": orgin_data + " 07:00:00", "end": orgin_data + " 08:00:00"},
            {"begin": orgin_data + " 08:00:00", "end": orgin_data + " 09:00:00"},
            {"begin": orgin_data + " 09:00:00", "end": orgin_data + " 10:00:00"},
            {"begin": orgin_data + " 10:00:00", "end": orgin_data + " 11:00:00"},
            {"begin": orgin_data + " 11:00:00", "end": orgin_data + " 12:00:00"},
            {"begin": orgin_data + " 12:00:00", "end": orgin_data + " 13:00:00"},
            {"begin": orgin_data + " 13:00:00", "end": orgin_data + " 14:00:00"},
            {"begin": orgin_data + " 14:00:00", "end": orgin_data + " 15:00:00"},
            {"begin": orgin_data + " 15:00:00", "end": orgin_data + " 16:00:00"},
            {"begin": orgin_data + " 16:00:00", "end": orgin_data + " 17:00:00"},
            {"begin": orgin_data + " 17:00:00", "end": orgin_data + " 18:00:00"},
            {"begin": orgin_data + " 18:00:00", "end": orgin_data + " 19:00:00"},
            {"begin": orgin_data + " 19:00:00", "end": orgin_data + " 20:00:00"},
            {"begin": orgin_data + " 20:00:00", "end": orgin_data + " 21:00:00"},
            {"begin": orgin_data + " 21:00:00", "end": orgin_data + " 22:00:00"},
            {"begin": orgin_data + " 22:00:00", "end": orgin_data + " 23:00:00"},
            {"begin": orgin_data + " 23:00:00", "end": orgin_data + " 23:59:59"}
        ]

    return begin_scope




@pytest.fixture(scope="session")
def track(request):
    begin = str(datetime.date.today()) + " 00:00:00"
    end = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime("%Y-%m-%d") + " 00:00:00"
    # begin = "2024-08-14 00:00:00"
    # end = "2024-08-15 00:00:00"
    track = DataTrack().get_track(begin, end)
    if track:
        yield track
        log.info("测试用例执行完毕")
    else:
        raise Exception("没有获取track数据")


@pytest.fixture(scope="session")
def clickhouse_client(request):
    client = ClickhouseConnection.get_client()
    if client:
        yield client
        log.info("测试用例执行完毕")
    else:
        raise Exception("获取clickhouse连接失败")

@pytest.fixture(scope="session")
def clickhouse_tb1_client(request):
    client = ClickhouseConnection.get_tb1_client()
    if client:
        yield client
        log.info("测试用例执行完毕")
    else:
        raise Exception("获取clickhouse连接失败")


class ClickhouseConnection(weeeTest.TestCase):
    client = None

    @classmethod
    def get_client(cls) -> Client:
        try:
            cls.client = clickhouse_connect.get_client(
                host='ch.data.sayweee.net',
                port=8123,
                username='weee_qa',
                password=r'18o38lYmxRAEchpZ',
                database='weee_data',
                connect_timeout=300000,
                send_receive_timeout=300000
            )
            return cls.client
        except Exception as e:
            try:
                cls.client = clickhouse_connect.get_client(
                    host='ch.data.sayweee.net',
                    port=8123,
                    username='weee_qa',
                    password=r'18o38lYmxRAEchpZ',
                    database='weee_data',
                    connect_timeout=300000,
                    send_receive_timeout=300000
                )
                return cls.client
            except Exception as e:
                log.info("连接clickhouse失败: " + str(e))

    @classmethod
    def get_tb1_client(cls) -> Client:
        try:
            cls.client = clickhouse_connect.get_client(
                host='ch.tb1.sayweee.net',
                port=8123,
                username='weee_qa',
                password=r'7Vil+sjBsLDheaqNyKIHCw',
                database='weee_data'
            )
            return cls.client
        except Exception as e:
            try:
                cls.client = clickhouse_connect.get_client(
                    host='ch.tb1.sayweee.net',
                    port=8123,
                    username='weee_qa',
                    password=r'7Vil+sjBsLDheaqNyKIHCw',
                    database='weee_data'
                )
                return cls.client
            except Exception as e:
                log.info("连接clickhouse失败: " + str(e))

    # def query_track(self, sql):
    #     res = self.client.query(sql)
    #     return res


class DataTrack(weeeTest.TestCase):
    def get_track(self, begin_at, end_at):
        print("======== 开始获取tb1埋点数据 ========")
        print("1====>", self.env.get("env_url"))
        print("2====>", self.base_url)
        if "api.tb1.sayweee.net" in self.env.get("env_url") or "api.tb1.sayweee.net" in self.base_url:
            tb1_track_data = self._get_track_data_tb1_v2(begin_at, end_at)
            if isinstance(tb1_track_data.get('data'), list):
                print("获取的tb1埋点的数据长度为：", len(tb1_track_data.get('data')))
                with open("./tb1_tack_v2.json", "w", encoding='utf-8') as fp:
                    fp.write(json.dumps(tb1_track_data.get('data'), ensure_ascii=False))
                return tb1_track_data.get('data')
            else:
                raise Exception(f"没有获取tb1环境的埋点，track={tb1_track_data}")
        elif "api.sayweee.net" in self.env.get("env_url") or "api.sayweee.net" in self.base_url:
            print("======== 开始获取线上埋点数据 ========")
            online_track_data = self._get_track_data_online_v2(begin_at, end_at)
            if isinstance(online_track_data.get('data'), list):
                print("获取的线上环境埋点的数据长度为：", len(online_track_data.get('data')))
                with open("./online_tack_v2.json", "w", encoding='utf-8') as fp:
                    fp.write(json.dumps(online_track_data.get('data'), ensure_ascii=False))
                return online_track_data.get('data')
            else:
                raise Exception(f"没有获取tb1环境的埋点，track={online_track_data}")
        else:
            log.debug(f"base_url输入不正确，请检查, base_url={self.base_url}")
            raise Exception(f"base_url输入不正确，请检查, base_url={self.base_url}")

    @staticmethod
    def _get_track_data_tb1_v2(begin_at=None, end_at=None):
        where = ""
        if begin_at and end_at:
            where = f"__time>='{begin_at}' and __time<='{end_at}'"
        elif begin_at and not end_at:
            where = f"__time>='{begin_at}'"
        elif end_at and not begin_at:
            where = f"__time<='{end_at}'"
        elif not begin_at and not end_at:
            where = '2>1'
        else:
            log.debug("条件输入不正确，请检查条件")

        query = f"""
                    SELECT
                      "__time",
                      "l0_sdk_version",
                      "l0_event_id",
                      "l0_d2_validated",
                      "l0_view_id",
                      "l0_ip",
                      "l0_language",
                      "l0_event_time",
                      "l0_zipcode",
                      "l0_user_id",
                      "l0_referer_page_key",
                      "l0_os_version",
                      "l0_screen_width",
                      "l0_screen_height",
                      "l0_referer_view_id",
                      "l0_referer_page_ctx_app",
                      "l0_d2",
                      "l0_referer_page_url",
                      "l0_bu",
                      "message_id",
                      "params",
                      "l0_platform",
                      "l0_user_agent",
                      "l0_page_key",
                      "l0_page_ctx_app",
                      "l0_app_version",
                      "l0_os_language",
                      "l0_st",
                      "l0_store",
                      "l0_event_type",
                      "l0_device_id",
                      "l0_session_id",
                      "l0_page_url"
                    FROM weee_data.data_tracking_local 
                    where {where} 
                    ORDER BY __time DESC limit 5000000
                """
        print("query===>", query)
        res = HR.request(
            data={
                "path": "http://tb1-data-355373674.us-east-2.elb.amazonaws.com:8123/",
                "method": "post",
                "headers": {
                    "Content-Type": r"text/plain;charset=UTF-8",
                    "Authorization": r"Basic ZGF0YV9wbGF0Zm9ybTo3dlNpRVcyZTJkMzJwNTZN",
                    "User-Agent": r"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36 Edg/126.0.0.0"
                },
                "param": {
                    "add_http_cors_header": 1,
                    "default_format": "JSONCompact",
                    "max_result_rows": 5000000,
                    "max_result_bytes": 10000000000000000,
                    "result_overflow_mode": "break"
                },

                "request_body": query
            },
            params_type="form"
        )

        keys = ['__time', 'l0_sdk_version', 'l0_event_id', 'l0_d2_validated', 'l0_view_id', 'l0_ip', 'l0_language',
                'l0_event_time', 'l0_zipcode', 'l0_user_id', 'l0_referer_page_key', 'l0_os_version',
                'l0_screen_width',
                'l0_screen_height', 'l0_referer_view_id', 'l0_referer_page_ctx_app', 'l0_d2', 'l0_referer_page_url',
                'l0_bu', 'message_id', 'params', 'l0_platform', 'l0_user_agent', 'l0_page_key', 'l0_page_ctx_app',
                'l0_app_version', 'l0_os_language', 'l0_st', 'l0_store', 'l0_event_type', 'l0_device_id',
                'l0_session_id', 'l0_page_url']
        render_data = []
        if type(res.json()['data']) == list and res.json()['data']:
            for item in res.json()['data']:
                new_dict = dict(zip(keys, item))
                render_data.append(new_dict)
            return {
                "code": 200,
                "data": render_data
            }
        else:
            return {
                "code": 500,
                "data": "未获取到线上埋点数据"
            }

    @staticmethod
    def _get_track_data_online_v2(begin_at, end_at):
        where = ""
        if begin_at and end_at:
            where = f"__time>='{begin_at}' and __time<='{end_at}'"
        elif begin_at and not end_at:
            where = f"__time>='{begin_at}'"
        elif end_at and not begin_at:
            where = f"__time<='{end_at}'"
        elif not begin_at and not end_at:
            where = '2>1'
        else:
            log.debug("条件输入不正确，请检查条件")
        query = f"""
                    SELECT
                      "__time",
                      "l0_sdk_version",
                      "l0_event_id",
                      "l0_d2_validated",
                      "l0_view_id",
                      "l0_ip",
                      "l0_language",
                      "l0_event_time",
                      "l0_zipcode",
                      "l0_user_id",
                      "l0_referer_page_key",
                      "l0_os_version",
                      "l0_screen_width",
                      "l0_screen_height",
                      "l0_referer_view_id",
                      "l0_referer_page_ctx_app",
                      "l0_d2",
                      "l0_referer_page_url",
                      "l0_bu",
                      "message_id",
                      "params",
                      "l0_platform",
                      "l0_user_agent",
                      "l0_page_key",
                      "l0_page_ctx_app",
                      "l0_app_version",
                      "l0_os_language",
                      "l0_st",
                      "l0_store",
                      "l0_event_type",
                      "l0_device_id",
                      "l0_session_id",
                      "l0_page_url"
                    FROM weee_data.data_tracking_local 
                    where {where} 
                    ORDER BY __time DESC limit 800
                """
        res = HR.request(
            data={
                "path": "http://clickhouse-http-endpoint-1303890441.us-west-2.elb.amazonaws.com:8123/",
                "method": "post",
                "headers": {
                    "Content-Type": r"text/plain;charset=UTF-8",
                    "Authorization": r"Basic ZGF0YV9wbGF0Zm9ybTo3dlNpRVcyZTJkMzJwNTZN"
                },
                "param": {
                    "add_http_cors_header": 1,
                    "default_format": "JSONCompact",
                    "max_result_rows": 50000000,
                    "max_result_bytes": 10000000000000000,
                    "result_overflow_mode": "break"
                },

                "request_body": query
            },
            params_type="form"
        )

        keys = ['__time', 'l0_sdk_version', 'l0_event_id', 'l0_d2_validated', 'l0_view_id', 'l0_ip', 'l0_language',
                'l0_event_time', 'l0_zipcode', 'l0_user_id', 'l0_referer_page_key', 'l0_os_version',
                'l0_screen_width',
                'l0_screen_height', 'l0_referer_view_id', 'l0_referer_page_ctx_app', 'l0_d2', 'l0_referer_page_url',
                'l0_bu', 'message_id', 'params', 'l0_platform', 'l0_user_agent', 'l0_page_key', 'l0_page_ctx_app',
                'l0_app_version', 'l0_os_language', 'l0_st', 'l0_store', 'l0_event_type', 'l0_device_id',
                'l0_session_id', 'l0_page_url']
        render_data = []
        if type(res.json()['data']) == list and res.json()['data']:
            for item in res.json()['data']:
                new_dict = dict(zip(keys, item))
                render_data.append(new_dict)
            return {
                "code": 200,
                "data": render_data
            }
        else:
            return {
                "code": 500,
                "data": "未获取到线上埋点数据"
            }
