import ast
import os
from typing import List, Dict

from test_dir.api_case.ec.datatrack.trackV3.case_generator.track_case import TrackCase
from test_dir.api_case.ec.datatrack.trackV3.read_excel.read_excel_file import ReadCsv


class T2ProdImpCase(TrackCase):
    def __init__(self, data):
        super().__init__(data)

    def case_data_compose(self) -> List[Dict]:
        """
        self.data[0]: 真实数据
        self.data[1]: 表的列名：case_name, page_key, mod_nm等
        """
        new_list_mapping = []
        for index, item in enumerate(self.data[0]):
            if index == 0:
                continue
            new_list = list(zip(self.data[0][0], item))
            new_dict = dict(zip(self.data[1], new_list))
            new_list_mapping.append(new_dict)
        return new_list_mapping

    def case_generate(self):
        """
        为每条case组装sql语句
        """
        # 每个event type需要单独处理，统一处理难度较大，主要问题是参数与断言是否属于params，或者属于params的哪个层级
        list_data = self.case_data_compose()
        case_names = []
        assertions = []
        amount = None
        sqls = []
        platform_page_key = []
        owners = []
        case_ids = []
        # 这里不好统一，每个event_type都不一样，所以这个方法不能放到基类中，每个event_type一个base_sql
        # base_sql = """ select JSONExtractRaw(params, 'mod_nm') as mod_nm, JSONExtractRaw(params, 'sec_nm') as sec_nm,JSONExtractRaw(JSONExtractRaw(params, 'co'), 'target_nm') as target_nm, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'target_type') as target_type, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'click_type') as click_type from weee_data.data_tracking_all where __time>='{0}' and __time<='{1}' and l0_platform='{2}' and l0_page_key='{3}' and sec_nm='"{4}"' and click_type='"{5}"' and """
        base_sql = """ select JSONExtractRaw(params, 'mod_nm') as mod_nm, JSONExtractRaw(params, 'sec_nm') as sec_nm,JSONExtractRaw(JSONExtractRaw(params, 'co'), 'prod_id') as prod_id, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'prod_name') as prod_name, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'sold_status') as sold_status, JSONExtractRaw(JSONExtractRaw(params, 'ctx'), 'page_target') as page_target, JSONExtractRaw(JSONExtractRaw(params, 'ctx'), 'global_vendor') as global_vendor, JSONExtractRaw(JSONExtractRaw(params, 'ctx'), 'trace_id') as trace_id from weee_data.data_tracking_all where __time>='{0}' and __time<='{1}' and l0_platform='{2}' and l0_page_key='{3}' and l0_event_type='t2_prod_imp' and """
        for i in list_data:
            if i.get("case_name")[1] == 'blank' or i.get("module")[1].startswith("#"):
                continue

            where = []
            _priority = ast.literal_eval(os.getenv("priority") or '["P1", "P2", "P3"]')

            if i.get("priority")[1] in _priority:
                for k, v in i.items():
                    # 难点在于处理sec_nm, click_type这种既有普通字符串，又有列表的情况 -- 目前除了platform之外，不允许写列表
                    # 字段值只有5中情况： 1. 普通值 2. null 3. not null 4. 包含?的值用like匹配且?必须写在字段末尾 5. blank（不参与匹配）
                    if v[0] in ["mod_nm", "sec_nm", "prod_id", "prod_name", "sold_status", "page_target", "global_vendor", "trace_id"] and v[1] != 'blank':
                        if v[1] == 'null':
                            where.append(f"{v[0]}='null'")
                        elif v[1] == "not null":
                            where.append(f"""{v[0]}!='null' and {v[0]}!='""'""")
                        elif "?" in v[1]:
                            where.append(f"""{v[0]} like '%"{v[1][:-2]}%'""")
                        elif "?" not in v[1]:
                            where.append(f"""{v[0]}='"{v[1]}"'""")

                    elif v[0] == 'assertion':
                        # 逻辑有变动，现在只考虑数量，把以前的断言条件改为查询条件
                        try:
                            amount = int(v[0])
                        except Exception as e:
                            print("assert列没有填写正确的数量或填写的不是数字" + str(e))
                            amount = 1

                case_names.append(i.get("case_name")[1])
                if where:
                    # 有条件语句时
                    # sqls.append(base_sql + " and ".join(where) + " limit 10")
                    sqls.append(base_sql + " and ".join(where))
                else:
                    # sqls.append(base_sql[:-4] + " limit 10")
                    sqls.append(base_sql[:-4])
                assertions.append(amount)

                # 处理page_name和page_key
                print("+++++++===>", i.get("case_name")[1])
                page_key = ast.literal_eval(i.get("page_key")[1])
                if page_key:
                    pk_new = [(item[0], item[1] + i.get("page_name")[1]) for item in page_key]
                else:
                    raise Exception("没有标注page_key,请检查文档")
                platform_page_key.append(pk_new)
                owners.append(i.get("owner")[1])
                case_ids.append(i.get("case_id")[1])

        if len(sqls) == len(assertions) == len(platform_page_key) == len(case_names):
            all_click_type_data = list(zip(case_names, sqls, platform_page_key, assertions, owners, case_ids))
            return {
                "code": "0000",
                "sql" : sqls,
                "platform_page_key": platform_page_key,
                "assertions": assertions,
                "assertions_sqls": list(zip(sqls, assertions)),
                "all": all_click_type_data
            }
        else:
            return {
                "code": "9999",
                "message": "返回数据错误, excel表格的数据不一致"
           }

class T2CartImpCase(TrackCase):
    def __init__(self, data):
        super().__init__(data)

    def case_data_compose(self) -> List[Dict]:
        """
        self.data[0]: 真实数据
        self.data[1]: 表的列名：case_name, page_key, mod_nm等
        """
        new_list_mapping = []
        for index, item in enumerate(self.data[0]):
            if index == 0:
                continue
            new_list = list(zip(self.data[0][0], item))
            new_dict = dict(zip(self.data[1], new_list))
            new_list_mapping.append(new_dict)
        return new_list_mapping

    def case_generate(self):
        """
        为每条case组装sql语句
        """
        # 每个event type需要单独处理，统一处理难度较大，主要问题是参数与断言是否属于params，或者属于params的哪个层级
        list_data = self.case_data_compose()
        case_names = []
        assertions = []
        amount = None
        sqls = []
        platform_page_key = []
        owners = []
        case_ids = []
        base_sql = """ select JSONExtractRaw(params, 'mod_nm') as mod_nm, JSONExtractRaw(params, 'sec_nm') as sec_nm,JSONExtractRaw(JSONExtractRaw(params, 'co'), 'items') as co_items, JSONExtractRaw(JSONExtractRaw(JSONExtractRaw(params, 'co'), 'items'), 'product_id') as product_id, JSONExtractRaw(JSONExtractRaw(JSONExtractRaw(params, 'co'), 'items'), 'quantity') as quantity, JSONExtractRaw(JSONExtractRaw(JSONExtractRaw(params, 'co'), 'items'), 'source') as source, JSONExtractRaw(JSONExtractRaw(JSONExtractRaw(params, 'co'), 'items'), 'price') as price, JSONExtractRaw(JSONExtractRaw(JSONExtractRaw(params, 'co'), 'items'), 'status') as items_status, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'deal_id') as deal_id, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'delivery_mode') as delivery_mode, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'type') as co_type from weee_data.data_tracking_all where __time>='{0}' and __time<='{1}' and l0_platform='{2}' and l0_page_key='{3}' and l0_event_type='t2_cart_imp' and """
        for i in list_data:
            if i.get("case_name")[1] == 'blank' or i.get("module")[1].startswith("#"):
                continue

            where = []
            _priority = ast.literal_eval(os.getenv("priority") or '["P1", "P2", "P3"]')

            if i.get("priority")[1] in _priority:
                for k, v in i.items():
                    # 难点在于处理sec_nm, click_type这种既有普通字符串，又有列表的情况 -- 目前除了platform之外，不允许写列表
                    # 字段值只有5中情况： 1. 普通值 2. null 3. not null 4. 包含?的值用like匹配且?必须写在字段末尾 5. blank（不参与匹配）
                    if v[0] in ["mod_nm", "sec_nm", "co_items", "product_id", "quantity", "source", "price", "items_status", "deal_id", "delivery_mode", "co_type"] and v[1] != 'blank':
                        if v[1] == 'null':
                            where.append(f"{v[0]}='null'")
                        elif v[1] == "not null":
                            where.append(f"""{v[0]}!='null' and {v[0]}!='""'""")
                        elif "?" in v[1]:
                            where.append(f"""{v[0]} like '%"{v[1][:-2]}%'""")
                        elif "?" not in v[1]:
                            where.append(f"""{v[0]}='"{v[1]}"'""")

                    elif v[0] == 'assertion':
                        # 逻辑有变动，现在只考虑数量，把以前的断言条件改为查询条件
                        try:
                            amount = int(v[0])
                        except Exception as e:
                            print("assert列没有填写正确的数量或填写的不是数字" + str(e))
                            amount = 1

                case_names.append(i.get("case_name")[1])
                if where:
                    # 有条件语句时
                    # sqls.append(base_sql + " and ".join(where) + " limit 10")
                    sqls.append(base_sql + " and ".join(where))
                else:
                    # sqls.append(base_sql[:-4] + " limit 10")
                    sqls.append(base_sql[:-4])
                assertions.append(amount)

                # 处理page_name和page_key
                print("+++++++===>", i.get("case_name")[1])
                page_key = ast.literal_eval(i.get("page_key")[1])
                if page_key:
                    pk_new = [(item[0], item[1] + i.get("page_name")[1]) for item in page_key]
                else:
                    raise Exception("没有标注page_key,请检查文档")
                platform_page_key.append(pk_new)
                owners.append(i.get("owner")[1])
                case_ids.append(i.get("case_id")[1])

        if len(sqls) == len(assertions) == len(platform_page_key) == len(case_names):
            all_click_type_data = list(zip(case_names, sqls, platform_page_key, assertions, owners, case_ids))
            return {
                "code": "0000",
                "sql" : sqls,
                "platform_page_key": platform_page_key,
                "assertions": assertions,
                "assertions_sqls": list(zip(sqls, assertions)),
                "all": all_click_type_data
            }
        else:
            return {
                "code": "9999",
                "message": "返回数据错误, excel表格的数据不一致"
           }

class T2ClickActionCase(TrackCase):
    def __init__(self, data):
        super().__init__(data)

    def case_data_compose(self) -> List[Dict]:
        """
        self.data[0]: 真实数据
        self.data[1]: 表的列名：case_name, page_key, mod_nm等
        """
        new_list_mapping = []
        for index, item in enumerate(self.data[0]):
            if index == 0:
                continue
            new_list = list(zip(self.data[0][0], item))
            new_dict = dict(zip(self.data[1], new_list))
            new_list_mapping.append(new_dict)
        return new_list_mapping

    def case_generate(self):
        """
        为每条case组装sql语句
        """
        # 每个event type需要单独处理，统一处理难度较大，主要问题是参数与断言是否属于params，或者属于params的哪个层级
        list_data = self.case_data_compose()
        case_names = []
        assertions = []
        amount = None
        sqls = []
        platform_page_key = []
        owners = []
        case_ids = []
        # 这里不好统一，每个event_type都不一样，所以这个方法不能放到基类中，每个event_type一个base_sql
        # base_sql = """ select JSONExtractRaw(params, 'mod_nm') as mod_nm, JSONExtractRaw(params, 'sec_nm') as sec_nm,JSONExtractRaw(JSONExtractRaw(params, 'co'), 'target_nm') as target_nm, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'target_type') as target_type, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'click_type') as click_type from weee_data.data_tracking_all where __time>='{0}' and __time<='{1}' and l0_platform='{2}' and l0_page_key='{3}' and sec_nm='"{4}"' and click_type='"{5}"' and """
        base_sql = """ select JSONExtractRaw(params, 'mod_nm') as mod_nm, JSONExtractRaw(params, 'sec_nm') as sec_nm,JSONExtractRaw(JSONExtractRaw(params, 'co'), 'target_nm') as target_nm, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'target_type') as target_type, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'click_type') as click_type, JSONExtractRaw(JSONExtractRaw(params, 'ctx'), 'page_target') as page_target, JSONExtractRaw(JSONExtractRaw(params, 'ctx'), 'trace_id') as trace_id from weee_data.data_tracking_all where __time>='{0}' and __time<='{1}' and l0_platform='{2}' and l0_page_key='{3}' and l0_event_type='t2_click_action' and """
        for i in list_data:
            if i.get("case_name")[1] == 'blank' or i.get("module")[1].startswith("#"):
                continue

            where = []
            _priority = ast.literal_eval(os.getenv("priority") or '["P1", "P2", "P3"]')

            if i.get("priority")[1] in _priority:
                for k, v in i.items():
                    # 难点在于处理sec_nm, click_type这种既有普通字符串，又有列表的情况
                    if v[0] in ["mod_nm", "sec_nm", "target_nm", "target_type", "click_type", "page_target", "trace_id"] and v[1] != 'blank':
                        if v[1] == 'null':
                            where.append(f"{v[0]}='null'")
                        elif v[1] == "not null":
                            where.append(f"""{v[0]}!='null' and {v[0]}!='""'""")
                        elif "?" in v[1]:
                            where.append(f"""{v[0]} like '%"{v[1][:-2]}%'""")
                        elif "?" not in v[1]:
                            where.append(f"""{v[0]}='"{v[1]}"'""")

                    elif v[0] == 'assertion':
                        # 逻辑有变动，现在只考虑数量，把以前的断言条件改为查询条件
                        try:
                            amount = int(v[0])
                        except Exception as e:
                            print("assert列没有填写正确的数量或填写的不是数字" + str(e))
                            amount = 1

                case_names.append(i.get("case_name")[1])
                if where:
                    # 有条件语句时
                    # sqls.append(base_sql + " and ".join(where) + " limit 10")
                    sqls.append(base_sql + " and ".join(where))
                else:
                    # sqls.append(base_sql[:-4] + " limit 10")
                    sqls.append(base_sql[:-4])
                assertions.append(amount)

                # 处理page_name和page_key
                print("+++++++===>", i.get("case_name")[1])
                page_key = ast.literal_eval(i.get("page_key")[1])
                if page_key:
                    pk_new = [(item[0], item[1] + i.get("page_name")[1]) for item in page_key]
                else:
                    raise Exception("没有标注page_key,请检查文档")
                platform_page_key.append(pk_new)
                owners.append(i.get("owner")[1])
                case_ids.append(i.get("case_id")[1])

        if len(sqls) == len(assertions) == len(platform_page_key) == len(case_names):
            all_click_type_data = list(zip(case_names, sqls, platform_page_key, assertions, owners, case_ids))
            return {
                "code": "0000",
                "sql" : sqls,
                "platform_page_key": platform_page_key,
                "assertions": assertions,
                "assertions_sqls": list(zip(sqls, assertions)),
                "all": all_click_type_data
            }
        else:
            return {
                "code": "9999",
                "message": "返回数据错误, excel表格的数据不一致"
           }

class T2CartActionCase(TrackCase):
    def __init__(self, data):
        super().__init__(data)

    def case_data_compose(self) -> List[Dict]:
        """
        self.data[0]: 真实数据
        self.data[1]: 表的列名：case_name, page_key, mod_nm等
        """
        new_list_mapping = []
        for index, item in enumerate(self.data[0]):
            if index == 0:
                continue
            new_list = list(zip(self.data[0][0], item))
            new_dict = dict(zip(self.data[1], new_list))
            new_list_mapping.append(new_dict)
        return new_list_mapping

    def case_generate(self):
        """
        为每条case组装sql语句
        """
        # 每个event type需要单独处理，统一处理难度较大，主要问题是参数与断言是否属于params，或者属于params的哪个层级
        list_data = self.case_data_compose()
        case_names = []
        assertions = []
        amount = None
        sqls = []
        platform_page_key = []
        owners = []
        case_ids = []
        # 这里不好统一，每个event_type都不一样，所以这个方法不能放到基类中，每个event_type一个base_sql
        base_sql = """ select JSONExtractRaw(params, 'mod_nm') as mod_nm, JSONExtractRaw(params, 'sec_nm') as sec_nm, JSONExtractRaw(JSONExtractRaw(params, 'ctx'), 'page_target') as page_target, JSONExtractRaw(JSONExtractRaw(params, 'ctx'), 'relate_info') as relate_info, JSONExtractRaw(JSONExtractRaw(params, 'ctx'), 'filter_sub_category') as filter_sub_category  from weee_data.data_tracking_all where __time>='{0}' and __time<='{1}' and l0_platform='{2}' and l0_page_key='{3}' and l0_event_type='t2_cart_action' and """
        for i in list_data:
            # 处理空行
            if i.get("case_name")[1] == 'blank' or i.get("module")[1].startswith("#"):
                continue
            where = []

            # 判断优化级
            _priority = ast.literal_eval(os.getenv("priority") or '["P1"]')
            if i.get("priority")[1] in _priority:
                for k, v in i.items():
                    if v[0] in ["mod_nm", "sec_nm", "page_target", "relate_info", "filter_sub_category"] and v[1] != 'blank':
                        if v[1] == 'null':
                            where.append(f"{v[0]}='null'")
                        elif v[1] == "not null":
                            where.append(f"""{v[0]}!='null' and {v[0]}!='""' """)
                        elif "?" in v[1]:
                            # 后面1个%前面不能加引号，否则匹配不到
                            where.append(f"""{v[0]} like '%"{v[1][:-2]}%'""")
                        elif "?" not in v[1]:
                            where.append(f"""{v[0]}='"{v[1]}"'""")

                    elif v[0] == 'assertion':
                        try:
                            amount = int(v[1])
                        except Exception as e:
                            print("assert列没有填写正确的数量或填写的不是数字" + str(e))
                            amount = 1

                case_names.append(i.get("case_name")[1])
                if where:
                    # 有条件语句时
                    # sqls.append(base_sql + " and ".join(where) + " limit 10")
                    sqls.append(base_sql + " and ".join(where))
                else:
                    # sqls.append(base_sql[:-4] + " limit 10")
                    sqls.append(base_sql[:-4])

                assertions.append(amount)
                # 处理page_name和page_key
                page_key = ast.literal_eval(i.get("page_key")[1])
                pk_new=[]
                if page_key:
                    pk_new = [(item[0], item[1] + i.get("page_name")[1]) for item in page_key]
                else:
                    raise Exception("没有标注page_key,请检查文档")
                platform_page_key.append(pk_new)
                owners.append(i.get("owner")[1])
                case_ids.append(i.get("case_id")[1])

        if len(sqls) == len(assertions) == len(platform_page_key) == len(case_names):
            return {
                "code": "0000",
                "sql" : sqls,
                "platform_page_key": platform_page_key,
                "assertions": assertions,
                "assertions_sqls": list(zip(sqls, assertions)),
                "all": list(zip(case_names, sqls, assertions, platform_page_key, owners, case_ids))
            }
        else:
            return {
                "code": "9999",
                "message": "返回数据错误, excel表格的数据不一致"
           }

class PageView(TrackCase):
    def __init__(self, data):
        super().__init__(data)

    def case_data_compose(self) -> List[Dict]:
        """
        self.data[0]: 真实数据
        self.data[1]: 表的列名：case_name, page_key, mod_nm等
        """
        new_list_mapping = []
        for index, item in enumerate(self.data[0]):
            if index == 0:
                continue
            # new_list = list(zip(item, self.data[0][0]))
            new_list = list(zip(self.data[0][0], item))
            new_dict = dict(zip(self.data[1], new_list))
            new_list_mapping.append(new_dict)
        return new_list_mapping

    def case_generate(self):
        """
        为每条case组装sql语句
        """
        # 每个event type需要单独处理，统一处理难度较大，主要问题是参数与断言是否属于params，或者属于params的哪个层级
        list_data = self.case_data_compose()
        case_names = []
        assertions = []
        amount = None
        sqls = []
        platform_page_key = []
        owners = []
        case_ids = []
        # 这里不好统一，每个event_type都不一样，所以这个方法不能放到基类中，每个event_type一个base_sql
        base_sql = """ select JSONExtractRaw(JSONExtractRaw(params, 'ctx'), 'page_target') as page_target, JSONExtractRaw(JSONExtractRaw(params, 'ctx'), 'trace_id') as trace_id from weee_data.data_tracking_all where __time>='{0}' and __time<='{1}' and l0_platform='{2}' and l0_page_key='{3}' and l0_event_type='page_view' and """
        for index, i in enumerate(list_data):
            # 处理空行
            if i.get("case_name")[1] == 'blank' or i.get("module")[1].startswith("#"):
                continue
            where = []

            _priority = ast.literal_eval(os.getenv("priority") or '["P1", "P2", "P3"]')
            if i.get("priority")[1] in _priority:
                for k, v in i.items():
                    if v[0] in ["page_target", "trace_id"] and v[1] != 'blank':
                        if v[1] != 'null' and v[1] != 'not null':
                            where.append(f"""{v[0]}='"{v[1]}"'""")
                        elif v[1] == 'null':
                            where.append(f"{v[0]}='null'")
                        elif v[1] == 'not null':
                            where.append(f"{v[0]}!='null' and {v[0]}!='""'")
                    elif v[0] == 'assertion':
                        try:
                            amount = int(v[1])
                        except Exception as e:
                            print("assert列没有填写正确的数量或填写的不是数字" + str(e))
                            amount = 1

                case_names.append(i.get("case_name")[1])
                if where:
                    # 有条件语句时
                    # sqls.append(base_sql + " and ".join(where) + " limit 10")
                    sqls.append(base_sql + " and ".join(where))
                else:
                    # sqls.append(base_sql[:-4] + " limit 10")
                    sqls.append(base_sql[:-4])

                assertions.append(amount)
                # 处理page_name和page_key
                page_key = ast.literal_eval(i.get("page_key")[1])
                pk_new=[]
                if page_key:
                    pk_new = [(item[0], item[1] + i.get("page_name")[1]) for item in page_key]
                else:
                    raise Exception("没有标注page_key,请检查文档")
                platform_page_key.append(pk_new)
                owners.append(i.get("owner")[1])
                case_ids.append(i.get("case_id")[1])


        if len(sqls) == len(assertions) == len(platform_page_key) == len(case_names):
            return {
                "code": "0000",
                "sql" : sqls,
                "platform_page_key": platform_page_key,
                "assertions": assertions,
                "assertions_sqls": list(zip(sqls, assertions)),
                "all": list(zip(case_names, sqls, assertions, platform_page_key, owners, case_ids))
            }
        else:
            return {
                "code": "9999",
                "message": "返回数据错误, excel表格的数据不一致"
           }


def generate_case_data(track_case_class: TrackCase):
    r1 = track_case_class.case_generate()
    return r1




if __name__ == '__main__':
    r = ReadCsv("t2_click_action.csv").read()
    t_click = T2ClickActionCase(r)
    r1 = generate_case_data(t_click)
    print(r1)

    # r = ReadExcel("t2_cart_action").read()
    # t_click = T2CartActionCase(r)
    # r1 = generate_case_data(t_click)

    # r = ReadCsv("page_view.csv").read()
    # page_view = PageView(r)
    # r1 = generate_case_data(page_view)
    # print(r1)
