# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
sales portal MKPL 促销模块
"""
from datetime import datetime
import time

import weeeTest
from test_dir.api.ec.central_portal.central_mkpl import Promotions, SellerMgmt
from weeeTest import weeeConfig, jmespath
import pytz
from datetime import datetime, timedelta
from test_dir.api.ec.central_portal.central_mkpl import ProductsMgmt, Filters, Promotions
from . import sales_header


class TestPromotionList(weeeTest.TestCase):
    """
    促销列表
    """

    @weeeTest.mark.list('sales', 'Transaction', 'MKPL', 'test_promotions')
    def test_sales_promotion_list(self):
        """
        Sales MKPL促销管理-Promotion list-查询Promotion list页面数据
        """

        res = Promotions().admin_promotion_page(headers=sales_header)
        print(res)
        assert res['result'], f"The result of admin_promotion_page should be True. res={res}"

        pros = jmespath(res, "object.data")
        all_promotion_valid = all(pro.get('biz_type') == 'seller' for pro in pros)
        assert all_promotion_valid, "The field obtained by the list is not  'seller'"

        current_time = time.time()    # 当前时间的时间戳

        # status=30判断当前时间的pdt时间在这个"start_time"和"end_time"范围内

        for pro in pros:
            status = pro.get('status')
            start_time = pro.get('start_time')
            end_time = pro.get('end_time')

            if status == 30:
                assert start_time <= current_time <= end_time, f"当前时间不在促销活动 {pro.get('ps_title')} 的有效时间范围内"

    @weeeTest.mark.list('sales', 'Transaction', 'MKPL', 'test_promotions')
    def test_sales_promotion_detail(self):
        """
        Sales MKPL 促销管理-Promotion detail
        """

        res = Promotions().mkpl_admin_promotion_detail(headers=sales_header, ps_id=10015)
        # 断言object返回结果为Ture

        assert res['result'], f"The result of mkpl_admin_promotion_detail should be True. res={res}"
        # 断言biz_type为'seller'
        assert res.get('object', {}).get('biz_type') == 'seller', "The 'biz_type' field should be 'seller'"
        # 断言object返回的ps_id不为空
        assert res.get('object', {}).get('ps_id') is not None, "The 'ps_id' field should not be None"

    @weeeTest.mark.list('sales', 'Transaction', 'MKPL', 'test_promotions')
    def test_admin_vendors_discounts(self):
        """
        Sales MKPL获取促销管理-Promotion discount Mgmt 列表页
        """

        res = Promotions().admin_global_plus_discounts(headers=sales_header)
        # 断言object返回结果为Ture
        assert res['result'], f"The result of mkpl_admin_promotion_detail should be True. res={res}"

        # 断言"status"为 "A"的时间要在"start_time"和"end_time"内
        # jmespath(res, "object.data")
        current_time = time.time()  # 当前时间的时间戳

        for discounts_status in res.get('object', {}).get('data', []):
            if discounts_status.get('status') == 'A':
                start_time = discounts_status.get('start_time')
                end_time = discounts_status.get('end_time')
                assert start_time <= current_time <= end_time, f"当前时间不在活动 {discounts_status.get('title')} 的有效时间范围内"

    @weeeTest.mark.list('sales', 'Transaction', 'MKPL', 'test_promotions')
    def test_promotion_coupon_page(self):
        """
        Sales MKPL获取促销管理-Promotions Coupon Mgnmt 列表页
        """

        res = Promotions().promotion_coupon_page(headers=sales_header)
        # jmespath(res, "object.data")
        # 断言返回的biz_type为mkpl
        for coupon_info in res.get('object', {}).get('result', []):
            assert coupon_info.get('biz_type') == 'mkpl', "The 'biz_type' field should be 'mkpl'"

            """
            断言benefit_type="Percentage Discount off"时type="Z"，
            benefit_type="Money Value off"时type="D"，
            benefit_type="Shipping Fee off"时，status=F
            """

            if coupon_info.get('benefit_type') == "Percentage Discount off":
                assert coupon_info.get(
                    'type') == 'Z', "The 'type' field should be 'Z' when benefit_type is 'Percentage Discount off'"
            elif coupon_info.get('benefit_type') == "Money Value off":
                assert coupon_info.get(
                    'type') == 'D', "The 'type' field should be 'D' when benefit_type is 'Money Value off'"
            elif coupon_info.get('benefit_type') == "Shipping Fee off":
                assert coupon_info.get(
                    'type') == 'F', "The 'type' field should be 'F' when benefit_type is 'Shipping Fee off'"

    @weeeTest.mark.list('sales', 'Transaction', 'MKPL', 'test_promotions')
    def test_admin_campaigns_global_plus(self):
        """
        查看Global+ Campaign list
        """
        campaign_type1 = jmespath(Filters().admin_campaign_types(headers=sales_header, biz_type='seller'),
                                  'object[0].id')
        vendor_id = jmespath(Filters().vendor_market_list(headers=sales_header, biz_type='seller'), 'object[0].id')
        job = Promotions().job_campaigns_process(headers=sales_header)
        # 断言 job 执行成功，否则admin_campaigns case断言可能失败，因为campaign状态不是最新的，tb1需要手动执行job
        assert job['result'] is True, "The job did not execute successfully; the result is not True."
        assert job[
                   'object'] == "success", f"The job did not execute successfully; the object is not 'success', but {job['object']}."

        res1 = Promotions().admin_campaigns(headers=sales_header, biz_type="seller", seller_id=vendor_id)
        # 断言biz_type为"seller"，判断这是Global+的campaign，并检查提报的submissions属于这个商家
        for campaign in res1['object']:
            assert campaign['biz_type'] == "seller", "biz_type is not seller"
            if campaign['submissions']:
                for submission in campaign['submissions']:
                    if submission['seller_id'] == vendor_id:
                        print(
                            f"ID: {campaign['id']}, Name: {campaign['name']}, Submission ID: {submission['submission_id']}")
                        # 获取campaign_id
                        campaign_id = campaign['id']
                        # 调用admin_campaign_detail方法，获得campaign详情页的信息
                        res_detail = Promotions().admin_campaign_detail(headers=sales_header, campaign_id=campaign_id)
                        assert res_detail[
                                   'result'] is True, "The res_detail did not execute successfully; the result is not True."
                        # 检查is_public是否为false，这个campaign是否是private,部分商家可参与
                        if res_detail['object']['is_public'] is False:
                            # 断言vendor_id存在于res_detail['object']['seller_ids']中，提报活动的商家属于campaign可参与商家范围内
                            assert vendor_id in res_detail['object'][
                                'seller_ids'], f"vendor_id {vendor_id} is NOT present in campaign_id {campaign_id} when is_public is false"
                            print(
                                f"vendor_id {vendor_id} is present in campaign_id {campaign_id} and is_public is false")

        res2 = Promotions().admin_campaigns(headers=sales_header, biz_type="seller", campaign_type=campaign_type1)
        # 断言campaign_type_id与campaign_type1一致，并且biz_type为"seller";判断返回的活动类型与filter一致
        for campaign in res2['object']:
            assert campaign['campaign_type_id'] == campaign_type1, "campaign_type_id does not match campaign_type1"
            assert campaign['biz_type'] == "seller", "biz_type is not seller"

        # 设置洛杉矶的时区
        la_tz = pytz.timezone('America/Los_Angeles')
        # 获取当前的UTC时间，并转换为洛杉矶的当地时间
        current_time_la = datetime.now(tz=pytz.UTC).astimezone(la_tz)
        # 获取当前洛杉矶时间的时间戳
        current_timestamp_la = current_time_la.timestamp()
        res3 = Promotions().admin_campaigns(headers=sales_header, biz_type="seller", status_list=["S", "E", "A"])
        # 断言当前时间在submission_start和campaign_end之间，在Ongoing tab下，活动开始报名且活动未结束
        for campaign in res3['object']:
            assert campaign['submission_start'] <= current_timestamp_la <= campaign[
                'campaign_end'], f"Current time is not between{campaign['id']} submission_start and campaign_end"

        res4 = Promotions().admin_campaigns(headers=sales_header, biz_type="seller", status_list="P")
        # 断言当前时间小于每个活动的submission_start，在Future Campaign tab下，活动未开始报名
        for campaign in res4['object']:
            assert current_timestamp_la < campaign['submission_start'], "Current time is not less than submission_start"
        # 断言当前时间大于每个活动的campaign_end，在Ended tab下，活动已经结束
        res5 = Promotions().admin_campaigns(headers=sales_header, biz_type="seller", status_list="F")
        for campaign in res5['object']:
            assert current_timestamp_la > campaign['campaign_end'], "Current time is not greater than campaign_end"

        res6 = Promotions().admin_campaigns(headers=sales_header, biz_type="seller", status_list="D")
        assert res6['result'] == True, "The result of admin campaigns should be True."
        res7 = Promotions().admin_campaigns(headers=sales_header, biz_type="seller", status_list="R")
        # 检查submissions是否为Null，如果不为null则检查review_status是否为'R'; 被删除的活动不能存在报名中的submissions提报
        for campaign in res7['object']:
            if campaign['submissions'] is not None:
                for submission in campaign['submissions']:
                    assert submission[
                               'review_status'] == 'R', f"Found abnormal data: review_status is not 'R' for submission_id {submission['submission_id']}"
        res_calendar = Promotions().admin_campaigns_calendar(headers=sales_header, biz_type="seller")
        assert res_calendar['result'] == True, "The result of admin campaigns should be True."

    @weeeTest.mark.list('sales', 'Transaction', 'MKPL', 'test_promotions')
    def test_admin_campaigns_submissions_global_plus(self):
        """
        查看Global+ Campaign detail的submissions信息
        """

        res0 = Promotions().admin_campaigns(headers=sales_header, biz_type="seller", status_list=["S", "E", "A"])
        # 使用断言确保res0中有返回的数据
        assert res0, "未能获取有效的campaigns数据。"
        assert 'result' in res0 and res0['result'], "返回的数据中result字段不为True。"
        assert 'object' in res0, "返回的数据中没有object字段。"

        response_json = res0

        # 遍历返回结构中的每个campaign
        for campaign in response_json['object']:
            campaign_id = campaign['id']
            approved_count = campaign.get("approved_seller_count", 0)
            pending_count = campaign.get("pending_seller_count", 0)
            rejected_count = campaign.get("rejected_seller_count", 0)
            res2 = Promotions().submissions_status_count(headers=sales_header, campaign_id=campaign_id)

            # 检查approved_count是否不为零，有审核通过的提报
            if approved_count != 0:
                res1 = Promotions().admin_campaign_submissions(headers=sales_header, campaign_id=campaign_id,
                                                               review_status="A")
                assert res1 and res1.get("result"), f"获取活动ID {campaign_id} 提交的submission失败"
                assert len(
                    res1.get("object", [])) == approved_count, f"活动ID {campaign_id} 的approved submission计数不匹配"
                # 确保res2包含object键，并且它3个元素,三个tab
                assert res2.get("object") and len(
                    res2.get("object")) == 3, f"活动ID {campaign_id} 的状态 In Review，Approved，Rejected tab不完整"
                # 断言第二个元素的count字段等于approved_count
                assert res2.get("object")[1]["count"] == approved_count, f"活动ID {campaign_id} 的批准提交计数不匹配"

            # 检查pending_count是否不为零，有待审核的提报
            if pending_count != 0:
                res1 = Promotions().admin_campaign_submissions(headers=sales_header, campaign_id=campaign_id,
                                                               review_status="I")
                assert res1 and res1.get("result"), f"获取活动ID {campaign_id} 的待审核提交失败"
                assert len(res1.get("object", [])) == pending_count, f"活动ID {campaign_id} 的待审核提交计数不匹配"
                # 确保res2包含object键，并且它3个元素,三个tab
                assert res2.get("object") and len(res2.get("object")) == 3, f"活动ID {campaign_id} 的状态计数列表不足"
                # 断言第二个元素的count字段等于approved_count
                assert res2.get("object")[0]["count"] == pending_count, f"活动ID {campaign_id} 的批准提交计数不匹配"

            # 检查rejected_count是否不为零
            if rejected_count != 0:
                res1 = Promotions().admin_campaign_submissions(headers=sales_header, campaign_id=campaign_id,
                                                               review_status="R")
                assert res1 and res1.get("result"), f"获取活动ID {campaign_id} 的拒绝提交失败"
                assert len(res1.get("object", [])) == rejected_count, f"活动ID {campaign_id} 的拒绝提交计数不匹配"
                # 确保res2包含object键，并且它3个元素,三个tab
                assert res2.get("object") and len(res2.get("object")) == 3, f"活动ID {campaign_id} 的状态计数列表不足"
                # 断言第二个元素的count字段等于approved_count
                assert res2.get("object")[2]["count"] == rejected_count, f"活动ID {campaign_id} 的批准提交计数不匹配"

        print("所有Global+活动的提交状态计数验证通过。")

    @weeeTest.mark.list('sales', 'Transaction', 'MKPL', 'test_promotions')
    def test_admin_campaigns_global_fbw(self):
        """
        查看Global+FBW campaigns list
        """

        campaign_type1 = jmespath(Filters().admin_campaign_types(headers=sales_header, biz_type='seller'),
                                  'object[0].id')
        vendor_id = jmespath(Filters().vendor_market_list(headers=sales_header, biz_type='mkpl_fbw'), 'object[0].id')
        job = Promotions().job_campaigns_process(headers=sales_header)
        # 断言 job 执行成功，否则admin_campaigns case断言可能失败
        assert job['result'] is True, "The job did not execute successfully; the result is not True."
        assert job[
                   'object'] == "success", f"The job did not execute successfully; the object is not 'success', but {job['object']}."

        res1 = Promotions().admin_campaigns(headers=sales_header, biz_type="mkpl_fbw", seller_id=vendor_id)
        # 断言biz_type为"mkpl_fbw"，并检查submissions属于这个商家
        for campaign in res1['object']:
            assert campaign['biz_type'] == "mkpl_fbw", "biz_type is not mkpl_fbw"
            if campaign['submissions']:
                for submission in campaign['submissions']:
                    if submission['seller_id'] == vendor_id:
                        print(
                            f"ID: {campaign['id']}, Name: {campaign['name']}, Submission ID: {submission['submission_id']}")
                        # 获取campaign_id
                        campaign_id = campaign['id']
                        # 调用admin_campaign_detail方法
                        res_detail = Promotions().admin_campaign_detail(headers=sales_header, campaign_id=campaign_id)
                        assert res_detail[
                                   'result'] is True, "The res_detail did not execute successfully; the result is not True."
                        # 检查is_public是否为false
                        if res_detail['object']['is_public'] is False:
                            # 断言vendor_id存在于res_detail['object']['seller_ids']中
                            assert vendor_id in res_detail['object'][
                                'seller_ids'], f"vendor_id {vendor_id} is NOT present in campaign_id {campaign_id} when is_public is false"
                            print(
                                f"vendor_id {vendor_id} is present in campaign_id {campaign_id} and is_public is false")

        res2 = Promotions().admin_campaigns(headers=sales_header, biz_type="mkpl_fbw", campaign_type=campaign_type1)
        # 断言campaign_type_id与campaign_type1一致，并且biz_type为"mkpl_fbw"
        for campaign in res2['object']:
            assert campaign['campaign_type_id'] == campaign_type1, "campaign_type_id does not match campaign_type1"
            assert campaign['biz_type'] == "mkpl_fbw", "biz_type is not mkpl_fbw"

        # 设置洛杉矶的时区
        la_tz = pytz.timezone('America/Los_Angeles')
        # 获取当前的UTC时间，并转换为洛杉矶的当地时间
        current_time_la = datetime.now(tz=pytz.UTC).astimezone(la_tz)
        # 获取当前洛杉矶时间的时间戳
        current_timestamp_la = current_time_la.timestamp()

        res3 = Promotions().admin_campaigns(headers=sales_header, biz_type="mkpl_fbw", status_list=["S", "E", "A"])
        # 断言当前时间在submission_start和campaign_end之间，活动开始报名且活动未结束
        for campaign in res3['object']:
            assert campaign['submission_start'] <= current_timestamp_la <= campaign[
                'campaign_end'], "Current time is not between submission_start and campaign_end"

        res4 = Promotions().admin_campaigns(headers=sales_header, biz_type="mkpl_fbw", status_list="P")
        # 断言当前时间小于每个活动的submission_start，活动未开始报名
        for campaign in res4['object']:
            assert current_timestamp_la < campaign['submission_start'], "Current time is not less than submission_start"
        # 断言当前时间大于每个活动的campaign_end，活动已经结束
        res5 = Promotions().admin_campaigns(headers=sales_header, biz_type="mkpl_fbw", status_list="F")
        for campaign in res5['object']:
            assert current_timestamp_la > campaign['campaign_end'], "Current time is not greater than campaign_end"

        res6 = Promotions().admin_campaigns(headers=sales_header, biz_type="mkpl_fbw", status_list="D")
        assert res6['result'] == True, "The result of admin campaigns should be True."
        res7 = Promotions().admin_campaigns(headers=sales_header, biz_type="mkpl_fbw", status_list="R")
        # 检查submissions是否为Null，如果不为null则检查review_status是否为'R'，活动被删除且不存在报名中的submissions提报
        for campaign in res7['object']:
            if campaign['submissions'] is not None:
                for submission in campaign['submissions']:
                    assert submission[
                               'review_status'] == 'R', f"Found abnormal data: review_status is not 'R' for submission_id {submission['submission_id']}"
        res_calendar = Promotions().admin_campaigns_calendar(headers=sales_header, biz_type="mkpl_fbw")
        assert res_calendar['result'] == True, "The result of admin campaigns should be True."

    @weeeTest.mark.list('sales', 'Transaction', 'MKPL', 'test_promotions')
    def test_admin_campaigns_submissions_global_fbw(self):
        """
        查看Global+FBW campaigns 详情页及submissions信息
        """
        res0 = Promotions().admin_campaigns(headers=sales_header, biz_type="mkpl_fbw", status_list=["S", "E", "A"])

        # 使用断言确保res3中有返回的数据
        assert res0, "未能获取有效的促销活动数据。"
        assert 'result' in res0 and res0['result'], "返回的数据中result字段不为True。"
        assert 'object' in res0, "返回的数据中没有object字段。"

        response_json = res0

        # 遍历返回结构中的每个活动
        for campaign in response_json['object']:
            campaign_id = campaign['id']
            approved_count = campaign.get("approved_seller_count", 0)
            pending_count = campaign.get("pending_seller_count", 0)
            rejected_count = campaign.get("rejected_seller_count", 0)
            res2 = Promotions().submissions_status_count(headers=sales_header, campaign_id=campaign_id)

            # 检查approved_count是否不为零
            if approved_count != 0:
                res1 = Promotions().admin_campaign_submissions(headers=sales_header, campaign_id=campaign_id,
                                                               review_status="A")
                assert res1 and res1.get("result"), f"获取活动ID {campaign_id} 的批准提交失败"
                assert len(res1.get("object", [])) == approved_count, f"活动ID {campaign_id} 的批准提交计数不匹配"
                # 确保res2包含object键，并且它3个元素,三个tab
                assert res2.get("object") and len(res2.get("object")) == 3, f"活动ID {campaign_id} 的状态计数列表不足"
                # 断言第二个元素的count字段等于approved_count
                assert res2.get("object")[1]["count"] == approved_count, f"活动ID {campaign_id} 的批准提交计数不匹配"

            # 检查pending_count是否不为零
            if pending_count != 0:
                res1 = Promotions().admin_campaign_submissions(headers=sales_header, campaign_id=campaign_id,
                                                               review_status="I")
                assert res1 and res1.get("result"), f"获取活动ID {campaign_id} 的待审核提交失败"
                assert len(res1.get("object", [])) == pending_count, f"活动ID {campaign_id} 的待审核提交计数不匹配"
                # 确保res2包含object键，并且它3个元素,三个tab
                assert res2.get("object") and len(res2.get("object")) == 3, f"活动ID {campaign_id} 的状态计数列表不足"
                # 断言第二个元素的count字段等于approved_count
                assert res2.get("object")[0]["count"] == pending_count, f"活动ID {campaign_id} 的批准提交计数不匹配"

            # 检查rejected_count是否不为零
            if rejected_count != 0:
                res1 = Promotions().admin_campaign_submissions(headers=sales_header, campaign_id=campaign_id,
                                                               review_status="R")
                assert res1 and res1.get("result"), f"获取活动ID {campaign_id} 的拒绝提交失败"
                assert len(res1.get("object", [])) == rejected_count, f"活动ID {campaign_id} 的拒绝提交计数不匹配"
                # 确保res2包含object键，并且它3个元素,三个tab
                assert res2.get("object") and len(res2.get("object")) == 3, f"活动ID {campaign_id} 的状态计数列表不足"
                # 断言第二个元素的count字段等于approved_count
                assert res2.get("object")[2]["count"] == rejected_count, f"活动ID {campaign_id} 的批准提交计数不匹配"

        print("所有Global+活动的提交状态计数验证通过。")

    @weeeTest.mark.list('sales', 'Transaction', 'MKPL', 'test_promotions')
    def test_vender_list_by_tag_and_catalogue(self):
        """
        查看创建campaign时。private的商家查询
        """
        res_tagid = SellerMgmt().seller_optional_tag_list(headers=sales_header)
        assert res_tagid.get('result') is True, "seller_optional_tag_list API调用失败，没有返回result为True"
        res_category = SellerMgmt().seller_optional_product_first_catalogues(headers=sales_header)
        assert res_category.get('result') is True, "seller_optional_product_first_catalogues API调用失败，没有返回result为True"

        res_ethnicity = SellerMgmt().seller_optional_ethnicity(headers=sales_header, type_id=100)
        assert res_ethnicity.get('result') is True, "seller_optional_ethnicity API调用失败，没有返回result为True"

        # 确保API返回的数据结构正确
        assert 'object' in res_tagid and len(res_tagid['object']) > 0, "res_tagid中没有找到有效的object数据"
        assert 'object' in res_category and len(res_category['object']) > 0, "res_category中没有找到有效的object数据"
        assert 'object' in res_ethnicity and 'meta_codes' in res_ethnicity['object'] and len(
            res_ethnicity['object']['meta_codes']) > 0, "res_ethnicity中没有找到有效的meta_codes数据"

        # 使用获取的数据调用另一个API
        res = Filters().vender_list_by_tag_and_catalogue(
            headers=sales_header,
            biz_type="seller",
            tagId=res_tagid["object"][0]["id"],
            category=res_category["object"][0]["id"],
            ethnicity=res_ethnicity["object"]["meta_codes"][0]["key"]
        )
        assert res.get('result') is True, "vender_list_by_tag_and_catalogue API调用失败，没有返回result为True"


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    weeeTest.main()
