# !/usr/bin/python3
# -*- coding: utf-8 -*-


import weeeTest
from datetime import datetime, timedelta

from test_dir.api.ec.central_portal.central_im import CentralIm


class TestSalesImageSearchLogs(weeeTest.TestCase):

    @weeeTest.mark.list('sales', 'Transaction', 'test_sales_synonyms')
    def test_sales_image_search_logs(self, sales_header):
        """ # 商品销售信息管理-商品内容信息管理-图片搜索日志-查询图片搜索日志页面数据"""
        search_log_list = CentralIm().search_log_list(headers=sales_header)
        assert len(search_log_list["object"]["data"]) > 0, f'查询图片搜索日志页面数据异常{search_log_list}'

    @weeeTest.mark.list('sales', 'Transaction', 'test_search_logs_with_pagination')
    def test_search_logs_with_pagination(self, sales_header):
        """ # 商品销售信息管理-商品内容信息管理-图片搜索日志-分页查询测试"""
        # 测试第一页
        search_log_list = CentralIm().search_log_list(
            headers=sales_header,
            pageSize=10,
            startColumn=0
        )
        assert search_log_list["success"] is True, f'分页查询第一页失败{search_log_list}'
        assert "data" in search_log_list["object"], f'返回数据格式异常{search_log_list}'

        # 测试第二页
        search_log_list_page2 = CentralIm().search_log_list(
            headers=sales_header,
            pageSize=10,
            startColumn=10
        )
        assert search_log_list_page2["success"] is True, f'分页查询第二页失败{search_log_list_page2}'

    @weeeTest.mark.list('sales', 'Transaction', 'test_search_logs_by_search_type_image')
    def test_search_logs_by_search_type_image(self, sales_header):
        """ # 商品销售信息管理-商品内容信息管理-图片搜索日志-按搜索类型筛选(图片搜索)"""
        search_log_list = CentralIm().search_log_list(
            headers=sales_header,
            search_type="image",
            pageSize=20,
            startColumn=0
        )
        assert search_log_list["success"] is True, f'按图片搜索类型筛选失败{search_log_list}'
        assert "data" in search_log_list["object"], f'返回数据格式异常{search_log_list}'

    @weeeTest.mark.list('sales', 'Transaction', 'test_search_logs_by_search_type_upc')
    def test_search_logs_by_search_type_upc(self, sales_header):
        """ # 商品销售信息管理-商品内容信息管理-图片搜索日志-按搜索类型筛选(UPC码搜索)"""
        search_log_list = CentralIm().search_log_list(
            headers=sales_header,
            search_type="upc_code",
            pageSize=20,
            startColumn=0
        )
        assert search_log_list["success"] is True, f'按UPC码搜索类型筛选失败{search_log_list}'
        assert "data" in search_log_list["object"], f'返回数据格式异常{search_log_list}'

    @weeeTest.mark.list('sales', 'Transaction', 'test_search_logs_by_user_id')
    def test_search_logs_by_user_id(self, sales_header):
        """ # 商品销售信息管理-商品内容信息管理-图片搜索日志-按用户ID筛选"""
        search_log_list = CentralIm().search_log_list(
            headers=sales_header,
            user_id="8903000",
            pageSize=20,
            startColumn=0
        )
        assert search_log_list["success"] is True, f'按用户ID筛选失败{search_log_list}'
        assert "data" in search_log_list["object"], f'返回数据格式异常{search_log_list}'

    @weeeTest.mark.list('sales', 'Transaction', 'test_search_logs_by_time_range')
    def test_search_logs_by_time_range(self, sales_header):
        """ # 商品销售信息管理-商品内容信息管理-图片搜索日志-按时间范围筛选"""
        # 计算时间戳：当前时间减7天到当前时间
        end_time = int(datetime.now().timestamp())
        start_time = int((datetime.now() - timedelta(days=7)).timestamp())

        search_log_list = CentralIm().search_log_list(
            headers=sales_header,
            start_time=start_time,
            end_time=end_time,
            pageSize=20,
            startColumn=0
        )
        assert search_log_list["success"] is True, f'按时间范围筛选失败{search_log_list}'
        assert "data" in search_log_list["object"], f'返回数据格式异常{search_log_list}'
