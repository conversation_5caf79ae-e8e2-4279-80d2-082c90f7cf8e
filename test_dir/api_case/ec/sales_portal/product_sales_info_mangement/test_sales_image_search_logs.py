# !/usr/bin/python3
# -*- coding: utf-8 -*-


import weeeTest
from datetime import datetime, timedelta

from test_dir.api.ec.central_portal.central_im import CentralIm


class TestSalesImageSearchLogs(weeeTest.TestCase):

    @weeeTest.mark.list('sales', 'Transaction', 'test_sales_synonyms')
    def test_sales_image_search_logs(self, sales_header):
        """ # 商品销售信息管理-商品内容信息管理-图片搜索日志-查询图片搜索日志页面数据"""
        search_log_list = CentralIm().search_log_list(headers=sales_header)
        assert len(search_log_list["object"]["data"]) > 0, f'查询图片搜索日志页面数据异常{search_log_list}'

    @weeeTest.mark.list('sales', 'Transaction', 'test_search_log_list_startColumn')
    def test_search_log_list_startColumn(self, sales_header):
        """ # 商品销售信息管理-商品内容信息管理-图片搜索日志-测试startColumn字段"""
        search_log_list = CentralIm().search_log_list(
            headers=sales_header,
            startColumn=0
        )
        assert search_log_list["success"] is True, f'startColumn字段测试失败{search_log_list}'
        assert "data" in search_log_list["object"], f'返回数据格式异常{search_log_list}'

    @weeeTest.mark.list('sales', 'Transaction', 'test_search_log_list_pageSize')
    def test_search_log_list_pageSize(self, sales_header):
        """ # 商品销售信息管理-商品内容信息管理-图片搜索日志-测试pageSize字段"""
        search_log_list = CentralIm().search_log_list(
            headers=sales_header,
            pageSize=10
        )
        assert search_log_list["success"] is True, f'pageSize字段测试失败{search_log_list}'
        assert "data" in search_log_list["object"], f'返回数据格式异常{search_log_list}'

    @weeeTest.mark.list('sales', 'Transaction', 'test_search_log_list_search_type_all')
    def test_search_log_list_search_type_all(self, sales_header):
        """ # 商品销售信息管理-商品内容信息管理-图片搜索日志-测试search_type字段(All-不传参数)"""
        search_log_list = CentralIm().search_log_list(
            headers=sales_header,
            search_type=None
        )
        assert search_log_list["success"] is True, f'search_type字段(All)测试失败{search_log_list}'
        assert "data" in search_log_list["object"], f'返回数据格式异常{search_log_list}'

    @weeeTest.mark.list('sales', 'Transaction', 'test_search_log_list_search_type_image')
    def test_search_log_list_search_type_image(self, sales_header):
        """ # 商品销售信息管理-商品内容信息管理-图片搜索日志-测试search_type字段(image)"""
        search_log_list = CentralIm().search_log_list(
            headers=sales_header,
            search_type="image"
        )
        assert search_log_list["success"] is True, f'search_type字段(image)测试失败{search_log_list}'
        assert "data" in search_log_list["object"], f'返回数据格式异常{search_log_list}'

    @weeeTest.mark.list('sales', 'Transaction', 'test_search_log_list_search_type_upc_code')
    def test_search_log_list_search_type_upc_code(self, sales_header):
        """ # 商品销售信息管理-商品内容信息管理-图片搜索日志-测试search_type字段(upc_code)"""
        search_log_list = CentralIm().search_log_list(
            headers=sales_header,
            search_type="upc_code"
        )
        assert search_log_list["success"] is True, f'search_type字段(upc_code)测试失败{search_log_list}'
        assert "data" in search_log_list["object"], f'返回数据格式异常{search_log_list}'

    @weeeTest.mark.list('sales', 'Transaction', 'test_search_log_list_start_time')
    def test_search_log_list_start_time(self, sales_header):
        """ # 商品销售信息管理-商品内容信息管理-图片搜索日志-测试start_time字段"""
        start_time = int((datetime.now() - timedelta(days=7)).timestamp())
        search_log_list = CentralIm().search_log_list(
            headers=sales_header,
            start_time=start_time
        )
        assert search_log_list["success"] is True, f'start_time字段测试失败{search_log_list}'
        assert "data" in search_log_list["object"], f'返回数据格式异常{search_log_list}'

    @weeeTest.mark.list('sales', 'Transaction', 'test_search_log_list_end_time')
    def test_search_log_list_end_time(self, sales_header):
        """ # 商品销售信息管理-商品内容信息管理-图片搜索日志-测试end_time字段"""
        end_time = int(datetime.now().timestamp())
        search_log_list = CentralIm().search_log_list(
            headers=sales_header,
            end_time=end_time
        )
        assert search_log_list["success"] is True, f'end_time字段测试失败{search_log_list}'
        assert "data" in search_log_list["object"], f'返回数据格式异常{search_log_list}'

    @weeeTest.mark.list('sales', 'Transaction', 'test_search_log_list_user_id')
    def test_search_log_list_user_id(self, sales_header):
        """ # 商品销售信息管理-商品内容信息管理-图片搜索日志-测试user_id字段"""
        search_log_list = CentralIm().search_log_list(
            headers=sales_header,
            user_id="8903000"
        )
        assert search_log_list["success"] is True, f'user_id字段测试失败{search_log_list}'
        assert "data" in search_log_list["object"], f'返回数据格式异常{search_log_list}'


