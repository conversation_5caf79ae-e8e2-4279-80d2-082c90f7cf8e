# !/usr/bin/python3
# -*- coding: utf-8 -*-


import weeeTest
from test_dir.api.ec.central_portal.central_growth import CentralGrowth


class TestSalesBannerMgmt(weeeTest.TestCase):
    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_banner_query_banner(self, sales_header):
        """ # 页面管理-Banner管理-ADS schedule管理-查询ADS schedule页面数据 """
        query_banner = CentralGrowth().banner_query_banner(headers=sales_header)
        assert len(query_banner.get("object", {}).get("data", [])) > 0, f'查询ADS schedule页面数据为空{query_banner}'

        # 验证返回数据的基本结构
        if query_banner.get("object", {}).get("data"):
            for banner in query_banner["object"]["data"][:3]:  # 只检查前3个
                assert banner.get("id") is not None, f'Banner ID为空'
                assert banner.get("type") is not None, f'Banner类型为空'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_banner_query_banner_with_type_filter(self, sales_header):
        """ # 页面管理-Banner管理-ADS schedule管理-查询ADS schedule页面数据(按类型筛选) """
        # 测试home类型的广告
        query_banner_home = CentralGrowth().banner_query_banner(headers=sales_header, type="home")
        assert query_banner_home.get("result") is True, f'查询home类型广告失败{query_banner_home}'

        # 测试category类型的广告
        query_banner_category = CentralGrowth().banner_query_banner(headers=sales_header, type="category")
        assert query_banner_category.get("result") is True, f'查询category类型广告失败{query_banner_category}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_banner_query_banner_with_language_filter(self, sales_header):
        """ # 页面管理-Banner管理-ADS schedule管理-查询ADS schedule页面数据(按语言筛选) """
        languages = ["en", "zh", "ja", "vi", "zh-Hant", "ko"]

        for lang in languages:
            query_banner = CentralGrowth().banner_query_banner(headers=sales_header, support_language=lang)
            assert query_banner.get("result") is True, f'查询语言为{lang}的广告失败{query_banner}'
            # 验证返回的数据结构
            if query_banner.get("object", {}).get("data"):
                for banner in query_banner["object"]["data"][:2]:  # 只检查前2个
                    assert banner.get("id") is not None, f'Banner ID为空，语言:{lang}'
                    assert banner.get("support_language") is not None, f'支持语言为空，语言:{lang}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_banner_query_banner_with_area_filter(self, sales_header):
        """ # 页面管理-Banner管理-ADS schedule管理-查询ADS schedule页面数据(按区域筛选) """
        # 测试不同区域的广告
        areas = [1, 2, 4]

        for area in areas:
            query_banner = CentralGrowth().banner_query_banner(headers=sales_header, support_area=area)
            assert query_banner.get("result") is True, f'查询区域{area}的广告失败{query_banner}'
            # 验证返回的数据结构
            if query_banner.get("object", {}).get("data"):
                for banner in query_banner["object"]["data"][:2]:  # 只检查前2个
                    assert banner.get("id") is not None, f'Banner ID为空，区域:{area}'
                    assert banner.get("support_area") is not None, f'支持区域为空，区域:{area}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_banner_query_banner_with_position_filter(self, sales_header):
        """ # 页面管理-Banner管理-ADS schedule管理-查询ADS schedule页面数据(按位置筛选) """
        # 测试不同位置的广告
        positions = [3, 5]

        for pos in positions:
            query_banner = CentralGrowth().banner_query_banner(headers=sales_header, pos=pos)
            assert query_banner.get("result") is True, f'查询位置{pos}的广告失败{query_banner}'
            # 验证返回的数据结构
            if query_banner.get("object", {}).get("data"):
                for banner in query_banner["object"]["data"][:2]:  # 只检查前2个
                    assert banner.get("id") is not None, f'Banner ID为空，位置:{pos}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_banner_query_type_info(self, sales_header):
        """ # 页面管理-Banner管理-ADS schedule管理-ADS schedule页面过滤器返回数据 """
        query_type_info = CentralGrowth().banner_query_type_info(headers=sales_header)
        biz_type = query_type_info.get("object", {}).get("biz_type", [])
        support_area = query_type_info.get("object", {}).get("support_area", [])
        type_info = query_type_info.get("object", {}).get("type", [])
        vendor_list = query_type_info.get("object", {}).get("vendor_list", [])
        assert len(biz_type) > 0, f'查询ADS schedule页面biz_type数据为空{query_type_info}'
        assert len(support_area) > 0, f'查询ADS schedule页面support_area数据为空{query_type_info}'
        assert len(type_info) > 0, f'查询ADS schedule页面type数据为空{query_type_info}'
        assert len(vendor_list) > 0, f'查询ADS schedule页面vendor_list数据为空{query_type_info}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_banner_portal_list_normal(self, sales_header):
        """ # 页面管理-Banner管理-Banner列表-查询Banner列表页面数据 """
        banner_portal_list_normal = CentralGrowth().banner_portal_list_normal(headers=sales_header,
                                                                              sales_type="sales_org",
                                                                              sales_org=[1],
                                                                              language_store=["en"],
                                                                              placement=["home"])
        assert banner_portal_list_normal.get("result") is True, f'Banner列表查询失败{banner_portal_list_normal}'
        assert len(
            banner_portal_list_normal.get("object", {}).get("result", [])) > 0, f'查询Banner列表页面数据为空{banner_portal_list_normal}'

        # 验证返回数据的基本结构
        if banner_portal_list_normal.get("object", {}).get("result"):
            for banner in banner_portal_list_normal["object"]["result"][:3]:  # 只检查前3个
                assert banner.get("id") is not None, f'Banner ID为空'
                assert banner.get("name") is not None, f'Banner名称为空'
                assert banner.get("status") is not None, f'Banner状态为空'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_banner_portal_list_normal_with_multiple_sales_org(self, sales_header):
        """ # 页面管理-Banner管理-Banner列表-查询Banner列表页面数据(多销售组织) """
        # 测试多个销售组织
        banner_portal_list = CentralGrowth().banner_portal_list_normal(
            headers=sales_header,
            sales_type="sales_org",
            sales_org=[1, 2, 3],
            language_store=["en"],
            placement=["home"]
        )
        assert banner_portal_list.get("result") is True, f'多销售组织Banner列表查询失败{banner_portal_list}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_banner_portal_list_normal_with_multiple_languages(self, sales_header):
        """ # 页面管理-Banner管理-Banner列表-查询Banner列表页面数据(多语言) """
        # 测试多语言支持
        languages = [["en"], ["zh"], ["ja"], ["en", "zh"]]

        for lang_list in languages:
            banner_portal_list = CentralGrowth().banner_portal_list_normal(
                headers=sales_header,
                sales_type="sales_org",
                sales_org=[1],
                language_store=lang_list,
                placement=["home"]
            )
            assert banner_portal_list.get("result") is True, f'语言{lang_list}Banner列表查询失败{banner_portal_list}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_banner_portal_list_normal_with_multiple_placements(self, sales_header):
        """ # 页面管理-Banner管理-Banner列表-查询Banner列表页面数据(多位置) """
        # 测试不同位置的Banner
        placements = [["home"], ["category"], ["home", "category"]]

        for placement_list in placements:
            banner_portal_list = CentralGrowth().banner_portal_list_normal(
                headers=sales_header,
                sales_type="sales_org",
                sales_org=[1],
                language_store=["en"],
                placement=placement_list
            )
            assert banner_portal_list.get("result") is True, f'位置{placement_list}Banner列表查询失败{banner_portal_list}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_banner_portal_list_normal_with_status_filter(self, sales_header):
        """ # 页面管理-Banner管理-Banner列表-查询Banner列表页面数据(按状态筛选) """
        # 测试不同状态的Banner
        statuses = [["A"], ["C"], ["X"], ["A", "C"]]

        for status_list in statuses:
            banner_portal_list = CentralGrowth().banner_portal_list_normal(
                headers=sales_header,
                sales_type="sales_org",
                sales_org=[1],
                language_store=["en"],
                placement=["home"],
                status=status_list
            )
            assert banner_portal_list.get("result") is True, f'状态{status_list}Banner列表查询失败{banner_portal_list}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_banner_portal_list_normal_with_name_filter(self, sales_header):
        """ # 页面管理-Banner管理-Banner列表-查询Banner列表页面数据(按名称筛选) """
        # 测试按名称搜索Banner
        names = ["test", "promotion"]

        for name in names:
            banner_portal_list = CentralGrowth().banner_portal_list_normal(
                headers=sales_header,
                sales_type="sales_org",
                sales_org=[1],
                language_store=["en"],
                placement=["home"],
                name=name
            )
            assert banner_portal_list.get("result") is True, f'名称{name}Banner列表查询失败{banner_portal_list}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_banner_portal_list_normal_with_pagination(self, sales_header):
        """ # 页面管理-Banner管理-Banner列表-查询Banner列表页面数据(分页测试) """
        # 测试第一页
        banner_portal_first = CentralGrowth().banner_portal_list_normal(
            headers=sales_header,
            sales_type="sales_org",
            sales_org=[1],
            language_store=["en"],
            placement=["home"],
            page_no=1,
            page_size=10
        )
        assert banner_portal_first.get("result") is True, f'第一页Banner列表查询失败{banner_portal_first}'

        # 测试第二页
        banner_portal_second = CentralGrowth().banner_portal_list_normal(
            headers=sales_header,
            sales_type="sales_org",
            sales_org=[1],
            language_store=["en"],
            placement=["home"],
            page_no=2,
            page_size=10
        )
        assert banner_portal_second.get("result") is True, f'第二页Banner列表查询失败{banner_portal_second}'

        # 验证分页数据不重复（如果两页都有数据的话）
        first_page_ids = []
        second_page_ids = []

        if banner_portal_first.get("object", {}).get("result"):
            first_page_ids = [banner.get("id") for banner in banner_portal_first["object"]["result"]]

        if banner_portal_second.get("object", {}).get("result"):
            second_page_ids = [banner.get("id") for banner in banner_portal_second["object"]["result"]]

        # 如果两页都有数据，验证没有重复
        if first_page_ids and second_page_ids:
            common_ids = set(first_page_ids) & set(second_page_ids)
            assert len(common_ids) == 0, f'分页数据重复: {common_ids}'


    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_banner_portal_option(self, sales_header):
        """ # 页面管理-Banner管理-Banner列表-查询Banner列表页面过滤器返回数据 """
        banner_portal_option = CentralGrowth().banner_portal_option(headers=sales_header)
        frequency = banner_portal_option.get("object", {}).get("frequency", {}).get("enums", [])
        languageStore = banner_portal_option.get("object", {}).get("languageStore", {}).get("enums", [])
        placement = banner_portal_option.get("object", {}).get("placement", {}).get("enums", [])
        platform = banner_portal_option.get("object", {}).get("platform", {}).get("enums", [])
        play_mode = banner_portal_option.get("object", {}).get("play_mode", {}).get("enums", [])
        salesRegion = banner_portal_option.get("object", {}).get("salesRegion", {}).get("enums", [])
        status = banner_portal_option.get("object", {}).get("status", {}).get("enums", [])
        type_enums = banner_portal_option.get("object", {}).get("type", {}).get("enums", [])
        assert len(frequency) > 0, f'查询Banner列表页面frequency过滤器返回数据为空{banner_portal_option}'
        assert len(languageStore) > 0, f'查询Banner列表页面languageStore过滤器返回数据为空{banner_portal_option}'
        assert len(placement) > 0, f'查询Banner列表页面placement过滤器返回数据为空{banner_portal_option}'
        assert len(platform) > 0, f'查询Banner列表页面platform过滤器返回数据为空{banner_portal_option}'
        assert len(play_mode) > 0, f'查询Banner列表页面play_mode过滤器返回数据为空{banner_portal_option}'
        assert len(salesRegion) > 0, f'查询Banner列表页面salesRegion过滤器返回数据为空{banner_portal_option}'
        assert len(status) > 0, f'查询Banner列表页面status过滤器返回数据为空{banner_portal_option}'
        assert len(type_enums) > 0, f'查询Banner列表页面type过滤器返回数据为空{banner_portal_option}'

    @weeeTest.mark.list('sales', 'Transaction','sales_portal_tb1')
    def test_sales_banner_portal_modify_by_group_id(self, sales_header):
        """ # 页面管理-Banner管理-Banner列表-修改Banner列表页面数据 """
        banner_portal_list_normal = CentralGrowth().banner_portal_list_normal(headers=sales_header,
                                                                              sales_type="sales_org",
                                                                              sales_org=[1],
                                                                              language_store=["en"],
                                                                              placement=["home"]
                                                                              )
        assert len(banner_portal_list_normal.get("object", {}).get("result", [])) > 0, f'查询Banner列表页面数据为空{banner_portal_list_normal}'

        result_list = banner_portal_list_normal.get("object", {}).get("result", [])
        if result_list:
            group_id = result_list[0].get("id")
            status = result_list[0].get("status")
            update_status = None
            if status == "A":
                update_status = "C"
            elif status == "C":
                update_status = "A"

            if group_id and update_status:
                banner_portal_modify = CentralGrowth().banner_portal_modify_by_group_id(headers=sales_header,
                                                                                        group_id=group_id,
                                                                                        status=update_status)
                assert banner_portal_modify.get("result") is True, f'修改Banner列表页面数据失败{banner_portal_modify}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_banner_portal_list_calendar(self, sales_header):
        """ # 页面管理-Banner管理-Banner日历-查询Banner日历页面数据 """
        import time
        from datetime import datetime, timedelta

        # 计算时间范围（当前时间-5天到+5天）
        start_time = int((datetime.now() - timedelta(days=5)).timestamp())
        end_time = int((datetime.now() + timedelta(days=5)).timestamp())

        banner_calendar = CentralGrowth().banner_portal_list_calendar(
            headers=sales_header,
            sales_org=[1],
            language_store=["en"],
            placement=["home"],
            status="A",
            start_time=start_time,
            end_time=end_time
        )
        assert banner_calendar.get("result") is True, f'Banner日历查询失败{banner_calendar}'

        # 验证返回数据的基本结构
        if banner_calendar.get("object", {}).get("result"):
            for banner in banner_calendar["object"]["result"][:3]:  # 只检查前3个
                assert banner.get("id") is not None, f'Banner ID为空'
                assert banner.get("name") is not None, f'Banner名称为空'
                assert banner.get("status") is not None, f'Banner状态为空'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_banner_portal_list_calendar_with_multiple_sales_org(self, sales_header):
        """ # 页面管理-Banner管理-Banner日历-查询Banner日历页面数据(多销售组织) """
        import time
        from datetime import datetime, timedelta

        start_time = int((datetime.now() - timedelta(days=3)).timestamp())
        end_time = int((datetime.now() + timedelta(days=3)).timestamp())

        # 测试多个销售组织
        sales_orgs = [[1], [2], [3]]

        for sales_org in sales_orgs:
            banner_calendar = CentralGrowth().banner_portal_list_calendar(
                headers=sales_header,
                sales_org=sales_org,
                language_store=["en"],
                placement=["home"],
                status="A",
                start_time=start_time,
                end_time=end_time
            )
            assert banner_calendar.get("result") is True, f'销售组织{sales_org}Banner日历查询失败{banner_calendar}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_banner_portal_list_calendar_with_multiple_languages(self, sales_header):
        """ # 页面管理-Banner管理-Banner日历-查询Banner日历页面数据(多语言) """
        import time
        from datetime import datetime, timedelta

        start_time = int((datetime.now() - timedelta(days=3)).timestamp())
        end_time = int((datetime.now() + timedelta(days=3)).timestamp())

        # 测试多语言支持
        languages = [["en"], ["zh"], ["ja"], ["en", "zh"]]

        for lang_list in languages:
            banner_calendar = CentralGrowth().banner_portal_list_calendar(
                headers=sales_header,
                sales_org=[1],
                language_store=lang_list,
                placement=["home"],
                status="A",
                start_time=start_time,
                end_time=end_time
            )
            assert banner_calendar.get("result") is True, f'语言{lang_list}Banner日历查询失败{banner_calendar}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_banner_portal_list_calendar_with_multiple_placements(self, sales_header):
        """ # 页面管理-Banner管理-Banner日历-查询Banner日历页面数据(多位置) """
        import time
        from datetime import datetime, timedelta

        start_time = int((datetime.now() - timedelta(days=3)).timestamp())
        end_time = int((datetime.now() + timedelta(days=3)).timestamp())

        # 测试不同位置的Banner
        placements = [["home"], ["collect"], ["topx"]]

        for placement_list in placements:
            banner_calendar = CentralGrowth().banner_portal_list_calendar(
                headers=sales_header,
                sales_org=[1],
                language_store=["en"],
                placement=placement_list,
                status="A",
                start_time=start_time,
                end_time=end_time
            )
            assert banner_calendar.get("result") is True, f'位置{placement_list}Banner日历查询失败{banner_calendar}'

if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')

