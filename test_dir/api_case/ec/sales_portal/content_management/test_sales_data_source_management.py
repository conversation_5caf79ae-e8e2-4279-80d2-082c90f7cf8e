# !/usr/bin/python3
# -*- coding: utf-8 -*-


import weeeTest

from test_dir.api.ec.central_portal.central_content import CentralContent
from test_dir.api.ec.central_portal.central_growth import CentralGrowth


class TestSalesDataSourceMgmt(weeeTest.TestCase):
    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal')
    def test_sales_data_source(self, sales_header):
        """ # 页面管理-数据源管理-查询数据源管理页面数据 """
        data_object_list = CentralContent().content_data_object_list(headers=sales_header)
        assert data_object_list.get("result") is True, f'数据源管理查询失败{data_object_list}'
        assert len(data_object_list.get("object", {}).get("data", [])) > 0, f'查询数据源管理页面数据为空{data_object_list}'

        # 验证返回数据的基本结构
        if data_object_list.get("object", {}).get("data"):
            for data_source in data_object_list["object"]["data"][:3]:  # 只检查前3个
                assert data_source.get("rec_id") is not None, f'数据源ID为空'
                assert data_source.get("dataobject_name") is not None, f'数据源名称为空'
                assert data_source.get("data_type") is not None, f'数据源类型为空'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal')
    def test_sales_data_source_with_keyword_filter(self, sales_header):
        """ # 页面管理-数据源管理-查询数据源管理页面数据(按关键词筛选) """
        # 测试关键词搜索
        keywords = ["test"]

        for keyword in keywords:
            data_object_list = CentralContent().content_data_object_list(
                headers=sales_header,
                keyword=keyword
            )
            assert data_object_list.get("result") is True, f'关键词{keyword}数据源查询失败{data_object_list}'
            # 如果有结果，验证关键词是否在数据源信息中
            assert data_object_list.get("object", {}).get("data") , f'关键词{keyword}数据源查询失败{data_object_list}'
