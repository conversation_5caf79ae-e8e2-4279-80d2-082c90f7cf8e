# !/usr/bin/python3
# -*- coding: utf-8 -*-


import weeeTest

from test_dir.api.ec.central_portal.central_content import CentralContent


class TestSalesPageContentMgmt(weeeTest.TestCase):
    def _sales_activity_page(self, sales_header):
        """ # 面管理-内容管理-查询单活动页面 """
        content_page_list = CentralContent().content_page_list(headers=sales_header, page_type=7)
        assert len(content_page_list["object"]["data"]) > 0, f'内容管理-查询单活动页面为空{content_page_list}'
        return content_page_list

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_search_sales_activity_page(self,sales_header):
        """ # 页面管理-内容管理-查询单活动页面 """
        activity_page = CentralContent().content_page_list(headers=sales_header, page_type=7)
        assert len(activity_page["object"]["list"]) > 0, f'内容管理-查询单活动页面为空{activity_page}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_search_sales_activity_page_with_status_filter(self, sales_header):
        """ # 页面管理-内容管理-查询单活动页面(按状态筛选) """
        # 测试状态为A的活动页面
        activity_page_active = CentralContent().content_page_list(headers=sales_header, page_type=7, status="A")
        assert activity_page_active.get("result") is True, f'查询状态为A的活动页面失败{activity_page_active}'

        # 测试状态为R的活动页面
        activity_page_rejected = CentralContent().content_page_list(headers=sales_header, page_type=7, status="R")
        assert activity_page_rejected.get("result") is True, f'查询状态为R的活动页面失败{activity_page_rejected}'

        # 测试状态为G的活动页面
        activity_page_gray = CentralContent().content_page_list(headers=sales_header, page_type=7, status="G")
        assert activity_page_gray.get("result") is True, f'查询状态为G的活动页面失败{activity_page_gray}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_search_sales_activity_page_with_language_filter(self, sales_header):
        """ # 页面管理-内容管理-查询单活动页面(按语言筛选) """
        languages = ["en", "zh", "ja", "vi", "zh-Hant", "ko"]

        for lang in languages:
            activity_page = CentralContent().content_page_list(headers=sales_header, page_type=7, lang=lang)
            assert activity_page.get("result") is True, f'查询语言为{lang}的活动页面失败{activity_page}'
            # 验证返回的数据结构
            if activity_page.get("object", {}).get("list"):
                for page in activity_page["object"]["list"][:3]:  # 只检查前3个
                    assert page.get("page_key") is not None, f'页面key为空，语言:{lang}'
                    assert page.get("page_url") is not None, f'页面URL为空，语言:{lang}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_search_sales_activity_page_with_homepage_filter(self, sales_header):
        """ # 页面管理-内容管理-查询单活动页面(按首页显示筛选) """
        # 测试显示在首页的活动页面
        activity_page_homepage = CentralContent().content_page_list(headers=sales_header, page_type=7, is_show_homepage=1)
        assert activity_page_homepage.get("result") is True, f'查询首页显示的活动页面失败{activity_page_homepage}'

        # 测试不显示在首页的活动页面
        activity_page_not_homepage = CentralContent().content_page_list(headers=sales_header, page_type=7, is_show_homepage=0)
        assert activity_page_not_homepage.get("result") is True, f'查询非首页显示的活动页面失败{activity_page_not_homepage}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_search_sales_activity_page_with_keyword_filter(self, sales_header):
        """ # 页面管理-内容管理-查询单活动页面(按关键词筛选) """
        # 测试关键词搜索
        keywords = ["test", "activity", "page"]

        for keyword in keywords:
            activity_page = CentralContent().content_page_list(headers=sales_header, page_type=7, keyword=keyword)
            assert activity_page.get("result") is True, f'关键词{keyword}搜索活动页面失败{activity_page}'
            # 如果有结果，验证关键词是否在页面信息中
            if activity_page.get("object", {}).get("list"):
                for page in activity_page["object"]["list"][:2]:  # 只检查前2个
                    page_info = str(page.get("page_key", "")) + str(page.get("page_url", "")) + str(page.get("page_title", ""))
                    # 注意：这里不强制要求关键词匹配，因为搜索可能是模糊匹配

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_search_sales_activity_page_with_pagination(self, sales_header):
        """ # 页面管理-内容管理-查询单活动页面(分页测试) """
        # 测试第一页
        activity_page_first = CentralContent().content_page_list(headers=sales_header, page_type=7, limit=20, offset=0)
        assert activity_page_first.get("result") is True, f'查询第一页活动页面失败{activity_page_first}'

        # 测试第二页
        activity_page_second = CentralContent().content_page_list(headers=sales_header, page_type=7, limit=20, offset=20)
        assert activity_page_second.get("result") is True, f'查询第二页活动页面失败{activity_page_second}'

        # 验证分页数据不重复（如果两页都有数据的话）
        first_page_keys = []
        second_page_keys = []

        if activity_page_first.get("object", {}).get("list"):
            first_page_keys = [page.get("page_key") for page in activity_page_first["object"]["list"]]

        if activity_page_second.get("object", {}).get("list"):
            second_page_keys = [page.get("page_key") for page in activity_page_second["object"]["list"]]

        # 如果两页都有数据，验证没有重复
        if first_page_keys and second_page_keys:
            common_keys = set(first_page_keys) & set(second_page_keys)
            assert len(common_keys) == 0, f'分页数据重复: {common_keys}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_search_sales_routing_list(self, sales_header):
        """ # 页面管理-内容管理-查询复合活动页面 """
        activity_page = CentralContent().content_routing_list(headers=sales_header)
        assert len(activity_page.get("object", {}).get("data", [])) > 0, f'内容管理-查询复合活动页面为空{activity_page}'
        for index, data in enumerate(activity_page.get("object", {}).get("data", [])):
            assert data.get("routing_key") is not None
            assert data.get("routing_url") is not None
            # 更新复合页面数据状态
            if data.get("status") == "A":
                outing_operate = CentralContent().content_routing_operate(headers=sales_header,
                                                                          routing_id=data.get("rec_id"),
                                                                          status="R")
                assert outing_operate.get("object") == "success", f'更新复合页面数据状态{activity_page}'

            elif data.get("status") == "R":
                outing_operate = CentralContent().content_routing_operate(headers=sales_header,
                                                                          routing_id=data.get("rec_id"),
                                                                          status="R")
                assert outing_operate.get("object") == "success", f'更新复合页面数据状态{activity_page}'

            if index == 5:
                break

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_search_sales_routing_list_with_status_filter(self, sales_header):
        """ # 页面管理-内容管理-查询复合活动页面(按状态筛选) """
        # 测试状态为A的复合活动页面
        routing_page_active = CentralContent().content_routing_list(headers=sales_header, status="A")
        assert routing_page_active.get("result") is True, f'查询状态为A的复合活动页面失败{routing_page_active}'

        # 测试状态为R的复合活动页面
        routing_page_rejected = CentralContent().content_routing_list(headers=sales_header, status="R")
        assert routing_page_rejected.get("result") is True, f'查询状态为R的复合活动页面失败{routing_page_rejected}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_search_sales_routing_list_with_keyword_filter(self, sales_header):
        """ # 页面管理-内容管理-查询复合活动页面(按关键词筛选) """
        # 测试关键词搜索
        keywords = ["test", "activity"]

        for keyword in keywords:
            routing_page = CentralContent().content_routing_list(headers=sales_header, keyword=keyword)
            assert routing_page.get("result") is True, f'关键词{keyword}搜索复合活动页面失败{routing_page}'
            # 如果有结果，验证返回数据的基本结构
            if routing_page.get("object", {}).get("data"):
                for page in routing_page["object"]["data"][:2]:  # 只检查前2个
                    assert page.get("routing_key") is not None, f'路由key为空，关键词:{keyword}'
                    assert page.get("routing_url") is not None, f'路由URL为空，关键词:{keyword}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_search_sales_routing_list_with_pagination(self, sales_header):
        """ # 页面管理-内容管理-查询复合活动页面(分页测试) """
        # 测试第一页
        routing_page_first = CentralContent().content_routing_list(headers=sales_header, limit=5, offset=0)
        assert routing_page_first.get("result") is True, f'查询第一页复合活动页面失败{routing_page_first}'

        # 测试第二页
        routing_page_second = CentralContent().content_routing_list(headers=sales_header, limit=5, offset=5)
        assert routing_page_second.get("result") is True, f'查询第二页复合活动页面失败{routing_page_second}'

        # 验证分页数据不重复（如果两页都有数据的话）
        first_page_keys = []
        second_page_keys = []

        if routing_page_first.get("object", {}).get("data"):
            first_page_keys = [page.get("routing_key") for page in routing_page_first["object"]["data"]]

        if routing_page_second.get("object", {}).get("data"):
            second_page_keys = [page.get("routing_key") for page in routing_page_second["object"]["data"]]

        # 如果两页都有数据，验证没有重复
        if first_page_keys and second_page_keys:
            common_keys = set(first_page_keys) & set(second_page_keys)
            assert len(common_keys) == 0, f'分页数据重复: {common_keys}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_search_sales_routing_list_combined_filters(self, sales_header):
        """ # 页面管理-内容管理-查询复合活动页面(组合筛选条件) """
        # 测试组合筛选条件：状态+关键词+分页
        routing_page = CentralContent().content_routing_list(
            headers=sales_header,
            status="A",
            keyword="test",
            limit=10,
            offset=0
        )
        assert routing_page.get("result") is True, f'组合筛选条件查询复合活动页面失败{routing_page}'

        # 验证返回数据的基本结构
        if routing_page.get("object", {}).get("data"):
            for page in routing_page["object"]["data"][:3]:  # 只检查前3个
                assert page.get("routing_key") is not None, f'路由key为空'
                assert page.get("routing_url") is not None, f'路由URL为空'
                assert page.get("status") is not None, f'状态为空'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal_tb1')
    def test_search_sales_routing_operate_status_update(self, sales_header):
        """ # 页面管理-内容管理-复合活动页面状态更新测试 """
        # 获取复合活动页面列表
        routing_page = CentralContent().content_routing_list(headers=sales_header, limit=3)
        assert routing_page.get("result") is True, f'获取复合活动页面列表失败{routing_page}'

        if routing_page.get("object", {}).get("data"):
            for index, data in enumerate(routing_page["object"]["data"]):
                routing_id = data.get("rec_id")
                current_status = data.get("status")

                if routing_id and current_status:
                    # 测试状态更新（保持原状态，不实际改变）
                    operate_result = CentralContent().content_routing_operate(
                        headers=sales_header,
                        routing_id=routing_id,
                        status=current_status  # 保持原状态
                    )
                    assert operate_result.get("result") is True, f'更新复合页面状态失败{operate_result}'
                    assert operate_result.get("object") == "success", f'更新复合页面状态返回结果异常{operate_result}'

                if index == 2:  # 只测试前3个
                    break

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_search_sales_brand_page(self, sales_header):
        """ # 页面管理-内容管理-查询品牌页面 """
        content_page_list = CentralContent().content_page_list(headers=sales_header, page_type=9)
        assert len(content_page_list.get("object", {}).get("list", [])) > 0, f'内容管理-查询品牌页面为空{content_page_list}'
        for index, data in enumerate(content_page_list.get("object", {}).get("list", [])):
            assert data.get("page_url") is not None, f'品牌页面URL为空{content_page_list}'
            assert data.get("page_key") is not None, f'品牌页面key为空{content_page_list}'

            # # offline 品牌页面数据---暂时不执行
            # if data.get("status") == "A":
            #     page_trash = CentralContent().content_page_trash(headers=sales_header,
            #                                                      page_key=data.get("page_key"),
            #                                                      page_type=9)
            #     assert page_trash.get("object") == data.get("page_key")
            #
            # elif data.get("status") == "R":
            #     page_trash = CentralContent().content_page_trash(headers=sales_header,
            #                                                      page_key=data.get("page_key"),
            #                                                      page_type=9)
            #     assert page_trash.get("object") == data.get("page_key")

            if index == 5:
                break

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_search_sales_brand_page_with_status_filter(self, sales_header):
        """ # 页面管理-内容管理-查询品牌页面(按状态筛选) """
        # 测试状态为A的品牌页面
        brand_page_active = CentralContent().content_page_list(headers=sales_header, page_type=9, status="A")
        assert brand_page_active.get("result") is True, f'查询状态为A的品牌页面失败{brand_page_active}'

        # 测试状态为R的品牌页面
        brand_page_rejected = CentralContent().content_page_list(headers=sales_header, page_type=9, status="R")
        assert brand_page_rejected.get("result") is True, f'查询状态为R的品牌页面失败{brand_page_rejected}'

        # 测试状态为G的品牌页面
        brand_page_gray = CentralContent().content_page_list(headers=sales_header, page_type=9, status="G")
        assert brand_page_gray.get("result") is True, f'查询状态为G的品牌页面失败{brand_page_gray}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_search_sales_brand_page_with_pagination(self, sales_header):
        """ # 页面管理-内容管理-查询品牌页面(分页测试) """
        # 测试第一页
        brand_page_first = CentralContent().content_page_list(headers=sales_header, page_type=9, limit=5, offset=0)
        assert brand_page_first.get("result") is True, f'查询第一页品牌页面失败{brand_page_first}'

        # 测试第二页
        brand_page_second = CentralContent().content_page_list(headers=sales_header, page_type=9, limit=5, offset=5)
        assert brand_page_second.get("result") is True, f'查询第二页品牌页面失败{brand_page_second}'

        # 验证分页数据不重复（如果两页都有数据的话）
        first_page_keys = []
        second_page_keys = []

        if brand_page_first.get("object", {}).get("list"):
            first_page_keys = [page.get("page_key") for page in brand_page_first["object"]["list"]]

        if brand_page_second.get("object", {}).get("list"):
            second_page_keys = [page.get("page_key") for page in brand_page_second["object"]["list"]]

        # 如果两页都有数据，验证没有重复
        if first_page_keys and second_page_keys:
            common_keys = set(first_page_keys) & set(second_page_keys)
            assert len(common_keys) == 0, f'分页数据重复: {common_keys}'


