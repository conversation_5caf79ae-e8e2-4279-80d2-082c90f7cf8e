# !/usr/bin/python3
# -*- coding: utf-8 -*-


import weeeTest

from test_dir.api.ec.central_portal.central_im import CentralIm


class TestSalesCategoryIcon(weeeTest.TestCase):
    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_category_icon(self, sales_header):
        """ # 内容管理-分类Icon活动 """
        nav_card_list = CentralIm().nav_card_list(headers=sales_header)
        assert nav_card_list.get("result") is True, f'分类Icon活动查询失败{nav_card_list}'
        assert len(nav_card_list.get("object", {}).get("data", [])) > 0, f'查询分类Icon活动数据为空{nav_card_list}'

        # 验证返回数据的基本结构
        if nav_card_list.get("object", {}).get("data"):
            for nav_card in nav_card_list["object"]["data"][:3]:  # 只检查前3个
                assert nav_card.get("icon_url") is not None, f'分类Icon icon_url为空'
                assert nav_card.get("img_url") is not None, f'分类Icon img_url为空'
                assert nav_card.get("label") is not None, f'分类Icon label为空'
                assert nav_card.get("link_url") is not None, f'分类Iconlink_url为空'
                assert nav_card.get("nav_card_key") is not None, f'分类Iconnav_card_key为空'
                assert nav_card.get("slug") is not None, f'分类Iconslug为空'
                assert nav_card.get("mobile_img_url") is not None, f'分类Icon mobile_img_url为空'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_category_icon_with_keyword_filter(self, sales_header):
        """ # 内容管理-分类Icon活动-按关键词筛选 """
        # 测试关键词搜索
        keywords = ["test"]

        for keyword in keywords:
            nav_card_list = CentralIm().nav_card_list(
                headers=sales_header,
                keyword=keyword
            )
            assert nav_card_list.get("result") is True, f'关键词{keyword}分类Icon活动查询失败{nav_card_list}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_category_icon_with_progress_filter(self, sales_header):
        """ # 内容管理-分类Icon活动-按进度筛选 """
        # 测试不同进度状态
        progress_states = ["10", "20", "30"]

        for progress in progress_states:
            nav_card_list = CentralIm().nav_card_list(
                headers=sales_header,
                progress=progress
            )
            assert nav_card_list.get("result") is True, f'进度{progress}分类Icon活动查询失败{nav_card_list}'
            if nav_card_list.get("object", {}).get("data"):
                for nav_card in nav_card_list["object"]["data"][:3]:  # 只检查前3个
                    assert nav_card.get("icon_url") is not None, f'分类Icon icon_url为空'
                    assert nav_card.get("img_url") is not None, f'分类Icon img_url为空'
                    assert nav_card.get("label") is not None, f'分类Icon label为空'
                    assert nav_card.get("link_url") is not None, f'分类Iconlink_url为空'
                    assert nav_card.get("nav_card_key") is not None, f'分类Iconnav_card_key为空'
                    assert nav_card.get("slug") is not None, f'分类Iconslug为空'
                    assert nav_card.get("mobile_img_url") is not None, f'分类Icon mobile_img_url为空'
                    assert nav_card.get("progress") == progress , f'分类Icon 状态不对'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal')
    def test_sales_category_icon_with_language_filter(self, sales_header):
        """ # 内容管理-分类Icon活动-按语言筛选 """
        # 测试不同语言
        languages = ["en", "zh"]

        for language in languages:
            # 当语言为en时，需要传递store参数
            store = "cn" if language == "en" else None

            nav_card_list = CentralIm().nav_card_list(
                headers=sales_header,
                language=language,
                store=store
            )
            assert nav_card_list.get("result") is True, f'语言{language}分类Icon活动查询失败{nav_card_list}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_category_icon_with_sales_org_filter(self, sales_header):
        """ # 内容管理-分类Icon活动-按销售组织筛选 """
        # 测试不同销售组织
        sales_orgs = [1, 2, 3, 4]

        for sales_org in sales_orgs:
            nav_card_list = CentralIm().nav_card_list(
                headers=sales_header,
                sales_org=sales_org
            )
            assert nav_card_list.get("result") is True, f'销售组织{sales_org}分类Icon活动查询失败{nav_card_list}'
            # 验证返回数据的销售组织信息
            if nav_card_list.get("object", {}).get("data"):
                for nav_card in nav_card_list["object"]["data"][:2]:  # 只检查前2个
                    assert nav_card.get("icon_url") is not None, f'分类Icon icon_url为空'
                    assert nav_card.get("img_url") is not None, f'分类Icon img_url为空'
                    assert nav_card.get("label") is not None, f'分类Icon label为空'
                    assert nav_card.get("link_url") is not None, f'分类Iconlink_url为空'
                    assert nav_card.get("nav_card_key") is not None, f'分类Iconnav_card_key为空'
                    assert nav_card.get("slug") is not None, f'分类Iconslug为空'
                    assert nav_card.get("mobile_img_url") is not None, f'分类Icon mobile_img_url为空'


