# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_item.py
@Description    :
@CreateTime     :  2024/9/11 14:40
@Software       :  PyCharm
-----------------------------------
"""

from typing import Any

import requests
import weeeTest
import json

from qa_config.ec_sql import EcDB


class TestAutoToolRefundOrder(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'AutoTool','tb1',)
    def test_auto_tool_refund_order(self):
        """ 申请售后-订单造数据 """
        # 操作步骤
        # 直接下单，拿到订单号
        EcDB().refund_order(order_id="42650222")
        # json.loads(EcDB().update_order(order_id="42641774"))


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
