# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""

import weeeTest

from qa_config.ec_sql import EcDB
from test_dir.api.ec.ec_item.my_favorite.api_favorites import ApiFavorites
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from qa_config.product_data import product_ids


class TestAutoToolAddonOrder(weeeTest.TestCase):
    @weeeTest.mark.list('AutoTool', 'tb1','Transaction')
    def test_auto_tool_add_on_order(self, ec_login_header):
        """ Addon造数case"""
        EcDB().add_on_order(order_id="42641774")


