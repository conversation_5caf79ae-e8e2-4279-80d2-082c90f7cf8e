# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""

import weeeTest

from qa_config.ec_sql import EcDB



class TestAutoToolTodaysOrder(weeeTest.TestCase):
    @weeeTest.mark.list('Transaction', 'AutoTool','tb1',)
    def test_auto_tool_today_order(self, ec_login_header):
        """ 今日订单造数case"""
        EcDB().today_order(order_id="42648454")


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
