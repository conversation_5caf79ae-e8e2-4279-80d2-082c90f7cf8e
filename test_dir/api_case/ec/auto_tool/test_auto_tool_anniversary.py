# -*- coding: utf-8 -*-
"""
Sales MKPL 接口维护
"""
import weeeTest
from weeeTest import log, jmespath, weeeConfig
from datetime import datetime, timedelta
from test_dir.api.ec.central_portal.central_mkpl import Promotions


class TestAutoToolAnniversary(weeeTest.TestCase):
    @weeeTest.mark.list('AutoTool','tb1', 'Transaction')
    def test_auto_tool_anniversary(self, seller_header):
        """ 请输入商家和商品id """
        seller_products = {8114: [2070803]}

        for seller_id, product_ids in seller_products.items():
            for product_id in product_ids:
                try:
                    # 获取当前时间
                    now = datetime.now()
                    # 打印出年月日时分秒
                    # 创建包含当前时间和product_id的字符串
                    remark = now.strftime("%Y%m%d%H%M%S") + "_" + str(product_id)
                    coupon_plan = Promotions().mkpl_coupon_plan(headers=seller_header, group_id=210, remark=remark,
                                                                type="Z",
                                                                apply_relative_time=1, direct_skus=product_id,
                                                                vendor_id=str(seller_id),
                                                                order_amount_limit=0, apply_quantity=1, percentage=100)
                    print("API Response:", coupon_plan)
                    '''
                    coupon_plan = Promotions().mkpl_coupon_plan(headers=seller_header, group_id=206, remark=remark, type="D",
                                                                apply_relative_time=14, direct_skus=product_id,
                                                                vendor_id=str(seller_id),
                                                                order_amount_limit=0, issue_quantity=100000, amount=1.55, )
                    '''
                    print("优惠券创建成功", coupon_plan['object']['id'])

                    coupon_status = Promotions().coupon_plan_update(headers=seller_header,
                                                                    id=coupon_plan['object']['id'],
                                                                    status="G")
                    print("优惠券提交成功", coupon_status)

                    r = Promotions().raffle_refresh_prize(headers=seller_header, raffle_id=43, pool_id=190, weight=1,
                                                          quantity=1, refer_type="coupon_gift",
                                                          refer_id=coupon_plan['object']['id'],
                                                          price=100, vendor_id=seller_id)
                    print("新增小奖成功", r)

                except Exception as e:
                    print("发生了异常:", e)


if __name__ == '__main__':
    seller_header = {
        "Authorization": "Bearer eyJraWQiOiI5Y2IzYjhiYS0xMGMxLTQxMGUtODg0OS1mNGFhZTU4Nzc0ZjMiLCJhbGciOiJSUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.r6hnaHHlpkAG3DYZHTmu1XRq0rN32pGXXKhozpoVpI_VZY_ho-PRDhljO4-zLeZPmmhFHq1Bf7Uz08TQEaYdhp0F6q1keSr9l1V93OnUY_SrTMj-rkQBFF0YIcerTOClKr69TQKZatDoLh7FGKusMbLkPbFeIwfo1wVeFTvG2Js",
        "Content-Type": "application/json"
    }
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    test_instance = TestAnniversary()
    test_instance.test_anniversary(seller_header)
