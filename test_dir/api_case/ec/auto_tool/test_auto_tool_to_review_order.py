# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_item.py
@Description    :
@CreateTime     :  2024/9/11 14:40
@Software       :  PyCharm
-----------------------------------
"""

from typing import Any

import requests
import weeeTest
import json

from qa_config.ec_sql import EcDB


class TestAutoToolToReviewOrder(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'AutoTool','tb1',)
    def test_auto_tool_to_review_order(self):
        """ 待晒单-订单造数据 """
        # 操作步骤
        # 直接下单，拿到订单号，更改订单状态=F,补充invoice id
        EcDB().to_review_order(order_id="42654776")


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
