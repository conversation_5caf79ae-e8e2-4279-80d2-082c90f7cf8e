import random
import string
import weeeTest
from weeeTest import weeeConfig
from qa_config.ec_sql import EcDB


class TestAutoToolSellerOrder(weeeTest.TestCase):
    @weeeTest.mark.list('Transaction', 'AutoTool','tb1',)
    def generate_tracking_number(self):
        """生成物流单号（4字母+10数字+3字母）"""
        prefix = ''.join(random.choices(string.ascii_uppercase, k=4))
        numbers = ''.join(random.choices(string.digits, k=10))
        suffix = ''.join(random.choices(string.ascii_uppercase, k=3))
        return f"{prefix}{numbers}{suffix}"

    @weeeTest.mark.list('test_seller_order', 'Transaction', 'tb1', 'autotool')
    def test_auto_tool_seller_ship_order(self, seller_header, order_id: int):
        """执行发货操作"""
        # 可用物流公司ID列表
        carrier_ids = [22, 13, 30, 23, 21, 32, 8, 37, 36, 2, 38, 6, 25, 14, 15, 11, 35, 5, 29,
                       26, 19, 28, 31, 27, 24, 16, 17, 33, 34, 1, 3, 20, 18]
        carrier_id=random.choice(carrier_ids)
        carrier_name=EcDB().get_carrier_name(carrier_id=carrier_id)

        data = {
            "packages": [
                {
                    "carrier_id": carrier_id,
                    "tracking_number": self.generate_tracking_number(),
                    "carrier_name": carrier_name
                }
            ],
            "order_id":int(order_id),
            "vendor_id": 7319
        }



        self.post( url='/central/mkpl/merchant/ref/summary/track',  headers=seller_header,json=data)
        assert self.response["result"]  is True

if __name__ == '__main__':
    # 用户输入订单ID
    order_id = int(input("请输入订单ID: ").strip())

    # 配置请求头
    seller_header = {
        "Authorization": "Bearer eyJraWQiOiI5Y2IzYjhiYS0xMGMxLTQxMGUtODg0OS1mNGFhZTU4Nzc0ZjMiLCJhbGciOiJSUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.BHrJExRvyeBmZSig7eLwLz5wLJ03a_8ZOJmW52uTuVZpAM2GMd0hnwaj2rs3qiRKprXZVuGQwf1ZsrKqTIaUFTqTSMlwPmu1v6WrlmyVoca1tLyfvi6eZyw1CUhuSpwYZy5yKg-j_JfR47L-cQ-zCuAXoGRnauiZgnQALvdjM7s",
        "Content-Type": "application/json"
    }

    # 配置基础URL
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    # 执行测试
    test_instance = TestSellerOrder()
    result = test_instance.test_seller_ship_order( seller_header=seller_header,order_id=order_id)
