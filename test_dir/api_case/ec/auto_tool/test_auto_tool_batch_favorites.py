# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""

import requests
import weeeTest
from weeeTest import log

from test_dir.api.ec.ec_item.my_favorite.api_favorites import ApiFavorites
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder
from qa_config.product_data import product_ids


class TestAutoToolBatchFavorites(weeeTest.TestCase):
    @weeeTest.mark.list('AutoTool', 'tb1','Transaction')
    def test_auto_tool_batch_favorites(self, ec_login_header):
        """ 我的清单-批量搜藏商品 (工具case，不需跑线上)"""
        # 1.获取登录header
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 读取data数据
        # 批量删除我的搜藏
        # 根据商品id集合批量取消我的收藏
        ApiFavorites().favorites_batch_batch(headers=ec_login_header, product_ids=product_ids)
        assert self.response["result"] is True
        # 添加/取消 我的收藏
        for product_id in product_ids:
            # 添加/取消 我的收藏
            ApiFavorites().favorites_set(headers=ec_login_header, target_id=product_id)
            assert self.response["result"] is True


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
