"""
<AUTHOR>  huimin.li
@Version        :  V1.0.0
------------------------------------
@File           :  test_pre_sale.py
@Description    :  create presale
@CreateTime     :  2025/3/21
@Software       :  PyCharm
------------------------------------
@ModifyTime     :
"""
import weeeTest

from qa_config.product_data import product_ids
from test_dir.api.ec.central_portal.central_im import CentralIm


class TestAutoToolPreSale(weeeTest.TestCase):
    @weeeTest.mark.list('Transaction', 'tb1', 'autotool')
    def test_auto_tool_pre_sale(self, sales_header):
        """创建预售活动"""
        product_id = [8715, 48518, 20774]
        start_time = "2025-3-25"
        end_time = "2025-3-29"

        CentralIm().create_pre_sale(headers=sales_header,
                                    product_id_0=product_id[0],
                                    product_id_1=product_id[1],
                                    product_id_2=product_id[2],
                                    sales_org_ids=1,
                                    start_time=start_time,
                                    end_time=end_time)
        CentralIm().start_pre_sale(headers=sales_header)


