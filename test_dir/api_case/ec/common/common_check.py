import random

import weeeTest
from weeeTest import jmespath

from test_dir.api.ec.ec_content.banner.cms_banner import CmsBanner
from test_dir.api.ec.ec_item.search_v3.search_by_ids_v3 import SearchByIdsV3
from test_dir.api.ec.ec_so.deliverydates.get_valid_delivery_dates import GetValidDeliveryDates
from test_dir.api.ec.ec_so.preorder.porder_date import PorderDate
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api_case.ec.common.comm_check_products_with_cart import CommCheckProductsWithCart
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction


class CommonCheck(weeeTest.TestCase):

    @staticmethod
    def list_check(list1, list2):
        """ 判断列表1是否存在于列表2中"""
        result = True
        for l1 in list1:
            status = True if l1 in list2 else False
            result = result and status

        return result

    @staticmethod
    def compute_total_price_in_cart_not_include_freight(sections: list):
        """
        return product total price in different carts including activity items but not include freight
        """
        return sum([float(section["fee_info"]["total_price_with_activity"]) for section in sections])

    @staticmethod
    def compute_total_price_in_cart_include_freight(sections: list):
        """
        return product total price in different carts including activity items but not include freight
        """
        return sum([float(section["fee_info"]["final_amount"]) for section in sections])

    @staticmethod
    def check_shipping_fee(account_level, product_amount: float):
        """
        return shipping fee according account level
        """

        if product_amount > 0 > (product_amount - 35):
            if 'Gold' == account_level:
                return 4.95
            if 'Silver' == account_level:
                return 5.95
            if 'Bronze' == account_level:
                return 6.95
        if product_amount >= 35 and (product_amount - 49) < 0:
            if 'Gold' == account_level:
                return 0
            if 'Silver' == account_level:
                return 5.95
            if 'Bronze' == account_level:
                return 6.95
        if product_amount >= 49 and (product_amount - 79) < 0:
            if 'Gold' == account_level:
                return 0
            if 'Silver' == account_level:
                return 0
            if 'Bronze' == account_level:
                return 0
        if (product_amount - 79) >= 0:
            return 0

    @staticmethod
    def check_service_fee(account_level, product_amount: float):
        """
        return service fee according account level
        """
        if product_amount > 0 > (product_amount - 35):
            if 'Gold' == account_level:
                return 0
            if 'Silver' == account_level:
                return 1.95
            if 'Bronze' == account_level:
                return 2.95
        if product_amount >= 35 and (product_amount - 49) < 0:
            if 'Gold' == account_level:
                return 0
            if 'Silver' == account_level:
                return 1.95
            if 'Bronze' == account_level:
                return 2.95
        if product_amount >= 49 and (product_amount - 79) < 0:
            if 'Gold' == account_level:
                return 0
            if 'Silver' == account_level:
                return 1.95
            if 'Bronze' == account_level:
                return 2.95
        if (product_amount - 79) >= 0:
            return 0

    @staticmethod
    def check_free_shipping_fee(account_level):
        """
        return service fee according account level
        """
        if 'Gold' == account_level:
            return 35.00
        if 'Silver' == account_level:
            return 49.00
        if 'Bronze' == account_level:
            return 49.00

    @staticmethod
    def check_product_info(headers, product: dict, category_type: str = "others", filters: str = "others",
                           source: str = "others"):
        # 验证product 信息公共断言方法
        assert product["biz_type"] is not None, f'商品{product["id"]}信息返回异常{product}'
        assert product["category"] is not None, f'商品{product["id"]}信息返回异常{product}'
        assert product["category_color"] is not None, f'商品{product["id"]}信息返回异常{product}'
        assert product["category_name"] is not None, f'商品{product["id"]}信息返回异常{product}'
        assert product["img_urls"] is not None, f'商品{product["id"]}信息返回异常{product}'
        assert product["img"] is not None, f'商品{product["id"]}信息返回异常{product} '
        assert "weeecdn" in product['img'], f'商品{product["id"]}信息返回异常{product} '
        assert product["price"] is not None, f'商品{product["id"]}信息返回异常{product} '
        if product["base_price"] is not None:
            assert product["base_price"] > product["price"], f'商品{product["id"]}信息返回异常{product}'
        assert product["parent_category"] is not None, f'商品{product["id"]}信息返回异常{product}'

        assert product["max_order_quantity"] is not None, f'商品{product["id"]}信息返回异常{product}'
        assert product["name"] is not None, f'商品{product["id"]}信息返回异常{product} '
        assert product["sales_max_order_quantity"] is not None, f'商品{product["id"]}信息返回异常{product}'
        assert product["sales_min_order_quantity"] is not None, f'商品{product["id"]}信息返回异常{product}'
        assert product["slug"] is not None, f'商品{product["id"]}信息返回异常{product} '
        assert str(product["id"]) in product['slug'], f'商品{product["id"]}信息返回异常{product}'
        assert product["sold_count"] is not None, f'商品{product["id"]}信息返回异常{product} '
        assert product["sold_status"] is not None, f'商品{product["id"]}信息返回异常{product} '
        # assert product["title"] is not None, f'商品{product["id"]}信息返回异常{product} '
        # assert product["title_en"] is not None, f'商品{product["id"]}信息返回异常{product}'
        # assert product["title_zh"] is not None, f'商品{product["id"]}信息返回异常{product}'
        # assert product["unit_range"] is not None, f'商品{product["id"]}信息返回异常{product}'
        assert product["view_link"] is not None, f'商品{product["id"]}信息返回异常{product} '
        assert str(product["id"]) in product["view_link"], f'商品{product["id"]}信息返回异常{product}'
        # pdp 链接
        CommCheckFunction().comm_check_link(product["view_link"], headers=headers)

        # 断言如果是来源首页，商品必是可售的
        if source in ("homepage", "trade_in", "perference_home"):
            assert product['sold_status'] == "available", f'商品{product["id"]}信息返回异常{product} '
        elif source in ("order_confirmation", ):
            # 订单成功页面曾经购买过滤预售商品
            assert product["is_presale"] is False
        elif source == "topx":
            pass
        elif source == "others":
            # 来源其他模块
            assert product["last_week_sold_count"] is not None, f'商品{product["id"]}信息返回异常{product}'
            if product["last_week_sold_count"] >= 50:
                assert product["last_week_sold_count_ui"] is not None, f'商品{product["id"]}信息返回异常{product}'
            # 如果有volume price

        # 断言如果是mkpl商品，商家信息不为空
        if product["biz_type"] == "seller":
            assert product["vender_id"] is not None, f'商品{product["id"]}信息返回异常{product} '
            assert product["vender_info_view"] != {}, f'商品{product["id"]}信息返回异常{product} '
            assert product.get("vender_info_view").get(
                "delivery_desc") is not None, f'商品{product["id"]}信息返回异常{product} '
            assert product.get("vender_info_view").get(
                "eta_range") is not None, f'商品{product["id"]}信息返回异常{product}'
            assert product.get("vender_info_view").get(
                "vender_name") is not None, f'商品{product["id"]}信息返回异常{product}'
            assert product.get("vender_info_view").get(
                "vender_id") is not None, f'商品{product["id"]}信息返回异常{product}'
            assert product["is_mkpl"] is True, f'商品{product["id"]}信息返回异常{product}'
        elif product["biz_type"] == "fbw":
            assert product["vender_id"] is not None, f'商品{product["id"]}信息返回异常{product} '
            # 每日现做tag
            # assert any(tag['tag_key'] == "freshly" for tag in product[
            #     "product_tag_list"]), f'商品{product["id"]}信息返回异常{product} '
        elif product["biz_type"] == "mkpl_fbw":
            pass
        elif product["biz_type"] == "normal":
            pass
            # assert product["ethnicity"] is not None, f'商品{product["id"]}信息返回异常{product}'

        if product.get("volume_price_support") is True:
            assert product["volume_price"] > product["price"], f'商品{product["id"]}信息返回异常{product}'

        if product.get("is_sponsored") is True:
            # 广告赞助商品
            assert product["sponsored_text"] is not None, f'商品{product["id"]}信息返回异常{product}'
            assert product["ads_creative"] is not None, f'商品{product["id"]}信息返回异常{product}'
        # 产品标签tag
        if product.get("entrance_tag") is not None:
            more_link = product["entrance_tag"]["more_link"]
            assert product["entrance_tag"]['tag_name'] is not None
            # 验证点击more_link 跳转正常
            CommCheckFunction().comm_check_link(more_link, headers=headers)

        if category_type == "new":
            # 如果这个商品不是广告商品，那么标签返回 new
            if product["is_sponsored"] is False:
                assert any(item["label_key"] == "new" for item in
                           product["label_list"]), f'商品{product["id"]}信息返回异常{product}'

        elif category_type == "sale":
            if product["is_sponsored"] is False:
                assert product["price"] is not None, f'商品{product["id"]}信息返回异常{product} '
                # 如果这个商品没有折扣，且不是广告商品，那么标签返回 每日低价
                if product["base_price"] is None and product["sponsored_text"] is None:
                    if len(product["label_list"]) > 0:
                        assert product["label_list"][0]["label_key"] == "EDLP_label" or product['label_list'][0][
                            "label_key"] == "off", f'商品{product["id"]}信息返回异常{product} '

                # 如果这个商品有折扣，且不是广告商品，那么标签返回 off
                if product["base_price"] is not None and product["sponsored_text"] is None:
                    assert product['base_price'] > product[
                        "price"], f'商品{product["id"]}信息返回异常{product} '
                    assert product["label_list"][0][
                               "label_key"] == "off", f'商品{product["id"]}信息返回异常{product} '
            # if len(product["activity_tag_list"]) > 0:
            #     assert any(item["tag_key"] == "kill_deal" for item in
            #                product["product_tag_list"]), f'商品{product["id"]}信息返回异常{product}'
        elif category_type == "trending":
            assert str(product["id"]) in product["slug"], f'商品{product["id"]}信息返回异常{product} '

        elif category_type == "alcohol":
            assert product["item_type"] == "alcohol", f'商品{product["id"]}信息返回异常{product} '
            assert "alcohol" in product["category"], f'商品{product["id"]}信息返回异常{product} '

        elif category_type in ("freshgourmet", "freshbakery"):
            # 每日现做tag, 这个断言不是每次都成立，先去，待以后优化
            # assert any(tag['tag_key'] for tag in product[
            #     "product_tag_list"]), f'商品{product["id"]}信息返回异常{product}'
            if product["brand_name"]:
                # 此处脚本断言失败，需要修复 --20241029
                # assert product[
                #            "brand_name"] is not None, f'这个商品{product["id"]}的brand_name不能为空, product={product}'
                assert product[
                           "brand_key"] is not None, f'这个商品{product["id"]}的brand_key不能为空, product={product}'
        elif category_type == "fruits":
            assert category_type in product[
                "category"], f'这个商品{product["id"]}的category{product["category"]}不是{category_type}, product={product}'
        elif category_type == "trade_in":
            # trade_in页面
            assert product["trade_in_price"] is not None, f"商品id：{product['id']}，商品信息：{product}"
            assert product["trade_in_price"] < product[
                "price"], f"商品id：{product['id']}，商品信息：{product}"
        elif category_type == "global+":
            assert product["biz_type"] == "seller", f"商品id：{product['id']}，商品信息：{product}"
        elif category_type == "others":
            # 不是分类页面
            pass
        else:
            assert category_type in product[
                "category"], f'这个商品{product["id"]}的category{product["category"]}不是{category_type}, product={product}'

        if filters == "delivery_type_pantry":
            assert product["is_pantry"] is True, f"商品id：{product['id']}，商品信息：{product}"
        elif filters == "delivery_type_global":
            assert product["is_mkpl"] is True, f"商品id：{product['id']}，商品信息：{product}"
        elif filters == "product_type_cold_pack":
            assert product["is_colding_package"] is True, f"商品id：{product['id']}，商品信息：{product}"

    @staticmethod
    def check_product_status_assertion(headers, deal_date, source, product: dict):
        # 验证商品状态公共断言方法
        # 获取用户的preorder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=headers)["object"]
        if product["sold_status"] == "sold_out":
            # 售罄状态
            assert product["restock_tip"] is not None
            CommCheckFunction().comm_set_favorites(headers=headers, target_id=product["id"])

        elif product["sold_status"] == "available":
            # 可加够状态
            CommCheckProductsWithCart().product_add_to_cart(headers=headers,
                                                            product=product, porder_deal_date=deal_date,
                                                            product_source=source)
        elif product["sold_status"] == "change_other_day":
            # 获取这个商品的可售日期
            product_date = GetValidDeliveryDates().so_delivery_date_item(headers=headers, product_id=product["id"])
            assert len(product_date["object"]["delivery"]) > 0, f'该商品：{product["id"]}没有返回可切换的日期'
            delivery = product_date["object"]["delivery"]
            # 获取随机日期
            random_date = delivery[random.randint(0, len(delivery) - 1)]['date']
            # 切换日期
            porder_date = PorderDate().porder_date(headers=headers, delivery_pickup_date=random_date)
            assert porder_date["result"] is True
            # 获取用户的preorder
            porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=headers)["object"]
        return porder

    @staticmethod
    def check_my_orders_assertion(myorder, filter_status):
        assert myorder["addr_city"] is not None, f'我的订单信息返回异常{myorder}'
        assert myorder["addr_country"] is not None, f'我的订单信息返回异常{myorder}'
        assert myorder["addr_sales_org"] is not None, f'我的订单信息返回异常{myorder}'
        assert myorder["addr_state"] is not None, f'我的订单信息返回异常{myorder}'
        assert myorder["addr_zipcode"] is not None, f'我的订单信息返回异常{myorder}'
        assert myorder["address"] is not None, f'我的订单信息返回异常{myorder}'
        assert myorder["alias"] is not None, f'我的订单信息返回异常{myorder}'
        assert myorder["biz_type"] is not None, f'我的订单信息返回异常{myorder}'
        assert myorder["final_amount"] is not None, f'我的订单信息返回异常{myorder}'

        if myorder["biz_type"] == "R-alcohol-0":
            assert myorder["order_alcohol_tips"] is not None, f'我的订单信息返回异常{myorder}'
            assert myorder["shipping_shipment_date"] is not None, f'我的订单信息返回异常{myorder}'
        elif myorder["biz_type"] == "R-normal-0":
            assert myorder["shipping_shipment_date"] is not None, f'我的订单信息返回异常{myorder}'
        elif myorder["biz_type"] == "S-normal-0":
            assert myorder["vendor_title"] is not None, f'我的订单信息返回异常{myorder}'
            assert myorder["vendor_logo_url"] is not None, f'我的订单信息返回异常{myorder}'
            assert myorder["vendor_id"] is not None, f'我的订单信息返回异常{myorder}'
            assert myorder["shipping_shipment_date"] is not None, f'我的订单信息返回异常{myorder}'
        elif myorder["biz_type"] == "V-giftcard-0":
            if filter_status != "1":
                assert myorder["btnList"] is None, f'我的订单信息返回异常{myorder}'

        elif myorder["biz_type"] == "V-point-0":
            pass
        assert len(myorder["products"]) > 0, f'我的订单信息返回异常{myorder}'
        values_to_check = []
        if filter_status == "all":
            assert myorder["status"] != "X", f'我的订单信息返回异常{myorder}'
        elif filter_status == "1":
            # 待支付
            assert myorder["status"] == "C", f'我的订单信息返回异常{myorder}'
            values_to_check = ["pay", "cancel"]
        elif filter_status == "2":
            # 待发货
            assert myorder["status"] == "P", f'我的订单信息返回异常{myorder}'
            values_to_check = ["buy_again", "modify_product", "cancel_order", "order_share"]
        elif filter_status == "3":
            # 已发货
            assert myorder["status"] == "F", f'我的订单信息返回异常{myorder}'
            if myorder["review_status"] == "no":
                # 待晒单
                values_to_check = ["buy_again", "post", "customer_support"]
            elif myorder["review_status"] == "all":
                # 已晒单
                values_to_check = ["buy_again", "edit_post", "customer_support"]
        elif filter_status == "6":
            # 待晒单
            assert myorder["status"] == "F", f'我的订单信息返回异常{myorder}'
            if myorder["review_status"] == "no":
                # 待晒单
                values_to_check = ["buy_again", "post", "customer_support"]
            elif myorder["review_status"] == "all":
                # 已晒单
                values_to_check = ["buy_again", "edit_post", "customer_support"]
        elif filter_status == "4":
            # 已取消
            assert myorder["status"] == "X", f'我的订单信息返回异常{myorder}'
            values_to_check = ["buy_again"]
        if myorder["sub_order_type"] == "group_buy":
            assert myorder["btnList"] is None, f'我的订单信息返回异常{myorder}'
        else:
            assert myorder["btnList"] is not None, f'我的订单信息返回异常{myorder}'
            # 断言不同订单状态按钮不一致
            assert all(value in myorder["btnList"] for value in values_to_check), f'我的订单信息返回异常{myorder}'
        assert myorder["shipping"] is not None, f'我的订单信息返回异常{myorder}'

    @staticmethod
    def check_order_detail(order_detail, order_id):
        assert order_detail["payment_category_label"] is not None, f"{order_id}订单详情里数据异常，请确认：{order_detail}"
        assert order_detail['title'] == 'Order Detail', f"{order_id}订单详情里数据异常，请确认：{order_detail}"
        assert order_detail['actual_total'] > 0, f"{order_id}订单详情里数据异常，请确认：{order_detail}"
        assert order_detail['actual_total_with_tax'] > 0, f"{order_id}订单详情里数据异常，请确认：{order_detail}"
        assert order_detail["order"]["order_id"] == order_id, f"{order_id}订单详情里数据异常，请确认：{order_detail}"
        assert len(order_detail["order"]['products']) > 0, f"{order_id}订单详情里数据异常，请确认：{order_detail}"
        for product in order_detail["order"]['products']:
            assert product["title"] is not None, f"{order_id}订单详情里数据异常，请确认：{order_detail}"
            assert product["image_url"] is not None, f"{order_id}订单详情里数据异常，请确认：{order_detail}"
            assert product["view_link"] is not None, f"{order_id}订单详情里数据异常，请确认：{order_detail}"
            assert product["product_image_url"] is not None, f"{order_id}订单详情里数据异常，请确认：{order_detail}"
            assert product["price"] is not None, f"{order_id}订单详情里数据异常，请确认：{order_detail}"
            assert product["quantity"] >= 1, f"{order_id}订单详情里数据异常，请确认：{order_detail}"
            if product["tag"] == "Alcohol":
                pass
        if order_detail["order"]["biz_type"] == "R-alcohol-0":
            # 包含酒订单
            assert order_detail["order_alcohol_tips"] is not None, f"{order_id}订单详情里数据异常，请确认：{order_detail}"
            assert "buy_again" in order_detail['btnList'], f"{order_id}订单详情里数据异常，请确认：{order_detail}"
            assert "customer_support" in order_detail['btnList'], f"{order_id}订单详情里数据异常，请确认：{order_detail}"
            assert order_detail["time_range_str"] is not None, f"{order_id}订单详情里数据异常，请确认：{order_detail}"
            assert order_detail["pickup_date_info"] is not None, f"{order_id}订单详情里数据异常，请确认：{order_detail}"

        elif order_detail["order"]["biz_type"] == "R-normal-0":
            # 普通生鲜订单
            assert "buy_again" in order_detail['btnList'], f"{order_id}订单详情里数据异常，请确认：{order_detail}"
            assert "customer_support" in order_detail['btnList'], f"{order_id}订单详情里数据异常，请确认：{order_detail}"
            assert order_detail["time_range_str"] is not None, f"{order_id}订单详情里数据异常，请确认：{order_detail}"
            assert order_detail["pickup_date_info"] is not None, f"{order_id}订单详情里数据异常，请确认：{order_detail}"

        elif order_detail["order"]["biz_type"] == "S-normal-0":
            # mkpl 订单
            assert "buy_again" in order_detail['btnList'], f"{order_id}订单详情里数据异常，请确认：{order_detail}"
            assert "customer_support" in order_detail['btnList'], f"{order_id}订单详情里数据异常，请确认：{order_detail}"
            assert order_detail["order"]["seller_id"] is not None, f"{order_id}订单详情里数据异常，请确认：{order_detail}"
            assert order_detail["order"][
                       "sub_order_type"] == "seller_mail", f"{order_id}订单详情里数据异常，请确认：{order_detail}"

        elif order_detail["order"]["biz_type"] == "V-giftcard-0":
            # 礼品卡订单、
            pass
            # assert "resend_gift_card" in order_detail['btnList'], f"{order_id}订单详情里数据异常，请确认：{order_detail}"

        elif order_detail["order"]["biz_type"] == "V-point-0":
            # 积分订单
            assert order_detail['btnList'] == [], f"{order_id}订单详情里数据异常，请确认：{order_detail}"

        assert order_detail["order"]['delivery_mode'] is not None, f"{order_id}订单详情里数据异常，请确认：{order_detail}"
        assert order_detail["order"]["final_amount"] is not None, f"{order_id}订单详情里数据异常，请确认：{order_detail}"

        # assert order_detail["order"]["service_fee_detail_list"] is not None, f"{order_id}订单详情里数据异常，请确认：{order_detail}"
        # assert order_detail["order"]["additional_fees"] is not None, f"{order_id}订单详情里数据异常，请确认：{order_detail}"
        assert order_detail["order_status_info"] is not None, f"{order_id}订单详情里数据异常，请确认：{order_detail}"
        assert order_detail["order_status_info"][
                   "title"] is not None, f"{order_id}订单详情里数据异常，请确认：{order_detail}"
        assert order_detail["order_status_info"][
                   "message"] is not None, f"{order_id}订单详情里数据异常，请确认：{order_detail}"
        # assert order_detail["time_range_str"] is not None, f"{order_id}订单详情里数据异常，请确认：{order_detail}"
        # assert order_detail["three_hour_tip"] is not None, f"{order_id}订单详情里数据异常，请确认：{order_detail}"

    @staticmethod
    def check_social_review_list(review_list, source, headers):
        assert review_list["comment"] is not None, f'晒单数据返回异常{review_list}，请确认'
        assert review_list["comment_lang"] is not None, f'晒单数据返回异常{review_list}，请确认'
        # assert review_list["external_tags"] is not None, f'晒单数据返回异常{review_list}，请确认'
        assert len(review_list["pictures"]) > 0, f'晒单数据返回异常，请确认{review_list}'
        assert review_list["id"] is not None, f'晒单数据返回异常，请确认{review_list}'
        assert review_list["link"] is not None, f'晒单数据返回异常{review_list}，请确认'
        if source == "seller":
            assert review_list["product"]["biz_type"] == "seller", f'晒单数据返回异常，请确认{review_list}'
            assert "/social/" + str(review_list["id"]) in review_list[
                "link"], f'晒单数据返回异常，请确认{review_list}'
            assert review_list["product"] is not None, f'晒单数据返回异常{review_list}，请确认'
        elif source == "home":
            assert "/social/review/" + str(review_list["id"]) in review_list[
                "link"], f'晒单数据返回异常，请确认{review_list}'

        elif source == "pdp":
            assert "/social/review/more/" + str(review_list["product_id"]) in review_list[
                "link"], f'晒单数据返回异常，请确认{review_list}'

        # 晒单链接
        CommCheckFunction().comm_check_link(review_list["link"], headers=headers), f'晒单数据返回异常{review_list}，请确认'
        assert len(review_list["pictures"]) > 0, f'晒单数据返回异常{review_list}，请确认'
        assert review_list["product_id"] is not None, f'晒单数据返回异常{review_list}，请确认'
        # assert review_list["product_image_url"] is not None, f'晒单数据返回异常{review_list}，请确认'
        # assert review_list["product_title"] is not None, f'晒单数据返回异常{review_list}，请确认'
        # assert review_list["title"] is not None, f'晒单数据返回异常{review_list}，请确认'
        # assert review_list["title_lang"] is not None, f'晒单数据返回异常{review_list}，请确认'
        assert review_list["type"] == "review", f'晒单数据返回异常{review_list}，请确认'
        assert review_list["show_url"] is not None, f'晒单数据返回异常，请确认{review_list}'
        assert review_list["user_name"] is not None, f'晒单数据返回异常，请确认{review_list}'
        assert review_list["user_avatar"] is not None, f'晒单数据返回异常，请确认{review_list}'
        assert review_list["start_id"] is not None, f'晒单数据返回异常，请确认{review_list}'

    @staticmethod
    def check_video_data(video_list, headers):
        assert video_list.get("comment") is not None, f'video 信息{video_list.get("id")}返回异常{video_list}'
        # assert video_list.get("content_type") == "review", f'video 信息{video_list.get("id")}返回异常{video_list}'
        assert video_list.get("id") is not None, f'video 信息{video_list.get("id")}返回异常{video_list}'
        assert video_list.get("link") is not None, f'video 信息{video_list.get("id")}返回异常{video_list}'
        assert "/social/video/" + str(video_list.get("id")) in video_list.get(
            "link"), f'video 信息{video_list.get("id")}返回异常{video_list}'
        # 点击视频进入视频详情
        CommCheckFunction().comm_check_link(video_list.get("link"), headers=headers)
        assert len(video_list.get("pictures")) > 0, f'video 信息{video_list.get("id")}返回异常{video_list}'
        assert video_list.get("type") == "video", f'video 信息{video_list.get("id")}返回异常{video_list}'
        assert video_list.get("title") is not None, f'video 信息{video_list.get("id")}返回异常{video_list}'
        assert video_list.get("user_avatar") is not None, f'video 信息{video_list.get("id")}返回异常{video_list}'
        assert video_list.get("user_badge") is not None, f'video 信息{video_list.get("id")}返回异常{video_list}'
        assert video_list.get("user_id") is not None, f'video 信息{video_list.get("id")}返回异常{video_list}'
        assert video_list.get("user_name") is not None, f'video 信息{video_list.get("id")}返回异常{video_list}'
        assert video_list.get("like_count") >= 0, f'video 信息{video_list.get("id")}返回异常{video_list}'
        if video_list.get("hash_tags"):
            hash_tags = video_list.get("hash_tags")
            for hash_tag in hash_tags:
                assert hash_tag.get("label"), f'video 信息{video_list.get("id")}返回异常{video_list}'
                assert hash_tag.get("tag_id"), f'video 信息{video_list.get("id")}返回异常{video_list}'
                # assert hash_tag.get("url"), f'video 信息{video_list.get("id")}返回异常{video_list}'
                # assert "/social/hashtag/detail/" + str(hash_tag.get("tag_id")) in hash_tag.get(
                #     "url"), f'video 信息{video_list.get("id")}返回异常{video_list}'

    @staticmethod
    def check_home_component_assert(component, headers):
        # 首页组件
        if component["component_instance_key"] == "cm_search_bar":
            assert component["properties"]["tips"] is not None, f'首页组件信息异常{component}'
        elif component["component_instance_key"] == "cm_main_banner":
            pass

        elif component["component_instance_key"] == "cm_top_message":
            pass
        elif component["component_instance_key"] == "cm_notice_banner":
            pass
        elif component["component_instance_key"] == "cm_categories":
            pass
        elif component["component_instance_key"] == "cm_banner_array":
            pass
        elif component["component_instance_key"] == "cm_banner_line":
            pass

        elif component["component_instance_key"] == "cm_virtual_recommend":
            pass

        elif component["component_instance_key"] == "cm_item_basket_starter":
            assert component["properties"]["title"] is not None, f'首页组件信息异常{component}'
            assert component["properties"]["event_key"] is not None, f'首页组件信息异常{component}'
            assert component["properties"]["more_link"] is not None, f'首页组件信息异常{component}'
            assert "/promotion/collection/" in component["properties"]["more_link"], f'首页组件信息异常{component}'
            CommCheckFunction().comm_check_link(component["properties"]["more_link"], headers=headers)

        elif component["component_instance_key"] == "cm_item_editors_pick":
            assert component["properties"]["title"] is not None, f'首页组件信息异常{component}'
            assert component["properties"]["event_key"] is not None, f'首页组件信息异常{component}'

        elif component["component_instance_key"] == "cm_item_trending":
            assert component["properties"]["title"] is not None, f'首页组件信息异常{component}'
            assert component["properties"]["event_key"] is not None, f'首页组件信息异常{component}'
            assert component["properties"]["more_link"] is not None, f'首页组件信息异常{component}'
            assert "/category/trending" in component["properties"]["more_link"]
            CommCheckFunction().comm_check_link(component["properties"]["more_link"], headers=headers)

        elif component["component_instance_key"] == "cm_item_sale":
            assert component["properties"]["title"] is not None, f'首页组件信息异常{component}'
            assert component["properties"]["event_key"] is not None, f'首页组件信息异常{component}'
            assert component["properties"]["more_link"] is not None, f'首页组件信息异常{component}'
            assert "/category/sale" in component["properties"]["more_link"], f'首页组件信息异常{component}'
            CommCheckFunction().comm_check_link(component["properties"]["more_link"], headers=headers)

        elif component["component_instance_key"] == "cm_item_new":
            assert component["properties"]["title"] is not None, f'首页组件信息异常{component}'
            assert component["properties"]["event_key"] is not None, f'首页组件信息异常{component}'
            assert component["properties"]["more_link"] is not None, f'首页组件信息异常{component}'
            assert "/category/new" in component["properties"]["more_link"], f'首页组件信息异常{component}'
            CommCheckFunction().comm_check_link(component["properties"]["more_link"], headers=headers)

        elif component["component_instance_key"] == "cm_lightning_deals":
            assert component["properties"]["title"] is not None, f'首页组件信息异常{component}'
            assert component["properties"]["link_url"] is not None, f'首页组件信息异常{component}'
            assert component["properties"]["more_link"] is not None, f'首页组件信息异常{component}'
            assert "/promotion/lightning-deals" in component["properties"]["more_link"], f'首页组件信息异常{component}'
            CommCheckFunction().comm_check_link(component["properties"]["more_link"], headers=headers)

        elif component["component_instance_key"] == "cm_mkpl_lightning":
            assert component["properties"]["title"] is not None, f'首页组件信息异常{component}'
            assert component["properties"]["link_url"] is not None, f'首页组件信息异常{component}'
            assert component["properties"]["more_link"] is not None, f'首页组件信息异常{component}'
            assert "/mkpl/lightning-deals" in component["properties"]["more_link"], f'首页组件信息异常{component}'
            CommCheckFunction().comm_check_link(component["properties"]["more_link"], headers=headers)

        elif component["component_instance_key"] == "cm_product_line_tabs_fresh_daily":
            assert component["properties"]["event_key"] is not None, f'首页组件信息异常{component}'
        elif component["component_instance_key"] == "cm_collection_v2_manual_rank1":
            assert component["properties"]["event_key"] is not None, f'首页组件信息异常{component}'

        elif component["component_instance_key"] == "cm_content_feed":
            assert component["properties"]["title"] is not None, f'首页组件信息异常{component}'
            assert component["properties"]["event_key"] is not None, f'首页组件信息异常{component}'

        elif component["component_instance_key"] == "cm_item_perference":
            assert "/category/perference" in component["properties"]["more_link"], f'首页组件信息异常{component}'
            CommCheckFunction().comm_check_link(component["properties"]["more_link"], headers=headers)
            assert component["properties"]["title"] is not None, f'首页组件信息异常{component}'
            assert component["properties"]["event_key"] is not None, f'首页组件信息异常{component}'
        elif component["component_instance_key"] == "cm_post_list":
            assert component["properties"]["title"] is not None, f'首页组件信息异常{component}'
        # 合集组件
        if component["component_key"] == "cm_collection":
            assert component["properties"]["more_link"] is not None, f'首页组件信息异常{component}'
            assert component["properties"]["event_key"] is not None, f'首页组件信息异常{component}'
            CommCheckFunction().comm_check_link(component["properties"]["more_link"], headers=headers)

    @staticmethod
    def check_fbw_component_assert(component, page_key):
        # FBW页面组件
        # 本地新鲜熟食卤味
        if component["component_instance_key"] == "cm_title_rich":
            assert component["properties"]["sub_title"] is not None, f'cms {page_key}组件信息异常{component}'
            assert component["properties"]["title_style"] is not None, f'cms {page_key}组件信息异常{component}'
            assert component["properties"]["background"] is not None, f'cms {page_key}组件信息异常{component}'
            assert component["properties"]["sub_title_style"] is not None, f'cms {page_key}组件信息异常{component}'
            assert component["properties"]["title"] is not None, f'cms {page_key}组件信息异常{component}'
            assert component["properties"]["desc"] is not None, f'cms {page_key}组件信息异常{component}'
            assert component["properties"]["event_key"] is not None, f'cms {page_key}组件信息异常{component}'

        elif component["component_instance_key"] == "cm_text_array":
            assert component["properties"]["text_array"] is not None, f'cms {page_key}组件信息异常{component}'
            assert component["properties"]["event_key"] is not None, f'cms {page_key}组件信息异常{component}'
        # 新品上架
        elif component["component_instance_key"] == "cm_product_line_new":
            assert component["properties"]["title_style"] is not None, f'cms {page_key}组件信息异常{component}'
            assert component["properties"]["more_link"] is not None, f'cms {page_key}组件信息异常{component}'
            if page_key == "bakery":
                assert "/promotion/collection/bakeryfreshdailynew" in component["properties"][
                    "more_link"], f'cms {page_key}组件信息异常{component}'
            elif page_key == "freshdeli":
                assert "/promotion/collection/freshdeliynew" in component["properties"][
                    "more_link"], f'cms {page_key}组件信息异常{component}'
            assert component["properties"]["title"] is not None, f'cms {page_key}组件信息异常{component}'
            assert component["properties"]["event_key"] is not None, f'cms {page_key}组件信息异常{component}'
        # 再次购买
        elif component["component_instance_key"] == "cm_product_line_buy_again":
            assert component["properties"]["title_style"] is not None, f'cms {page_key}组件信息异常{component}'
            assert component["properties"]["more_link"] is not None, f'cms {page_key}组件信息异常{component}'
            if page_key == "bakery":
                assert "/promotion/collection/bakerybuyagain" in component["properties"][
                    "more_link"], f'cms {page_key}组件信息异常{component}'
            elif page_key == "freshdeli":
                assert "/promotion/collection/freshdelibuyagain" in component["properties"][
                    "more_link"], f'cms {page_key}组件信息异常{component}'
            assert component["properties"]["title"] is not None, f'cms {page_key}组件信息异常{component}'
            assert component["properties"]["event_key"] is not None, f'cms {page_key}组件信息异常{component}'
        # 选择餐馆
        elif component["component_instance_key"] == "cm_nav_line":
            assert component["properties"]["title_style"] is not None, f'cms {page_key}组件信息异常{component}'
            assert component["properties"]["title"] is not None, f'cms {page_key}组件信息异常{component}'
            assert component["properties"]["event_key"] is not None, f'cms {page_key}组件信息异常{component}'
        # 所有菜品
        elif component["component_instance_key"] == "cm_content_feed_v2":
            assert component["properties"]["title_style"] is not None, f'cms {page_key}组件信息异常{component}'
            assert component["properties"]["title"] is not None, f'cms {page_key}组件信息异常{component}'
            assert component["properties"]["event_key"] is not None, f'cms {page_key}组件信息异常{component}'

    @staticmethod
    def check_share_content(content, source, args: dict = None):
        # 分享内容
        share_tips = content.get("share_tips")
        show_language = content.get("show_language")
        share_content = content.get("share_content")
        share_channels = content.get("share_channels")
        view_link = content.get("view_link")
        meta_properties = content.get("meta_properties")

        assert len(share_channels) > 0, f'{source} 分享信息返回异常，请确认{content}'
        channels_to_check = [
            "sms", "messenger", "whatsapp", "WeChatSession", "line",
            "kakao", "facebook", "WeChatMoments", "copyLink", "more"
        ]
        for channel in channels_to_check:
            assert channel in share_channels, f'{source} 分享信息返回异常，请确认{content}'

        for content in share_content:
            assert content["share_img_url"] is not None, f'{source}分享信息返回异常，请确认{content}'
            assert content["title"] is not None, f'{source} 分享信息返回异常，请确认{content}'
            assert content["link_url"] is not None, f'{source} 分享信息返回异常，请确认{content}'
            assert content["language"] is not None, f'{source} 分享信息返回异常，请确认{content}'
            if source == "pdp":
                assert content["description"] is not None, f'{source} 分享信息返回异常，请确认{content}'
                assert str(args["product_id"]) in content["link_url"], f'{source} 分享信息返回异常，请确认{content}'
            elif source == "freshdeli":
                assert content["description"] is not None, f'{source} 分享信息返回异常，请确认{content}'
                assert "/mkpl/freshdeli/landing" in content["link_url"], f'{source} 分享信息返回异常，请确认{content}'
            elif source == "bakery":
                assert content["description"] is not None, f'{source} 分享信息返回异常，请确认{content}'
                assert "/mkpl/bakery/landing" in content["link_url"], f'{source} 分享信息返回异常，请确认{content}'
            elif source == "global_plus":
                assert content["description"] is not None, f'{source} 分享信息返回异常，请确认{content}'
                assert "/mkpl/coupon/landing" in content["link_url"], f'{source} 分享信息返回异常，请确认{content}'
            elif source == "waterfall":
                assert content["description"] is not None, f'{source} 分享信息返回异常，请确认{content}'
                assert "/mkpl/global" in content["link_url"], f'{source} 分享信息返回异常，请确认{content}'
            elif source == "level_coupon":
                # assert content["description"] is not None, f'{source} 分享信息返回异常，请确认{content}'
                assert "/mkpl/coupon" in content["link_url"], f'{source} 分享信息返回异常，请确认{content}'
            elif source == "topx":
                assert content["description"] is not None, f'{source} 分享信息返回异常，请确认{content}'
                assert "/promotion/top-x/chart" in content["link_url"], f'{source} 分享信息返回异常，请确认{content}'
            elif source == "collection_brand":
                assert content["description"] is not None, f'{source} 分享信息返回异常，请确认{content}'
                assert "/brand/landing" in content["link_url"], f'{source} 分享信息返回异常，请确认{content}'
            elif source == "brand":
                assert content["description"] is not None, f'{source} 分享信息返回异常，请确认{content}'
                assert "/brand/detail/" + str(args["brand_slug"]) + "/" + str(args["brand_key"]) in content[
                    "link_url"], f'{source} 分享信息返回异常，请确认{content}'
            elif source == "collection_theme":
                assert content["description"] is not None, f'{source} 分享信息返回异常，请确认{content}'
                assert "/promotion/theme_landing" in content["link_url"], f'{source} 分享信息返回异常，请确认{content}'
            elif source == "raffle":
                pass
            elif source == "vendor":
                assert content["description"] is not None, f'{source} 分享信息返回异常，请确认{content}'
                assert "/mkpl/vendor" in content["link_url"], f'{source} 分享信息返回异常，请确认{content}'
            elif source == "group_by":
                assert content["description"] is not None, f'{source} 分享信息返回异常，请确认{content}'
                assert "/mkpl/group-order/landing" in content["link_url"], f'{source} 分享信息返回异常，请确认{content}'
            elif source == "group_by_bill":
                assert "/mkpl/group-order/detail/view" in content[
                    "link_url"], f'{source} 分享信息返回异常，请确认{content}'
            elif source == "lighting_deals":
                assert content["description"] is not None, f'{source} 分享信息返回异常，请确认{content}'
                assert "/promotion/lightning-deals" in content["link_url"], f'{source} 分享信息返回异常，请确认{content}'
            elif source == "mkpl_lighting_deals":
                assert content["description"] is not None, f'{source} 分享信息返回异常，请确认{content}'
                assert "/mkpl/lightning-deals" in content["link_url"], f'{source} 分享信息返回异常，请确认{content}'
            elif source == "topic":
                assert content["description"] is not None, f'{source} 分享信息返回异常，请确认{content}'
                assert "/topic" in content["link_url"], f'{source} 分享信息返回异常，请确认{content}'
            elif source == "hero":
                assert content["description"] is not None, f'{source} 分享信息返回异常，请确认{content}'
                assert "/product-favorite" in content["link_url"], f'{source} 分享信息返回异常，请确认{content}'
            elif source == "affiliate":
                assert content["description"] is not None, f'{source} 分享信息返回异常，请确认{content}'
                assert "/account/affiliate/landing" in content["link_url"], f'{source} 分享信息返回异常，请确认{content}'

        if source == "pdp":
            # 返利联盟文案
            assert show_language is True, f'{source} 分享信息返回异常，请确认{content}'
            assert share_tips is not None, f'{source} 分享信息返回异常，请确认{content}'
            assert "saveImage" in share_channels, f'{source} 分享信息返回异常，请确认{content}'
            assert "/product/share_view" in view_link, f'{source} 分享信息返回异常，请确认{content}'
        elif source == "freshdeli":

            assert show_language is True, f'{source} 分享信息返回异常，请确认{content}'
            assert meta_properties is not None, f'{source} 分享信息返回异常，请确认{content}'
            assert "/mkpl/freshdeli/landing" in meta_properties["fb_url"], f'{source} 分享信息返回异常，请确认{content}'
            assert meta_properties[
                       "fb_title"] == "Weee! local fresh gourmet | restaurant food delivered", f'{source} 分享信息返回异常，请确认{content}'
        elif source == "bakery":

            assert show_language is True, f'{source} 分享信息返回异常，请确认{content}'
            assert meta_properties is not None, f'{source} 分享信息返回异常，请确认{content}'
            assert "/mkpl/bakery/landing" in meta_properties["fb_url"], f'{source} 分享信息返回异常，请确认{content}'
            assert meta_properties[
                       "fb_title"] == "Weee! Local Fresh Bakery | Gourmet bakery goods, delivered", f'{source} 分享信息返回异常，请确认{content}'
        elif source == "topx":

            assert show_language is True, f'{source} 分享信息返回异常，请确认{content}'
            assert meta_properties is not None, f'{source} 分享信息返回异常，请确认{content}'
            assert "/promotion/top-x/chart" in meta_properties["fb_url"], f'{source} 分享信息返回异常，请确认{content}'
            assert meta_properties["fb_title"] == "Top Charts", f'{source} 分享信息返回异常，请确认{content}'
        elif source == "topx_detail":

            assert show_language is True, f'{source} 分享信息返回异常，请确认{content}'
            assert meta_properties is not None, f'{source} 分享信息返回异常，请确认{content}'
            assert "/promotion/top-x/" in meta_properties["fb_url"], f'{source} 分享信息返回异常，请确认{content}'
        elif source == "collection_brand":

            assert show_language is False, f'{source} 分享信息返回异常，请确认{content}'
            assert meta_properties is not None, f'{source} 分享信息返回异常，请确认{content}'
            assert "/brand/landing" in meta_properties["fb_url"], f'{source} 分享信息返回异常，请确认{content}'
        elif source == "brand":
            # meta_properties 为null

            assert show_language is True, f'{source} 分享信息返回异常，请确认{content}'
            assert "/brand/" + str(args["brand_slug"]) + "/" + str(
                args["brand_key"]) in view_link, f'{source} 分享信息返回异常，请确认{content}'
        elif source == "collection_theme":
            # meta_properties 为null

            assert "/promotion/theme_landing" in view_link, f'{source} 分享信息返回异常，请确认{content}'
        elif source == "global_plus":

            assert show_language is True, f'{source} 分享信息返回异常，请确认{content}'
            assert meta_properties is not None, f'{source} 分享信息返回异常，请确认{content}'
            assert "/mkpl/coupon/landing" in meta_properties["fb_url"], f'{source} 分享信息返回异常，请确认{content}'
        elif source == "waterfall":
            assert show_language is True, f'{source} 分享信息返回异常，请确认{content}'
        elif source == "level_coupon":
            assert show_language is True, f'{source} 分享信息返回异常，请确认{content}'
            assert "/mkpl/coupon" in meta_properties["fb_url"], f'{source} 分享信息返回异常，请确认{content}'
        elif source == "raffle":
            pass
        elif source == "vendor":

            assert show_language is True, f'{source} 分享信息返回异常，请确认{content}'
            assert meta_properties is not None, f'{source} 分享信息返回异常，请确认{content}'
            assert "/mkpl/vendor/" in meta_properties["fb_url"], f'{source} 分享信息返回异常，请确认{content}'
            assert "/mkpl/vendor/detail" in view_link, f'{source} 分享信息返回异常，请确认{content}'
        elif source == "group_by":

            assert show_language is True, f'{source} 分享信息返回异常，请确认{content}'
            assert meta_properties is not None, f'{source} 分享信息返回异常，请确认{content}'
            assert "/mkpl/group-order/landing" in meta_properties[
                "fb_url"], f'{source} 分享信息返回异常，请确认{content}'
            assert "/mkpl/group-order/landing" in view_link, f'{source} 分享信息返回异常，请确认{content}'
        elif source == "group_by_bill":

            assert show_language is True, f'{source} 分享信息返回异常，请确认{content}'
            assert meta_properties is not None, f'{source} 分享信息返回异常，请确认{content}'
            assert "/mkpl/group-order/detail/view" in meta_properties[
                "fb_url"], f'{source} 分享信息返回异常，请确认{content}'
            assert "/mkpl/group-order/detail/view" in view_link, f'{source} 分享信息返回异常，请确认{content}'
        elif source == "lighting_deals":
            assert show_language is True, f'{source} 分享信息返回异常，请确认{content}'
        elif source == "mkpl_lighting_deals":

            assert show_language is True, f'{source} 分享信息返回异常，请确认{content}'
            assert "/mkpl/lightning-deals" in meta_properties["fb_url"], f'{source} 分享信息返回异常，请确认{content}'
        elif source == "topic":

            assert show_language is True, f'{source} 分享信息返回异常，请确认{content}'
            assert "/topic" in meta_properties["fb_url"], f'{source} 分享信息返回异常，请确认{content}'
        elif source == "hero":

            assert show_language is True, f'{source} 分享信息返回异常，请确认{content}'
            assert "/product-favorite" in meta_properties["fb_url"], f'{source} 分享信息返回异常，请确认{content}'
        elif source == "affiliate":

            assert show_language is True, f'{source} 分享信息返回异常，请确认{content}'
            assert "/account/affiliate/landing" in meta_properties[
                "fb_url"], f'{source} 分享信息返回异常，请确认{content}'

        if meta_properties is not None:
            assert meta_properties["fb_url"] is not None, f'{source} 分享信息返回异常，请确认{content}'
            assert meta_properties["fb_title"] is not None, f'{source} 分享信息返回异常，请确认{content}'
            assert meta_properties["fb_image"] is not None, f'{source} 分享信息返回异常，请确认{content}'
            # assert meta_properties["fb_description"] is not None, f'{source} 分享信息返回异常，请确认{content}'
            # assert meta_properties["fb_width"] is not None, f'{source} 分享信息返回异常，请确认{content}'
            # assert meta_properties["fb_height"] is not None, f'{source} 分享信息返回异常，请确认{content}'
        # if view_link is not None:
        #     CommCheckFunction().comm_check_link(view_link)

    def check_cms_data_page(self, headers, cms_data, porder, page_key):
        # cms 页面组件
        sections = cms_data["layout"]["sections"]
        page_param = cms_data["layout"]["page_param"]
        data_source = cms_data["datasource"]
        for section in sections:
            # components = section["components"]
            for component in section["components"]:
                if len(component["datasource"]) > 0:
                    datasource_ds = component["datasource"][0]
                    component_link = data_source['{}'.format(datasource_ds)]['now']
                    self.get(headers=headers, url=component_link,
                             params={'dataobject_key': datasource_ds})
                    cms_detail = self.response
                    assert cms_detail["result"] is True
                    # print(cms_detail)

                # Banner 组件
                if component["component_key"] == "cm_banner":
                    assert component["datasource"] != [], f'cms {page_key}页面数据异常，请确认{cms_data}'
                    assert component["properties"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'
                    assert "cm_banner" in component["properties"]["event_key"], f'cms {page_key}页面数据异常，请确认{cms_data}'

                    # datasource_ds = component["datasource"][0]
                    # component_link = data_source['{}'.format(datasource_ds)]['now']
                    # self.get(headers=headers, url=component_link,
                    #          params={'dataobject_key': datasource_ds})
                    # cms_detail = self.response
                    #
                    # print(cms_detail)
                    cms_banner = CmsBanner().cms_banner(headers=headers, zipcode=porder["zipcode"],
                                                        date=porder["delivery_pickup_date"],
                                                        dataobject_key=component["datasource"][0])
                    assert cms_banner["object"]["banner_list"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'
                    for banner_list in cms_banner["object"]["banner_list"]:
                        assert banner_list["img_url"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'
                        if banner_list["link_url"] is not None:
                            # 点击跳转
                            CommCheckFunction().comm_check_link(banner_list["link_url"], headers=headers)

                    assert cms_banner["object"]["carousel"] is not None
                    for carousel in cms_banner["object"]["carousel"]:
                        assert carousel["img_url"] is not None
                        if carousel["link_url"] is not None:
                            # 点击跳转
                            CommCheckFunction().comm_check_link(carousel["link_url"], headers=headers)

                elif component["component_key"] == "cm_carousel_banner":
                    assert component["properties"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'
                    assert "cm_carousel_banner" in component["properties"]["event_key"], f'cms {page_key}页面数据异常，请确认{cms_data}'

                    cms_banner = CmsBanner().cms_banner(headers=headers, zipcode=porder["zipcode"],
                                                        date=porder["delivery_pickup_date"],
                                                        dataobject_key=component["datasource"][0])
                    assert cms_banner["object"]["banner_list"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'
                    for banner_list in cms_banner["object"]["banner_list"]:
                        assert banner_list["img_url"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'
                        if banner_list["link_url"] is not None:
                            # 点击跳转
                            CommCheckFunction().comm_check_link(banner_list["link_url"], headers=headers)

                    assert cms_banner["object"]["carousel"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'
                    for carousel in cms_banner["object"]["carousel"]:
                        assert carousel["img_url"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'
                        if carousel["link_url"] is not None:
                            # 点击跳转
                            CommCheckFunction().comm_check_link(carousel["link_url"], headers=headers)
                elif component["component_key"] == "cm_banner_container":
                    assert component["properties"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'
                    assert "cm_banner_container" in component["properties"]["event_key"], f'cms {page_key}页面数据异常，请确认{cms_data}'
                    cms_banner = CmsBanner().cms_banner(headers=headers, zipcode=porder["zipcode"],
                                                        date=porder["delivery_pickup_date"],
                                                        dataobject_key=component["datasource"][0])
                    assert cms_banner["object"]["banner_list"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'
                    for banner_list in cms_banner["object"]["banner_list"]:
                        assert banner_list["img_url"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'
                        if banner_list["link_url"] is not None:
                            # 点击跳转
                            CommCheckFunction().comm_check_link(banner_list["link_url"], headers=headers)

                    assert cms_banner["object"]["carousel"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'
                    for carousel in cms_banner["object"]["carousel"]:
                        assert carousel["img_url"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'
                        if carousel["link_url"] is not None:
                            # 点击跳转
                            CommCheckFunction().comm_check_link(carousel["link_url"], headers=headers)
                # 商品组件
                elif component["component_key"] == "cm_product_line":
                    assert component["properties"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'
                    assert "cm_product_line" in component["properties"]["event_key"], f'cms {page_key}页面数据异常，请确认{cms_data}'

                    # 遍历该部分中的datasource数组
                    for datasource_key in component['datasource']:
                        # 检查datasource键是否在datasource字典中
                        if datasource_key in data_source:
                            # 提取'now' URL
                            now_url = data_source[datasource_key]['now']
                            # 在URL中查找ids参数
                            if 'ids=' in now_url:
                                # 提取ids的值
                                ids_value = now_url.split('ids=')[1].split('&')[0]
                                print(ids_value)
                                ids_cms_v3 = SearchByIdsV3().search_by_ids_cms_v3(headers=headers,
                                                                                  zipcode=porder["zipcode"],
                                                                                  source=component["datasource"][0],
                                                                                  ids=ids_value,
                                                                                  date=porder["delivery_pickup_date"],
                                                                                  dataobject_key=
                                                                                  component["datasource"][0])
                                assert ids_cms_v3["result"] is True
                elif component["component_key"] == "cm_product_array":
                    assert component["properties"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'
                    assert "cm_product_array" in component["properties"]["event_key"], f'cms {page_key}页面数据异常，请确认{cms_data}'
                    # 遍历该部分中的datasource数组
                    for datasource_key in component['datasource']:
                        # 检查datasource键是否在datasource字典中
                        if datasource_key in data_source:
                            # 提取'now' URL
                            now_url = data_source[datasource_key]['now']
                            # 在URL中查找ids参数
                            if 'ids=' in now_url:
                                # 提取ids的值
                                ids_value = now_url.split('ids=')[1].split('&')[0]
                                print(ids_value)
                                ids_cms_v3 = SearchByIdsV3().search_by_ids_cms_v3(headers=headers,
                                                                                  zipcode=porder["zipcode"],
                                                                                  source=component["datasource"][0],
                                                                                  ids=ids_value,
                                                                                  date=porder["delivery_pickup_date"],
                                                                                  dataobject_key=
                                                                                  component["datasource"][0])
                                assert ids_cms_v3["result"] is True
                elif component["component_key"] == "cm_product_list":
                    assert component["properties"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'
                    assert "cm_product_list" in component["properties"]["event_key"], f'cms {page_key}页面数据异常，请确认{cms_data}'
                    # 遍历该部分中的datasource数组
                    for datasource_key in component['datasource']:
                        # 检查datasource键是否在datasource字典中
                        if datasource_key in data_source:
                            # 提取'now' URL
                            now_url = data_source[datasource_key]['now']
                            # 在URL中查找ids参数
                            if 'ids=' in now_url:
                                # 提取ids的值
                                ids_value = now_url.split('ids=')[1].split('&')[0]
                                print(ids_value)
                                ids_cms_v3 = SearchByIdsV3().search_by_ids_cms_v3(headers=headers,
                                                                                  zipcode=porder["zipcode"],
                                                                                  source=component["datasource"][0],
                                                                                  ids=ids_value,
                                                                                  date=porder["delivery_pickup_date"],
                                                                                  dataobject_key=
                                                                                  component["datasource"][0])
                                assert ids_cms_v3["result"] is True
                elif component["component_key"] == "cm_product_single":
                    assert component["properties"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'
                    assert "cm_product_single" in component["properties"]["event_key"], f'cms {page_key}页面数据异常，请确认{cms_data}'
                    # 遍历该部分中的datasource数组
                    for datasource_key in component['datasource']:
                        # 检查datasource键是否在datasource字典中
                        if datasource_key in data_source:
                            # 提取'now' URL
                            now_url = data_source[datasource_key]['now']
                            # 在URL中查找ids参数
                            if 'ids=' in now_url:
                                # 提取ids的值
                                ids_value = now_url.split('ids=')[1].split('&')[0]
                                print(ids_value)
                                ids_cms_v3 = SearchByIdsV3().search_by_ids_cms_v3(headers=headers,
                                                                                  zipcode=porder["zipcode"],
                                                                                  source=component["datasource"][0],
                                                                                  ids=ids_value,
                                                                                  date=porder["delivery_pickup_date"],
                                                                                  dataobject_key=
                                                                                  component["datasource"][0])
                                assert ids_cms_v3["result"] is True, f'cms {page_key}页面数据异常，请确认{cms_data}'
                elif component["component_key"] == "cm_product_waterfall":
                    assert component["properties"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'
                    assert "cm_product_waterfall" in component["properties"]["event_key"], f'cms {page_key}页面数据异常，请确认{cms_data}'
                    # 遍历该部分中的datasource数组
                    for datasource_key in component['datasource']:
                        # 检查datasource键是否在datasource字典中
                        if datasource_key in data_source:
                            # 提取'now' URL
                            now_url = data_source[datasource_key]['now']
                            # 在URL中查找ids参数
                            if 'ids=' in now_url:
                                # 提取ids的值
                                ids_value = now_url.split('ids=')[1].split('&')[0]
                                print(ids_value)
                                ids_cms_v3 = SearchByIdsV3().search_by_ids_cms_v3(headers=headers,
                                                                                  zipcode=porder["zipcode"],
                                                                                  source=component["datasource"][0],
                                                                                  ids=ids_value,
                                                                                  date=porder["delivery_pickup_date"],
                                                                                  dataobject_key=
                                                                                  component["datasource"][0])
                                assert ids_cms_v3["result"] is True, f'cms {page_key}页面数据异常，请确认{cms_data}'
                # 基础组件
                elif component["component_key"] == "cm_title":
                    assert component["properties"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'
                    assert "cm_title" in component["properties"]["event_key"], f'cms {page_key}页面数据异常，请确认{cms_data}'
                    assert component["properties"]["text"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'

                elif component["component_key"] == "cm_text":
                    assert component["datasource"] == [], f'cms {page_key}页面数据异常，请确认{cms_data}'
                    assert component["properties"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'
                    assert "cm_text" in component["properties"]["event_key"], f'cms {page_key}页面数据异常，请确认{cms_data}'
                    assert component["properties"]["text"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'

                elif component["component_key"] == "cm_button":
                    assert component["datasource"] == [], f'cms {page_key}页面数据异常，请确认{cms_data}'

                    assert component["properties"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'
                    assert "cm_button" in component["properties"]["event_key"], f'cms {page_key}页面数据异常，请确认{cms_data}'
                    assert component["properties"]["text"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'
                elif component["component_key"] == "cm_divider":
                    assert component["datasource"] == [], f'cms {page_key}页面数据异常，请确认{cms_data}'
                    assert component["properties"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'
                    assert "cm_divider" in component["properties"]["event_key"], f'cms {page_key}页面数据异常，请确认{cms_data}'
                elif component["component_key"] == "cm_block":
                    assert component["datasource"] == [], f'cms {page_key}页面数据异常，请确认{cms_data}'
                    assert component["properties"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'
                    assert component["properties"]["block_height"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'
                    assert "cm_block" in component["properties"]["event_key"], f'cms {page_key}页面数据异常，请确认{cms_data}'

                elif component["component_key"] == "cm_video":
                    assert component["datasource"] == [], f'cms {page_key}页面数据异常，请确认{cms_data}'
                    assert component["properties"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'
                    assert "cm_video" in component["properties"]["event_key"], f'cms {page_key}页面数据异常，请确认{cms_data}'
                    assert component["properties"]["video_url"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'

                elif component["component_key"] == "cm_complex_title":
                    assert component["datasource"] == [], f'cms {page_key}页面数据异常，请确认{cms_data}'
                    assert component["properties"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'
                    assert "cm_complex_title" in component["properties"]["event_key"], f'cms {page_key}页面数据异常，请确认{cms_data}'
                    # assert component["properties"]["video_url"] is not None

                elif component["component_key"] == "cm_multiple_coupons":
                    # assert component["datasource"] == []
                    assert "cm_multiple_coupons" in component["properties"]["event_key"], f'cms {page_key}页面数据异常，请确认{cms_data}'

                elif component["component_key"] == "cm_single_coupon":
                    # assert component["datasource"] == []
                    assert "cm_single_coupon" in component["properties"]["event_key"], f'cms {page_key}页面数据异常，请确认{cms_data}'

                elif component["component_key"] == "cm_countdown":
                    # assert component["datasource"] == []
                    assert "cm_countdown" in component["properties"]["event_key"]
                    assert component["properties"]["mobileImageInfo"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'
                    assert component["properties"]["server_time"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'
                    assert component["properties"]["color"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'
                    assert component["properties"]["pcSpaceInfo"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'
                    assert component["properties"]["end_time_string"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'
                    assert component["properties"]["end_time"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'
                    assert component["properties"]["mobileSpaceInfo"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'
                    assert component["properties"]["countdown_style"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'
                    assert component["properties"]["countdown_position"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'
                    assert component["properties"]["pcImageInfo"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'
                    assert component["properties"]["visibility_rule"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'

        # assert page_param["brand_key"] == "Gb31rSxI", f'cms 品牌页面数据异常，请确认{data}'
        assert page_param["share_title"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'
        assert page_param["share_desc"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'
        assert page_param["share_image"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'
        assert page_param["subject"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'
        assert page_param["page_name"] is not None, f'cms {page_key}页面数据异常，请确认{cms_data}'

    @staticmethod
    def sort_assert(products, sort_key, category):
        # 提取 price 并存储在列表中
        prices = [product["price"] for product in products]
        # reverse -- 排序规则，reverse = True 降序 ， reverse = False 升序（默认
        sort_1 = products[0]["price"]
        sort_2 = products[-1]["price"]
        if sort_key == "price_asc":
            # 断言商品"价格：低到高",返回商品价格为升序
            assert prices == sorted(prices, reverse=False)
            assert sort_1 <= sort_2, f"{category}搜索商品价格：低到高 排序不对：{sort_1}<={sort_2}"
        elif sort_key == "price_desc":
            # print(products)
            # 断言商品"价格：高到低" , 返回商品价格为倒序
            assert prices == sorted(prices, reverse=True)
            assert sort_1 >= sort_2, f"{category}搜索商品价格：高到低排序不对：{sort_1}>={sort_2}"

    @staticmethod
    def sort_data_assert(products, sort_key, category):
        # 分类接口会套一层data
        prices = [product["price"] for product in products]
        sort_1 = products[0]["data"]["price"]
        sort_2 = products[-1]["data"]["price"]
        if sort_key == "price_asc":
            # 断言商品"价格：低到高"
            assert sort_1 <= sort_2, f"{category}搜索商品价格：低到高 排序不对：{sort_1}<={sort_2}"
        elif sort_key == "price_desc":
            # print(products)
            # 断言商品"价格：高到低"
            assert sort_1 >= sort_2, f"{category}搜索商品价格：高到低排序不对：{sort_1}>={sort_2}"

    @staticmethod
    def filter_assert(products, property_key, value_key, category):
        # delivery_type 配送方式
        if property_key == "delivery_type":
            if value_key == "delivery_type_local_mof":
                # 断言MOF地区的本地配送商品
                assert products[-1][
                           "is_mkpl"] is False, f'分类：{category}，Filter:{property_key}:{value_key}返回异常{products}'
                assert products[-1][
                           "is_pantry"] is False, f'分类：{category}，Filter:{property_key}:{value_key}返回异常{products}'
            elif value_key == "delivery_type_fbw":
                # 断言MOF商品是本地配送商品
                assert products[-1][
                           "is_mkpl"] is False, f'分类：{category}，Filter:{property_key}:{value_key}返回异常{products}'
                assert products[-1][
                           "is_pantry"] is False, f'分类：{category}，Filter:{property_key}:{value_key}返回异常{products}'
            elif value_key == "delivery_type_local":
                # 断言非MOF地区的本地配送商品
                assert products[-1][
                           "is_mkpl"] is False, f'分类：{category}，Filter:{property_key}:{value_key}返回异常{products}'
                assert products[-1][
                           "is_pantry"] is False, f'分类：{category}，Filter:{property_key}:{value_key}返回异常{products}'
            elif value_key == "delivery_type_pantry":
                # 断言商品是pantry 商品
                assert products[-1][
                           "is_pantry"] is True, f'分类：{category}，Filter:{property_key}:{value_key}返回异常{products}'
            elif value_key == "delivery_type_global":
                # 断言商品是global+ 商品
                assert products[-1][
                           "is_mkpl"] is True, f'分类：{category}，Filter:{property_key}:{value_key}返回异常{products}'
                assert products[-1][
                           "biz_type"] == "seller", f'分类：{category}，Filter:{property_key}:{value_key}返回异常{products}'

        # product_type 产品类型
        elif property_key == "product_type":

            if value_key == "product_type_sale":
                # 断言商品是on sale商品,有折扣价
                for index, product in enumerate(products):
                    if product["volume_price_support"] is True:
                        assert product["price"] < product[
                            "volume_price"], f'分类：{category}，Filter:{property_key}:{value_key}返回异常{products}'
                    else:
                        assert product[
                                   "base_price"] is not None, f'分类：{category}，Filter:{property_key}:{value_key}返回异常{products}'
                        assert product["base_price"] > product[
                            "price"], f'分类：{category}，Filter:{property_key}:{value_key}返回异常{products}'
                    if index == 2:
                        break

            elif value_key == "product_type_new":
                # 断言商品是new 商品
                # 断言 'label_key' 为 'new' 的元素存在
                assert any(item['label_key'] == "new" for item in products[-1][
                    'label_list']), f'分类：{category}，Filter:{property_key}：{value_key},{products[-1]["id"]}:label_key="new" does not exist'
                # assert products[-1]["data"]['label_list'][0]['label_key'] == 'new', f"商品返回不是new商品，请确认~"
            elif value_key == "product_type_cold_pack":
                # 断言商品是 冷链商品
                assert products[-1][
                           "is_colding_package"] is True, f'分类：{category}，Filter:{property_key}：{value_key}返回异常{products}'

        # 产地过滤
        elif property_key == "5":
            pass

        # 价格区间
        elif property_key == "6":
            # $ 价格区间小于$5
            if value_key == "1":
                assert all(product["price"] < 5 for product in
                           products), f'分类：{category}，Filter:{property_key}：{value_key}返回异常{products},Not all prices are less than $5'
            # $ 价格区间 $5-10
            elif value_key == "2":
                assert all(5 <= product["price"] < 10 for product in
                           products), f'分类：{category}，Filter:{property_key}：{value_key}返回异常{products},Not all prices are in $5-10'

            # $ 价格区间 $10-15
            elif value_key == "3":
                assert all(10 <= product["price"] < 15 for product in
                           products), f'分类：{category}，Filter:{property_key}：{value_key}返回异常{products},Not all prices are in $10-15'

            # $ 价格区间 $15-25
            elif value_key == "4":
                assert all(15 <= product["price"] < 25 for product in
                           products), f'分类：{category}，Filter:{property_key}：{value_key}返回异常{products},Not all prices are in $15-25'
            # $ 价格区间高于 $25
            elif value_key == "5":
                assert all(product["price"] >= 25 for product in
                           products), f'分类：{category}，Filter:{property_key}：{value_key}返回异常{products},Not all prices are more than $25'
        # 商家过滤
        elif property_key == "8":
            # assert filters_res["object"][
            #            "total_count"] > 0, f'分类：{category}，Filter:商家过滤：{property_value["value_key"]} 返回异常{filters_res["object"]}'
            # assert len(filters_res.get("object").get(
            #     "contents")) > 0, f'分类：{category}，Filter:商家过滤：{property_value["value_key"]} 返回异常{filters_res["object"]}'
            # products = [content for content in filters_res["object"]["contents"] if
            #             content["type"] == "product"]
            # 断言商品是第三方商家商品
            if value_key != "0":
                assert all(str(product["vender_id"]) == value_key for product in
                           products), f'分类：{category}，Filter:{property_key}：{value_key}返回异常{products}'

    @staticmethod
    def filter_data_assert(products, property_key, value_key, category):
        # 分类接口会套一层data
        # delivery_type 配送方式
        if property_key == "delivery_type":

            # products = [content for content in filters_res["object"]["contents"] if
            #             content["type"] == "product"]

            if value_key == "delivery_type_local_mof":
                # 断言MOF地区的本地配送商品
                assert products[-1]["data"][
                           "is_mkpl"] is False, f'分类：{category}，Filter:{property_key}:{value_key}返回异常{products}'
                assert products[-1]["data"][
                           "is_pantry"] is False, f'分类：{category}，Filter:{property_key}:{value_key}返回异常{products}'
            elif value_key == "delivery_type_fbw":
                # 断言MOF商品是本地配送商品
                assert products[-1]["data"][
                           "is_mkpl"] is False, f'分类：{category}，Filter:{property_key}:{value_key}返回异常{products}'
                assert products[-1]["data"][
                           "is_pantry"] is False, f'分类：{category}，Filter:{property_key}:{value_key}返回异常{products}'
            elif value_key == "delivery_type_local":
                # 断言非MOF地区的本地配送商品
                assert products[-1]["data"][
                           "is_mkpl"] is False, f'分类：{category}，Filter:{property_key}:{value_key}返回异常{products}'
                assert products[-1]["data"][
                           "is_pantry"] is False, f'分类：{category}，Filter:{property_key}:{value_key}返回异常{products}'
            elif value_key == "delivery_type_pantry":
                # 断言商品是pantry 商品
                assert products[-1]["data"][
                           "is_pantry"] is True, f'分类：{category}，Filter:{property_key}:{value_key}返回异常{products}'
            elif value_key == "delivery_type_global":
                # 断言商品是global+ 商品
                assert products[-1]["data"][
                           "is_mkpl"] is True, f'分类：{category}，Filter:{property_key}:{value_key}返回异常{products}'
                assert products[-1]["data"][
                           "biz_type"] == "seller", f'分类：{category}，Filter:{property_key}:{value_key}返回异常{products}'

        # product_type 产品类型
        elif property_key == "product_type":

            if value_key == "product_type_sale":
                # 断言商品是on sale商品,有折扣价
                for index, product in enumerate(products):
                    if product["data"]["volume_price_support"] is True:
                        assert product["data"]["price"] < product["data"][
                            "volume_price"], f'分类：{category}，Filter:{property_key}:{value_key}返回异常{products}'
                    else:
                        assert product["data"][
                                   "base_price"] is not None, f'分类：{category}，Filter:{property_key}:{value_key}返回异常{products}'
                        assert product["data"]["base_price"] > product["data"][
                            "price"], f'分类：{category}，Filter:{property_key}:{value_key}返回异常{products}'
                    if index == 2:
                        break

            elif value_key == "product_type_new":
                # 断言商品是new 商品
                # 断言 'label_key' 为 'new' 的元素存在
                assert any(item['label_key'] == "new" for item in products[-1]["data"][
                    'label_list']), f'分类：{category}，Filter:{property_key}：{value_key},{products[-1]["data"]["id"]}:label_key="new" does not exist'
                # assert products[-1]["data"]['label_list'][0]['label_key'] == 'new', f"商品返回不是new商品，请确认~"
            elif value_key == "product_type_cold_pack":
                # 断言商品是 冷链商品
                assert products[-1]["data"][
                           "is_colding_package"] is True, f'分类：{category}，Filter:{property_key}：{value_key}返回异常{products}'

        # 产地过滤
        elif property_key == "5":
            pass

        # 价格区间
        elif property_key == "6":
            # $ 价格区间小于$5
            if value_key == "1":
                assert all(product["data"]["price"] < 5 for product in
                           products), f'分类：{category}，Filter:{property_key}：{value_key}返回异常{products},Not all prices are less than $5'
            # $ 价格区间 $5-10
            elif value_key == "2":
                assert all(5 <= product["data"]["price"] < 10 for product in
                           products), f'分类：{category}，Filter:{property_key}：{value_key}返回异常{products},Not all prices are in $5-10'

            # $ 价格区间 $10-15
            elif value_key == "3":
                assert all(10 <= product["data"]["price"] < 15 for product in
                           products), f'分类：{category}，Filter:{property_key}：{value_key}返回异常{products},Not all prices are in $10-15'

            # $ 价格区间 $15-25
            elif value_key == "4":
                assert all(15 <= product["data"]["price"] < 25 for product in
                           products), f'分类：{category}，Filter:{property_key}：{value_key}返回异常{products},Not all prices are in $15-25'
            # $ 价格区间高于 $25
            elif value_key == "5":
                assert all(product["data"]["price"] >= 25 for product in
                           products), f'分类：{category}，Filter:{property_key}：{value_key}返回异常{products},Not all prices are more than $25'
        # 商家过滤
        elif property_key == "8":
            # assert filters_res["object"][
            #            "total_count"] > 0, f'分类：{category}，Filter:商家过滤：{property_value["value_key"]} 返回异常{filters_res["object"]}'
            # assert len(filters_res.get("object").get(
            #     "contents")) > 0, f'分类：{category}，Filter:商家过滤：{property_value["value_key"]} 返回异常{filters_res["object"]}'
            # products = [content for content in filters_res["object"]["contents"] if
            #             content["type"] == "product"]
            # 断言商品是第三方商家商品
            if value_key != "0":
                assert all(str(product["data"]["vender_id"]) == value_key for product in
                           products), f'分类：{category}，Filter:{property_key}：{value_key}返回异常{products}'
