"""
<AUTHOR>  suqin
@Version        :  V1.0.0
------------------------------------
@File           :  check_product_add_to_cart_success.py.py
@Description    :
@CreateTime     :  2023/8/12 17:40
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/8/12 17:40
"""
from typing import Any

import weeeTest
import time
import logging
from weeeTest import weeeConfig, log, jmespath

from test_dir.api.ec.ec_so.preorder.query_preorder import QueryPreOrder
from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine
from test_dir.api.ec.ec_so.save_for_later.save_for_later_item_move_to_cart import SaveForLaterItemMoveToCart
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction


class CommCheckProductsWithCart(weeeTest.TestCase):

    def products_data_add_to_cart(self, headers, products: list | Any, porder_deal_date, product_source):
        # 一批商品循环加购
        for index, product in enumerate(products):
            if product["type"] != "carousel":
                if "biz_type" not in product["data"]:
                    product["data"]["biz_type"] = "normal"
                if product["data"]["sold_status"] == "available":
                    refer_type = "normal"
                    vender_id = None
                    if product["data"]["is_pantry"] is False and product["data"]["is_mkpl"] is False:
                        refer_type = "normal"
                    elif product["data"]["is_mkpl"] is True:
                        refer_type = "seller"
                        vender_id = product["data"]["vender_info_view"]["vender_id"]
                    elif product["data"]["is_pantry"] is True:
                        refer_type = "pantry"
                    UpdatePreOrderLine().porder_items_v3(headers=headers, product_id=product["data"]["id"],
                                                         biz_type=product["data"]["biz_type"],
                                                         date=porder_deal_date,
                                                         min_order_quantity=product['data']["min_order_quantity"],
                                                         quantity=product["data"]["min_order_quantity"],
                                                         is_pantry=product["data"]["is_pantry"],
                                                         is_mkpl=product["data"]["is_mkpl"],
                                                         refer_type=refer_type,
                                                         vender_id=vender_id,
                                                         source=product_source)
                    # 判断加购成功
                    assert self.response["result"] is True
                    print("buy api self.response is ====>", self.response)
                    # 判断加购成功
                    assert self.response["result"] is True and len(
                        self.response["object"][
                            "updateItems"]) > 0, f'product id is {product["data"]["id"]}, self.response.object is {self.response["object"]}, zipcode is {headers.get("Zipcode")}, store is {headers.get("Weee-Store")}'

                    # 判断加入购物车成功
                    CommCheckProductsWithCart().check_product_exists_in_cart(headers=headers, cart_domain="grocery",
                                                                             product_id=product["data"]["id"])

                if product["data"]["sold_status"] == "change_other_day":
                    # 支持切换日期流程
                    pass

            if index == 2:
                break

    def products_add_to_cart(self, headers, products: list | Any, porder_deal_date, product_source
                             ):
        # 一批商品循环加购
        for index, product in enumerate(products):
            if "biz_type" not in product:
                product["biz_type"] = "normal"
            if product["sold_status"] == "available":
                refer_type = "normal"
                vender_id = None
                if product["is_pantry"] is False and product["is_mkpl"] is False:
                    refer_type = "normal"
                elif product["is_mkpl"] is True:
                    refer_type = "seller"
                    vender_id = product["vender_info_view"]["vender_id"]
                elif product["is_pantry"] is True:
                    refer_type = "pantry"
                UpdatePreOrderLine().porder_items_v3(headers=headers, product_id=product["id"],
                                                     biz_type=product["biz_type"],
                                                     date=porder_deal_date,
                                                     min_order_quantity=product["min_order_quantity"],
                                                     quantity=product["min_order_quantity"],
                                                     is_pantry=product["is_pantry"],
                                                     is_mkpl=product["is_mkpl"],
                                                     refer_type=refer_type,
                                                     vender_id=vender_id,
                                                     source=product_source)
                print("buy api self.response is ====>", self.response)
                # 判断加购成功
                assert self.response["result"] is True and len(
                    self.response["object"][
                        "updateItems"]) > 0, f"product id is {product['id']}, self.response.object is {self.response['object']}, zipcode is {headers.get('Zipcode')}, store is {headers.get('Weee-Store')}"
                # 判断加入购物车成功
                CommCheckProductsWithCart().check_product_exists_in_cart(headers=headers, cart_domain="grocery",
                                                                         product_id=product["id"])

            if index == 2:
                break

    def product_add_to_cart(self, headers, product: dict | Any, porder_deal_date, product_source,
                            quantity: int = 1):
        # 单个商品加购
        if "biz_type" not in product.keys():
            product["biz_type"] = "normal"
        if product["sold_status"] == "available":
            refer_type = "normal"
            vender_id = None
            if product["is_pantry"] is False and product["is_mkpl"] is False:
                refer_type = "normal"
            elif product["is_mkpl"] is True:
                print(product)
                refer_type = "seller"
                vender_id = product["vender_info_view"]["vender_id"]
            elif product["is_pantry"] is True:
                refer_type = "pantry"
            UpdatePreOrderLine().porder_items_v3(headers=headers, product_id=product["id"],
                                                 biz_type=product["biz_type"],
                                                 date=porder_deal_date,
                                                 min_order_quantity=product["min_order_quantity"],
                                                 quantity=quantity,
                                                 is_pantry=product["is_pantry"],
                                                 is_mkpl=product["is_mkpl"],
                                                 refer_type=refer_type,
                                                 vender_id=vender_id,
                                                 source=product_source)
            print("buy api self.response is ====>", self.response)
            # 判断加购成功
            assert self.response["result"] is True and len(
                self.response["object"][
                    "updateItems"]) > 0, f"product id is {product['id']}, self.response is {self.response}, zipcode is {headers.get('zipcode')}, store is {headers.get('weee-store')}"
            # 判断加入购物车成功
            CommCheckProductsWithCart().check_product_exists_in_cart(headers=headers, cart_domain="grocery",
                                                                     product_id=product["id"])

    def check_product_exists_in_cart(self, headers, cart_domain, product_id, final_amount: str | float | bool = False,
                                     is_in=True):
        sections = QueryPreOrder().query_preorder_v5(headers=headers, cart_domain=cart_domain)["object"]["sections"]
        if len(sections) > 0:
            common_product_ids = [item['product_id'] for section in sections for item in section.get('items') if
                                  len(section.get('items')) > 0]
            activity_product_ids = []
            try:
                # activity_product_ids取值较为复杂，不能使用推导式
                # activity_product_ids = [item.get('product_id', "") for section in sections for activity_info in
                #                         section.get('activity_info') for item in activity_info.get('items', []) if
                #                         len(section.get('activity_info', [])) > 0 and activity_info.get('items', []) is not None]
                for section in sections:
                    activity_infos = section.get("activity_info")
                    if activity_infos:
                        for activity_info in activity_infos:
                            if activity_info.get("items"):
                                items = activity_info.get("items")
                                for item in items:
                                    activity_product_ids.append(item.get("product_id", None))
            except Exception as e:
                log.debug("集合中不存在activity_product_ids" + str(e))
                activity_product_ids = []
            if not isinstance(product_id, int):
                product_id = int(product_id)
            if is_in:
                assert product_id in common_product_ids + activity_product_ids, f"product_id is {product_id}, common_product_ids is {common_product_ids}, activity_product_ids is {activity_product_ids}"
            else:
                assert product_id not in common_product_ids + activity_product_ids, f"product_id is {product_id}, common_product_ids is {common_product_ids}, activity_product_ids is {activity_product_ids}"
        else:
            log.info("此购物车不存在任何商品")

        if isinstance(final_amount, float | str):
            assert (float(final_amount) - float(self.response['object']['final_amount'])) == 0

    def check_product_add_to_cart_success(self, headers, cart_domain, product_id):
        """判断加入购物车成功"""
        # 获取购物车下sections模块
        sections = QueryPreOrder().query_preorder_v5(headers=headers, cart_domain=cart_domain)["object"]["sections"]
        print(type(sections))
        begin = time.time()
        print(f'Began at ${begin}, section count = ${len(sections)}')
        product_id_exists = False
        if sections is not None:
            for section in sections:
                for activity_info in section["activity_info"]:
                    if activity_info["items"] is not None:
                        for item in activity_info["items"]:
                            if item["product_id"] == product_id:
                                product_id_exists = True
                                break
                if section["items"] is not None:
                    for item in section["items"]:
                        if item["product_id"] == product_id:
                            product_id_exists = True
                            break
        end = time.time()
        print(f'Ended at ${end}')
        print(f'Total time used = ${end - begin}')
        assert product_id_exists, "Product ID:" + str(product_id) + "does not exist in cart"

    def check_product_add_to_saveforlater_success(self, headers, cart_domain, product_id):
        """判断加入稍后再买成功"""
        save_for_later_response = SaveForLaterItemMoveToCart().save4later_v2(headers=headers)["object"]["items"]
        product_id_exists = False
        if save_for_later_response is not None:
            for items in save_for_later_response:
                items = items["product_id"]
                if items == product_id:
                    product_id_exists = True
                break
        assert product_id_exists, "Product ID: " + str(product_id) + " does not exist in cart"

    def check_product_add_to_cart_fail(self, headers, cart_domain, product_id):
        """判断加入购物车失败"""
        # 获取购物车下sections模块
        sections = QueryPreOrder().query_preorder_v5(headers=headers, cart_domain=cart_domain)["object"]["sections"]
        product_id_exists = True
        if sections is not None:
            for section in sections:
                items = section["items"]
                for item in items:
                    if item["product_id"] == product_id:
                        product_id_exists = False
                        break
        assert product_id_exists, "Product ID:" + str(product_id) + "exist in cart"

    def check_product_exists_and_price(self, headers, cart_domain, product_id, quantity,
                                       price: str | float | bool = False,
                                       is_in=True):
        sections = QueryPreOrder().query_preorder_v5(headers=headers, cart_domain=cart_domain)["object"]["sections"]
        assert len(sections) > 0, f'购物车商品为空，请确认{sections}'
        # 标记，用于确定是否找到了商品
        product_found = False
        # 进行断言
        for section in sections:
            for item in section["items"]:
                if item["product_id"] == product_id:
                    product_found = True
                    # print(item["price"])
                    # print(price * quantity)
                    # 断言价格是否相等
                    assert round(item["price"], 2) == round(price * quantity, 2), f'购物车商品 {item["product_id"]} 价格不对，{sections}, item_price={item.get("price")}, price={price}, quantity={quantity}'

                    # 单价
                    assert round(item["unit_price"], 2) == round(price, 2), f'购物车商品 {item["product_id"]} 价格不对，{sections}, item_unit_price={item.get("unit_price")}, price={price}'
                    # 断言数量是否相等
                    assert item["quantity"] == quantity, f'购物车商品 {item["quantity"]} 数量不对，{sections}, quantity={quantity}'
                    break

        # 如果商品没有在购物车中找到，则抛出错误
        assert product_found, f'购物车商品 {product_id} 不存在，{sections}'

    def check_product_exists_and_fee_info(self, headers, cart_domain, product_id, quantity,
                                          price: str | float | bool = False,
                                          is_in=True):
        sections = QueryPreOrder().query_preorder_v5(headers=headers, cart_domain=cart_domain)["object"]["sections"]
        assert len(sections) > 0, f'购物车商品为空，请确认{sections}'
        # 标记，用于确定是否找到了商品
        product_found = False
        # 进行断言
        for section in sections:
            for item in section["items"]:
                if item["product_id"] == product_id:
                    product_found = True
                    # 断言价格是否相等
                    assert round(item["price"], 2) == round(price * quantity,2), f'购物车商品 {item["product_id"]} 价格不对，{sections}'

                    # 单价
                    assert round(item["unit_price"], 2) == round(price,2), f'购物车商品 {item["product_id"]} 价格不对，{sections}'

                    # 断言数量是否相等
                    assert item["quantity"] == quantity, f'购物车商品 {item["product_id"]} 数量不对，{sections}'
                    break

        # 如果商品没有在购物车中找到，则抛出错误
        assert product_found, f'购物车商品 {product_id} 不存在，{sections}'
