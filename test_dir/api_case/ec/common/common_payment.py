import weeeTest

from test_dir.api.ec.ec_so.address.apply_user_address_information import ApplyUserAddressInformation
from test_dir.api.ec.ec_so.address.query_user_address_list import QueryUserAddressList
from test_dir.api.ec.ec_so.order.checkout import Checkout
from test_dir.api.ec.ec_so.order.prepare_checkout import PrepareCheckout
from test_dir.api.ec.ec_so.preorder.payment_category import PaymentCategory


class CommonPayment(weeeTest.TestCase):

    def pay_with_point(self, headers):
        # 1. 查询及应用地址
        address_list = QueryUserAddressList().address_list(headers)
        address_ids = [address["id"] for address in address_list["object"] if
                       len(address_list["object"]) > 0 and "98011" in address["address"]]
        assert address_ids, f"没有符合条件的地址，地址列表为{address_ids}"
        # 1.1 应用地址
        ApplyUserAddressInformation().address_apply_v2(headers, address_ids[0])
        # 2. 切换支付方式为P, 积分支付， 必须先切换支付方式，再pre checkout
        PaymentCategory().payment_category(headers, "P")
        # 3. 预结算
        pre = PrepareCheckout().prepare_checkout_v2(headers, "grocery")
        checkout_amount = pre["object"]["fee_info"]["final_amount"]
        checkout_pre_id = pre["object"]["checkout_pre_id"]

        # 4. 使用积分支付
        if self.env["env_url"] == 'https://api.tb1.sayweee.net':
            my_order = Checkout().checkout_v3(headers, "grocery", checkout_pre_id, checkout_amount)
            print("my order===>", my_order)
            if my_order['object']['order_ids']:
                return my_order['object']
            else:
                return {
                    "error": "获取订单号失败",
                    "data": my_order
                }
        else:
            return {
                "error": "目前不是tb1环境，不能支付"
            }

    @staticmethod
    def pay_with_paypal(headers):
        # 1. 查询及应用地址
        address_list = QueryUserAddressList().address_list(headers)
        address_ids = [address["id"] for address in address_list["object"] if
                       len(address_list["object"]) > 0 and "98011" in address["address"]]
        assert address_ids, f"没有符合条件的地址，地址列表为{address_ids}"
        # 1.1 应用地址
        ApplyUserAddressInformation().address_apply_v2(headers, address_ids[0])
        # 2. 切换支付方式，一定要放在pre checkout之前
        payment = PaymentCategory().payment_category(headers, "P", False)
        print("payment===>", payment)
        # 3. 预结算
        pre = PrepareCheckout().prepare_checkout_wechat_or_paypal_v2(headers, data={
            "cart_domain": "grocery",
            # 不需要填写小费信息
            # "tip_option": {
            #     "index": 0, "rate": None, "tip": 2
            # },
            "selected_points": False,  # 不使用积分支付，最好带上这个参数
            "delivery_window_id": ""
        })
        print("pre checkout result===>", pre)
        assert pre['object'], f"预结算失败，返回结果为：{pre}"
        checkout_amount = pre["object"]["fee_info"]["final_amount"]
        checkout_pre_id = pre["object"]["checkout_pre_id"]
        # 小费信息需要手动计算，否则checkout会失败
        if pre["object"]["fee_info"]["tip"] == '2.00':
            tip_options = {"index": 0, "rate": None, "tip": 2}
        elif pre["object"]["fee_info"]["tip"] == '3.00':
            tip_options = {"index": 1, "rate": None, "tip": 3}
        elif pre["object"]["fee_info"]["tip"] == '4.00':
            tip_options = {"index": 2, "rate": None, "tip": 4}
        else:
            tip_options = {"index": 3, "rate": None, "tip": 0}

        print("tip_options===>", tip_options)

        # 4. 使用paypal支付
        my_order = Checkout().checkout_wechat_or_paypal_v3(
            headers=headers,
            data={
                "referral_id": 0,
                "checkout_pre_id": checkout_pre_id,
                "checkoutAmount": checkout_amount,
                "cart_domain": "grocery",
                "plan_id": "",
                "delivery_window_id": "",
                "braintree_device_data": "",
                "tip_info": tip_options
            }
        )
        print("my order===>", my_order)
        assert my_order.get('object'), f"订单获取失败，请检查，返回数据为：{my_order}"
        assert my_order.get("object").get("order_ids"), f"订单获取失败，请检查，返回数据为：{my_order}"
        if my_order['object']['order_ids']:
            return my_order['object']
        else:
            return {
                "error": "获取订单号失败",
                "data": my_order
            }

    @staticmethod
    def pay_with_credit_card(headers):
        # 1. 查询及应用地址
        address_list = QueryUserAddressList().address_list(headers)
        address_ids = [address["id"] for address in address_list["object"] if
                       len(address_list["object"]) > 0 and "98011" in address["address"]]
        assert address_ids, f"没有符合条件的地址，地址列表为{address_ids}"
        # 1.1 应用地址
        ApplyUserAddressInformation().address_apply_v2(headers, address_ids[0])
        # 2. 切换支付方式，一定要放在pre checkout之前
        profile_id = PaymentCategory().braintree_profiles(headers)["object"][0]['profile_id']
        payment = PaymentCategory().payment_category_credit_card(
            headers,
            {
                "payment_category": "B",
                "profile_id": profile_id,
                "points": False
            }
        )

        # 3. pre checkout
        pre = PrepareCheckout().prepare_checkout_wechat_or_paypal_v2(headers, data={
            "cart_domain": "grocery",
            # 不需要填写小费信息
            # "tip_option": {
            #     "index": 0, "rate": None, "tip": 2
            # },
            "selected_points": False,  # 不使用积分支付，最好带上这个参数, 信用卡支付时，没看到这个参数
            "delivery_window_id": ""
        })

        print("pre checkout result===>", pre)
        checkout_amount = pre["object"]["fee_info"]["final_amount"]
        checkout_pre_id = pre["object"]["checkout_pre_id"]
        # 小费信息需要手动计算，否则checkout会失败
        if pre["object"]["fee_info"]["tip"] == '2.00':
            tip_options = {"index": 0, "rate": None, "tip": 2}
        elif pre["object"]["fee_info"]["tip"] == '3.00':
            tip_options = {"index": 1, "rate": None, "tip": 3}
        elif pre["object"]["fee_info"]["tip"] == '4.00':
            tip_options = {"index": 2, "rate": None, "tip": 4}
        else:
            tip_options = {"index": 3, "rate": None, "tip": 0}

        print("tip_options===>", tip_options)

        Checkout().checkout_wechat_or_paypal_v3(headers, data={
            "referral_id": 0,
            "checkout_pre_id": checkout_pre_id,
            "checkoutAmount": checkout_amount,
            "cart_domain": "grocery",
            "plan_id": "",
            "delivery_window_id": "",
            "braintree_device_data": "",
            "tip_info": tip_options
        })

    @staticmethod
    def pay_with_venmo(headers):

        # 1. 查询及应用地址
        address_list = QueryUserAddressList().address_list(headers)
        address_ids = [address["id"] for address in address_list["object"] if
                       len(address_list["object"]) > 0 and "98011" in address["address"]]
        assert address_ids, f"没有符合条件的地址，地址列表为{address_ids}"
        # 1.1 应用地址
        ApplyUserAddressInformation().address_apply_v2(headers, address_ids[0])
        # 2. 切换支付方式，一定要放在pre checkout之前
        payment = PaymentCategory().payment_category(headers, "V", False)
        print("payment===>", payment)
        # 3. 预结算
        pre = PrepareCheckout().prepare_checkout_wechat_or_paypal_v2(headers, data={
            "cart_domain": "grocery",
            # 不需要填写小费信息
            # "tip_option": {
            #     "index": 0, "rate": None, "tip": 2
            # },
            "selected_points": False,  # 不使用积分支付，最好带上这个参数
            "delivery_window_id": ""
        })
        print("pre checkout result===>", pre)
        assert pre['object'], f"预结算失败，返回结果为：{pre}"
        checkout_amount = pre["object"]["fee_info"]["final_amount"]
        checkout_pre_id = pre["object"]["checkout_pre_id"]
        # 小费信息需要手动计算，否则checkout会失败
        if pre["object"]["fee_info"]["tip"] == '2.00':
            tip_options = {"index": 0, "rate": None, "tip": 2}
        elif pre["object"]["fee_info"]["tip"] == '3.00':
            tip_options = {"index": 1, "rate": None, "tip": 3}
        elif pre["object"]["fee_info"]["tip"] == '4.00':
            tip_options = {"index": 2, "rate": None, "tip": 4}
        else:
            tip_options = {"index": 3, "rate": None, "tip": 0}

        print("tip_options===>", tip_options)

        # 4. 调结算接口，使用venmo支付
        my_order = Checkout().checkout_wechat_or_paypal_v3(
            headers=headers,
            data={
                "referral_id": 0,
                "checkout_pre_id": checkout_pre_id,
                "checkoutAmount": checkout_amount,
                "cart_domain": "grocery",
                "plan_id": "",
                "delivery_window_id": "",
                "braintree_device_data": "",
                "tip_info": tip_options
            }
        )
        print("my order===>", my_order)
        assert my_order.get('object'), f"订单获取失败，请检查，返回数据为：{my_order}"
        assert my_order.get("object").get("order_ids"), f"订单获取失败，请检查，返回数据为：{my_order}"
        if my_order['object']['order_ids']:
            return my_order['object']
        else:
            return {
                "error": "获取订单号失败",
                "data": my_order
            }

    @staticmethod
    def pay_with_all_method(headers, payment_category, is_point:bool=False):
        """
        # D 信用卡 strip，老APP使用
        # B 表示braintree信用卡，Q也是，目前废弃了
        # P 表示 PayPal
        # V 表示 venmo
        # M 表示ETB
        # I 表示 微信，F、C 也是微信，目前废弃了
        # K 表示支付宝, A 及E 目前不用跑了
        # H 表示cash app
        # L 表示apple pay，T 也是目前废弃了
        """

        # 1. 查询及应用地址
        address_list = QueryUserAddressList().address_list(headers)
        address_ids = [address["id"] for address in address_list["object"] if
                       len(address_list["object"]) > 0 and "98011" in address["address"]]
        assert address_ids, f"没有符合条件的地址，地址列表为{address_ids}"
        # 1.1 应用地址
        ApplyUserAddressInformation().address_apply_v2(headers, address_ids[0])
        # 2. 切换支付方式，一定要放在pre checkout之前
        if payment_category == "B":
            # 如果是信用卡，要传profile_id
            profile_id = PaymentCategory().braintree_profiles(headers)["object"][0]['profile_id']
            payment = PaymentCategory().payment_category(headers=headers,
                                                         payment_category=payment_category,
                                                         points=is_point, profile_id=profile_id)
            # payment = PaymentCategory().payment_category_credit_card(
            #     headers,
            #     {
            #         "payment_category": "B",
            #         "profile_id": profile_id,
            #         "points": False
            #     }
            # )
            print("payment===>", payment)
        else:
            payment = PaymentCategory().payment_category(headers=headers,
                                                         payment_category=payment_category,
                                                         points=is_point)
            print("payment===>", payment)
        # 3. 预结算
        pre = PrepareCheckout().prepare_checkout_wechat_or_paypal_v2(headers=headers, data={
            "cart_domain": "grocery",
            # 不需要填写小费信息
            # "tip_option": {
            #     "index": 0, "rate": None, "tip": 2
            # },
            "selected_points": False,  # 不使用积分支付，最好带上这个参数
            "delivery_window_id": ""
        })
        print("pre checkout result===>", pre)
        assert pre['object'], f"预结算失败，返回结果为：{pre}"
        checkout_amount = pre["object"]["fee_info"]["final_amount"]
        checkout_pre_id = pre["object"]["checkout_pre_id"]
        # 小费信息需要手动计算，否则checkout会失败
        if pre["object"]["fee_info"]["tip"] == '2.00':
            tip_options = {"index": 0, "rate": None, "tip": 2}
        elif pre["object"]["fee_info"]["tip"] == '3.00':
            tip_options = {"index": 1, "rate": None, "tip": 3}
        elif pre["object"]["fee_info"]["tip"] == '4.00':
            tip_options = {"index": 2, "rate": None, "tip": 4}
        else:
            tip_options = {"index": 3, "rate": None, "tip": 0}

        print("tip_options===>", tip_options)

        # 4. 调结算接口，使用venmo支付
        my_order = Checkout().checkout_wechat_or_paypal_v3(
            headers=headers,
            data={
                "referral_id": 0,
                "checkout_pre_id": checkout_pre_id,
                "checkoutAmount": checkout_amount,
                "cart_domain": "grocery",
                "plan_id": "",
                "delivery_window_id": "",
                "braintree_device_data": "",
                "tip_info": tip_options
            }
        )
        print("my order===>", my_order)
        assert my_order.get('object'), f"订单获取失败，请检查，返回数据为：{my_order}"
        assert my_order.get("object").get("order_ids"), f"订单获取失败，请检查，返回数据为：{my_order}"
        if my_order['object']['order_ids']:
            return my_order['object']
        else:
            return {
                "error": "获取订单号失败",
                "data": my_order
            }
