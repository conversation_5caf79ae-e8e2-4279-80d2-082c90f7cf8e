import re
import time
import requests
from weeeTest import jmespath
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction


class WelcomeCouponCheck:

    @staticmethod
    def header_background_img(welcome_coupons_res, headers):
        """
        welcome coupon 页面顶部图片判断
        :param welcome_coupons_res: 'global/welcome/coupons' response json
        :return:image_url_valid True/False
        """
        url = welcome_coupons_res['object']['background_image_url']
        print('check url', url)
        CommCheckFunction().comm_check_link(url, headers=headers)

    @staticmethod
    def header_title_style(welcome_coupons_res):
        """
        welcome coupon 页面顶部文案判断 金额文字色值
        :param welcome_coupons_res:
        :return:
        """
        header_title = welcome_coupons_res['object']['title_full']
        amount_color = header_title.split('color:')[1].split(';')[0]
        assert amount_color == '#D05C11', f'welcome coupon page header title style:amount color {amount_color} is not #D05C11'

    @staticmethod
    def single_coupon_basic_check(single_coupon):
        """
        mkpl welcome coupon 返回基础字段判断 seller_id seller_title plan_id end_time>当前时间
        :type single_coupon: object
        """
        assert type(
            single_coupon["seller_id"]) == int, f'coupon seller_id type {type(single_coupon["seller_id"])} is not int'
        assert type(single_coupon[
                        "seller_title"]) == str, f'coupon seller_title type {type(single_coupon["seller_title"])} is not str'
        assert len(single_coupon["seller_title"]) > 0, f'coupon seller_title is empty'
        assert type(single_coupon["plan_id"]) == int, f'coupon plan_id type {single_coupon["plan_id"]} is not int'
        assert single_coupon[
                   "end_time"] > time.time(), f'coupon end_time{single_coupon["end_time"]} exceed now time{time.time()}'

    @staticmethod
    def coupons_basic(welcome_coupons_res):
        """
        校验多个welcome Coupon 基础信息
        :param welcome_coupons_res:
        :return:
        """
        coupons = welcome_coupons_res['object']['coupons']
        for coupon in coupons:
            WelcomeCouponCheck.single_coupon_basic_check(coupon)

    @staticmethod
    def coupon_single_seller_products(coupon_product):
        """
        检验welcome Coupon 中卡片商品信息(不校验image_url: welcome coupon图片数量过多 执行时长过大)
        :param coupon_product:
        :return:
        """
        assert type(coupon_product["id"]) == int, f'coupon_product id {coupon_product["id"]} is not int'
        assert type(coupon_product["title"]) == str and len(
            coupon_product["title"]) > 0, f'coupon_product id {coupon_product["title"]} is not str or empty'

    @staticmethod
    def coupon_seller_products(welcome_coupons_res):
        """
        检验所有 coupon的商品
        :param welcome_coupons_res:
        :return:
        """
        for coupon in welcome_coupons_res['object']['coupons']:
            for product in coupon['products']:
                WelcomeCouponCheck.coupon_single_seller_products(product)

    @staticmethod
    def all_welcome_coupons(welcome_coupons_res, headers):
        WelcomeCouponCheck.header_background_img(welcome_coupons_res, headers=headers),
        WelcomeCouponCheck.header_title_style(welcome_coupons_res),
        WelcomeCouponCheck.coupons_basic(welcome_coupons_res),
        WelcomeCouponCheck.coupon_seller_products(welcome_coupons_res)


class BannerArrayCheck:
    @staticmethod
    def promo_banner_data(ds_banner_array, headers):
        data = ds_banner_array['object']['data']
        for banner in data:
            BannerArrayCheck.single_banner(banner, headers=headers)

    @staticmethod
    def single_banner(banner, headers):
        """
        global+ 的2x2 单个banner 字段检查
        :param banner:
        :return:
        """
        assert type(banner["key"]) == str and len(banner["key"]) > 0, 'banner key is not str or empty'
        assert 'mkpl_banner_' in banner["key"], f'promo banner key  {banner["key"]} is not mkpl_banner_ format'
        CommCheckFunction().comm_check_link(banner["image_url"], headers=headers)
        CommCheckFunction().comm_check_link(banner["link_url"], headers=headers)

    @staticmethod
    def promo_banner_rows(ds_banner_array, headers):
        """
        检查banner row和banner数量关系 2x2 、1x2
        :param ds_banner_array:
        :return:
        """
        banner_qty = len(ds_banner_array['object']['data'])
        rows = ds_banner_array['object']['rows']
        assert banner_qty == rows * 2, f'promo banner qty{banner_qty} and rows {rows} not match'

    @staticmethod
    def all_promo_banners(ds_banner_array, headers):
        """
        global+ 的2x2 所有banner 字段检查
        :param ds_banner_array:
        :return:
        """
        BannerArrayCheck.promo_banner_data(ds_banner_array, headers=headers),
        BannerArrayCheck.promo_banner_rows(ds_banner_array, headers=headers)


class WaterfallCheck:
    @staticmethod
    def cms_config(page_cms):
        """
        判断 global 页面cms datasource now link
        :param page_cms:
        :return:
        """
        datasource = page_cms['cmsData']['datasource']
        # now_links = [i['now'] for i in datasource if len(i['now']) > 0]
        # return len(datasource) == len(now_links)
        for ds in datasource:
            assert ds['now'].startswith(("/ec/item/",
                                         "/ec/marketplace/")), "cms config datasource now link does not start " \
                                                               "with/ec/item/ or /ec/marketplace/"

    @staticmethod
    def tabs(waterfall_res):
        """
        断言 global+ waterfall tab key title 非空 没有重复tab
        :param waterfall_res:
        :return:
        """
        tabs = waterfall_res['object']['tabs']
        # 断言title  key 非空
        for tab in tabs:
            assert tab['title'] and tab['key'], 'tab title or key is empty'
        tab_titles = jmespath(waterfall_res, 'object.tabs[*].title')
        tab_keys = jmespath(waterfall_res, 'object.tabs[*].key')
        deduplicate_tab_keys = list(set(tab_keys))
        # tab 去重后数量不变 没有重复tab
        assert len(tab_keys) == len(
            deduplicate_tab_keys), f'waterfall tab deduplicated:{deduplicate_tab_keys} compared with {tab_keys}'

    @staticmethod
    def content_item(feed_item):
        """
        断言 商品biz_type为seller;is_mkpl为true 商品remaining_count >=sales_min_order_quantity
        :param feed_item:
        :return:
        """
        assert feed_item['data']['product'][
                   'biz_type'] == 'seller', f"item content biz type{feed_item['data']['product']['biz_type']}: !=seller"
        assert feed_item['data']['product'][
            'is_mkpl'], f"item content is_mkpl:{feed_item['data']['product']['is_mkpl']}"
        assert int(feed_item['data']['product']['remaining_count']) >= int(
            feed_item['data']['product'][
                'sales_min_order_quantity']), f"item sales_min_order_quantity:{feed_item['data']['product']['remaining_count']}<remaining_count {feed_item['data']['product']['remaining_count']} "

    @staticmethod
    def content_seller(feed_seller):
        """
        断言seller卡片 商家id title link 描述 logo 评分 tag
        :param feed_seller:
        :return:
        """
        # seller_info_check = feed_seller['data']['seller']['id'] and feed_seller['data']['seller']['title']
        assert feed_seller['data']['seller']['id'] and feed_seller['data']['seller'][
            'title'], f'content seller id or title not exist{feed_seller}'
        assert str(feed_seller['data']['seller']['id']) in feed_seller['data']['seller'][
            'seller_url'], f"seller id {feed_seller['data']['seller']['id']}not in link{feed_seller['data']['seller']['seller_url']}"
        assert feed_seller['data']['seller'][
            'description'], f"seller card description {feed_seller['data']['seller']['description']}"
        assert 'weeecdn.com' in feed_seller['data']['seller'][
            'logo_url'], f"seller card logo url {feed_seller['data']['seller']['logo_url']}is not weeecdn"
        # 大数据推荐waterfall 卡片 seller 销量<50 时 没有tag
        if feed_seller['data']['seller']['tags']:
            for tag in feed_seller['data']['seller']['tags']:
                assert type(tag['title']) == str and len(tag['title']) > 0, f"card seller tag title {tag['title']}"

    @staticmethod
    def content_banners(feed_banners):
        """
        断言waterfall 组件banner 图片链接为weeecdn,status为A type:global_plus
        :param feed_banners:
        :return:
        """
        carousel_banners = feed_banners['data']['banners']['carousel']
        # check_banner = [banner for banner in carousel_banners if 'weeecdn.com' in banner['img']
        #                 and banner['status'] == 'A']
        for banner in carousel_banners:
            assert 'weeecdn.com' in banner['img'] and banner[
                'status'] == 'A', f"waterfall feeds banner img {banner['img']} status {banner['status']}"
            assert banner['type'] == 'global_plus', f"waterfall feeds banner type {banner['type']}"


class AllStoreCheck:
    """
    global+ 全部商店页面组件字段断言
    """

    @staticmethod
    def check_singe_seller_card(seller_card_obj):
        """
        all store 页面 商家卡片信息断言
        :param seller_card_obj: all store页面 单个商家卡片 object
        :return:
        """
        # 断言seller_id为int
        assert isinstance(seller_card_obj['seller_id'], int), "seller_id is not an integer"

        # 断言title为非空字符串
        assert isinstance(seller_card_obj['title'], str) and seller_card_obj[
            'title'].strip(), "title is empty or not a string"

        # 断言shipping_reminder_content包含'shipping'
        assert 'shipping' in seller_card_obj[
            'shipping_reminder_content'], "shipping_reminder_content does not contain 'shipping'"

        # 断言seller_url和more_link结尾和seller_id相同
        seller_id_str = str(seller_card_obj['seller_id'])
        assert seller_card_obj['seller_url'].endswith(seller_id_str), "seller_url does not end with seller_id"
        if len(seller_card_obj.get('products')) >= 10:
            assert seller_card_obj['more_link'].endswith(seller_id_str), "more_link does not end with seller_id"

        # 遍历所有seller tags
        for tag in seller_card_obj['seller_tags']:
            # 根据type进行不同的断言
            if tag['type'] == 'coupon':
                # 断言image_url包含'weeecdn.com'
                assert 'weeecdn.com' in tag['image_url'], "coupon tag's image_url does not contain 'weeecdn.com'"
            elif tag['type'] == 'discount':
                # 断言tag包含'off'
                assert 'off' in tag['tag'], "discount tag does not contain 'off'"
            elif tag['type'] == 'new':
                # 断言tag为'New arrival'
                assert tag['tag'] == 'New arrival', "new tag is not 'New arrival'"
            elif tag['type'] == 'sales_volume':
                # 断言tag包含'sold'
                assert 'Sold' in tag['tag'], "sales_volume tag does not contain 'sold'"

    @staticmethod
    def check_seller_card_product(seller_card_product):
        """
        检查 商家卡片中商品
        :param seller_card_product: all store 页面商家卡片中商品
        :return:
        """
        # 断言img_urls中的所有链接都包含'weeecdn.com'
        for img_url in seller_card_product['img_urls']:
            assert 'weeecdn.com' in img_url, f"img_url does not contain 'weeecdn.com': {img_url}"

        # 断言商品sold_status为available
        assert seller_card_product[
                   'sold_status'] == 'available', f"sold_status is not 'available': {seller_card_product['sold_status']}"

        # 断言sales_max_order_quantity >= sales_min_order_quantity
        assert seller_card_product['sales_max_order_quantity'] >= seller_card_product['sales_min_order_quantity'], \
            "sales_max_order_quantity is less than sales_min_order_quantity"

        # 断言id为int
        assert isinstance(seller_card_product['id'], int), f"id is not an integer: {seller_card_product['id']}"

        # 断言name为非空字符串
        assert isinstance(seller_card_product['name'], str) and seller_card_product[
            'name'].strip(), "name is empty or not a string"

        # 断言price为整数或1到2位小数
        assert isinstance(seller_card_product['price'], (int, float)) and re.match(r"^\d+(\.\d{1,2})?$",
                                                                                   str(seller_card_product['price'])), \
            f"price is not an integer or a float with 1-2 decimal places: {seller_card_product['price']}"

        # 断言view_link结尾为id
        assert seller_card_product['view_link'].endswith(str(seller_card_product['id'])), \
            f"view_link does not end with id: {seller_card_product['view_link']}"

        # 断言vender_id不等于5044
        assert seller_card_product['vender_id'] != 5044, f"product vender_id is 4: {seller_card_product['vender_id']}"

        # 断言biz_type为seller
        assert seller_card_product[
                   'biz_type'] == 'seller', f"biz_type is not 'seller': {seller_card_product['biz_type']}"
