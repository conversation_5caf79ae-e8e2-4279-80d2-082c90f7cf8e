"""
<AUTHOR>  suqin
@Version        :  V1.0.0
------------------------------------
@File           :  RemoveAllProductsInCart.py.py
@Description    :
@CreateTime     :  2023/8/12 17:40
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/8/12 17:40
"""
import datetime
import json
import time

import weeeTest
from weeeTest import log

from test_data.ec.simple.writejenkinslog import write_debug_log_on_jenkins
from test_dir.api.ec.ec_customer.account_rest.account_rest import AccountRest
from test_dir.api.ec.ec_customer.language_rest.language_rest import LanguageRest
from test_dir.api.ec.ec_item.store.api_store import ApiStore
from test_dir.api.ec.ec_so.deliverydates.get_valid_delivery_dates import GetValidDeliveryDates
from test_dir.api.ec.ec_so.preorder.porder_date import PorderDate
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_so.preorder.update_zipcode import UpdateZipcode


class SetUserPorder(weeeTest.TestCase):

    @staticmethod
    def set_user_header_porder(headers, zipcode, language):
        # 1. 切销售组织，更新zipcode
        update_zipcode = UpdateZipcode().update_zipcode_v1(headers=headers, zipcode=zipcode)
        assert update_zipcode.get("object") == "Success", f'切換zipcode失败，请确认{update_zipcode["object"]}'
        # 1.1 更新header里的zipcode
        headers.update({
            "Weee-Zipcode": zipcode,
            "Zipcode": zipcode
        })
        # print(_monitor_header)
        # 2. 获取最新日期
        delivery_res = GetValidDeliveryDates().so_delivery_date(headers=headers)
        assert delivery_res.get('object').get('delivery'), f"当前zipcode{zipcode} porder日期异常"
        # 2.1 改porder 默认拿第一天
        delivery_date = ""
        if delivery_res.get('object').get('delivery'):
            # 默认拿第一天
            delivery_date = delivery_res.get('object').get('delivery')[0].get('date')
            # 最后一天
            new_date = delivery_res.get('object').get('delivery')[-1].get('date')
            # 切换日期
            porder_date = PorderDate().porder_date(headers=headers,
                                                   delivery_pickup_date=delivery_date)
            # 断言
            assert porder_date["result"] is True
        # 2.2 更新header里的日期
        headers.update({
            "Weee-Date": delivery_date
        })
        # print(_monitor_header)
        # 3. 更新语言
        _language = LanguageRest().update_account_language(headers=headers, lang=language)
        assert _language.get("result") is True
        # 3.1 更新header的语言
        headers.update({
            "Lang": language
        })
        # if language != "en":
        #     try:
        #         del headers['Weee-Store']
        #     except Exception as e:
        #         log.info("header里没有weee-store")


        # print(_monitor_header)
        # if language == 'en':
        #     # 4.获取store id
        #     store_res = ApiStore().store_list(headers=headers, zipcode=zipcode)
        #     store = [(item['store_id'], item['store_key']) for item in store_res['object']]
        #     for store_id, store_key in store:
        #         # 4.1 更新store
        #         ApiStore().store_select(headers=headers, store_id=store_id, zipcode=int(zipcode))
        #         # 4.2 更新header里的store
        #         headers.update({
        #             "Weee-Store": store_key
        #         })

    def set_user_zipcode(self, headers, zipcode):
        update_zipcode = UpdateZipcode().update_zipcode_v1(headers=headers, zipcode=zipcode)

        assert update_zipcode.get("object") == "Success", f'切換zipcode失败，请确认{update_zipcode["object"]}'
        # 获取用户的preorder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=headers)["object"]
        new_date = (datetime.datetime.now() + datetime.timedelta(days=2)).strftime("%Y-%m-%d")
        headers.update({
            "Weee-Zipcode": porder.get("zipcode"),
            "Weee-Date": porder.get('delivery_pickup_date') if porder.get('delivery_pickup_date') != str(
                datetime.date.today()) else new_date,
            "weee-sales-org-id": str(porder.get('sales_org_id')),
            "Zipcode": porder.get("zipcode")
        })

        if porder.get('delivery_pickup_date') == str(datetime.date.today()):
            PorderDate().porder_date(headers=headers, delivery_pickup_date=new_date)
        assert porder["zipcode"] == str(zipcode), f'切換zipcode={zipcode}失败，请确认{porder["zipcode"]}'
        return porder

    def set_user_porder(self, headers, zipcode: str = "98011",
                        lang: str = "en", store_id: int = 1,
                        store_key: str = "cn"):
        """初始化默认用户的porder:zipcode =98011,语言=en，store =cn"""
        # 1. 切销售组织，更新zipcode
        update_zipcode = UpdateZipcode().update_zipcode_v1(headers=headers, zipcode=zipcode)
        assert update_zipcode.get("object") == "Success", f'切換zipcode失败，请确认{update_zipcode["object"]}'
        # 1.1 更新header里的zipcode
        headers.update({
            "Weee-Zipcode": zipcode,
            "Zipcode": zipcode
        })
        delivery_pickup_date = ''
        # 更新用户的语言为en
        language = LanguageRest().update_account_language(headers=headers, lang=lang)
        headers.update({
            "Lang": lang
        })

        # 断言
        assert language["message_id"] == "10000", f"更新用户的语言为en失败"

        account_simple_info = AccountRest().account_simple_info(headers=headers)
        # 断言
        assert account_simple_info["result"] is True
        assert account_simple_info["object"]["language"] == "en", f"查询用户信息失败或用户语言不为en"

        # 切换用户store到cn
        store_select = ApiStore().store_select(headers=headers, store_id=store_id, zipcode="98011")
        if store_select.get("result") is not True:
            time.sleep(5)
            store_select = ApiStore().store_select(headers=headers, store_id=store_id, zipcode="98011")
        # 断言
        assert store_select.get("result") is True, f"切换用户store到cn失败, resp={store_select}"
        headers.update({
            "Weee-Store": store_key
        })

        # 获取用户送货日期
        delivery_date = GetValidDeliveryDates().so_delivery_date(headers=headers)
        # 默认拿第一天
        delivery_pickup_date = delivery_date["object"]["delivery"][0]["date"]
        # 最后一天
        new_date = delivery_date.get('object').get('delivery')[-1].get('date')

        # 断言
        assert delivery_date["result"] is True, f"获取用户送货日期失败"
        # 更新用户送货日期
        porder_date = PorderDate().porder_date(headers=headers, delivery_pickup_date=delivery_pickup_date)

        # 断言
        assert porder_date["result"] is True, f"更新用户送货日期失败"

        # 获取用户的preorder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=headers)
        # 断言
        assert porder["result"] is True, f"获取用户porder失败"
        headers.update({
            "Weee-Zipcode": porder.get('object').get("zipcode"),
            "Weee-Date": porder.get('object').get('delivery_pickup_date') if porder.get('object').get(
                'delivery_pickup_date') != str(
                datetime.date.today()) else new_date,
            "weee-sales-org-id": str(porder.get('object').get('sales_org_id')),
            "Zipcode": porder.get('object').get("zipcode")
        })
        # write_debug_log_on_jenkins("./logs/porder.txt",
        #                            json.dumps(porder.response) + "headers:" + json.dumps(headers) + "\n")

        return porder["object"]

    def set_user_new_porder(self, headers, zipcode, lang, store_id: int = 1):
        """根据实际情况设置用户的porder"""
        # 1. 切销售组织，更新zipcode
        update_zipcode = UpdateZipcode().update_zipcode_v1(headers=headers, zipcode=zipcode)
        assert update_zipcode.get("object") == "Success", f'切換zipcode失败，请确认{update_zipcode["object"]}'
        # 1.1 更新header里的zipcode
        headers.update({
            "Weee-Zipcode": zipcode,
            "Zipcode": zipcode
        })
        # print(_monitor_header)
        # 2. 获取最新日期
        delivery_res = GetValidDeliveryDates().so_delivery_date(headers=headers)
        assert delivery_res.get('object').get('delivery'), f"当前zipcode{zipcode} porder日期异常"
        # 2.1 改porder 默认拿第一天
        delivery_date = ""
        if delivery_res.get('object').get('delivery'):
            # 默认拿第一天
            delivery_date = delivery_res.get('object').get('delivery')[0].get('date')
            # 最后一天
            new_date = delivery_res.get('object').get('delivery')[-1].get('date')
            # 切换日期
            porder_date = PorderDate().porder_date(headers=headers,
                                                   delivery_pickup_date=delivery_date)
            # 断言
            assert porder_date["result"] is True
        # 2.2 更新header里的日期
        headers.update({
            "Weee-Date": delivery_date
        })
        # print(_monitor_header)
        # 3. 更新语言
        _language = LanguageRest().update_account_language(headers=headers, lang=lang)
        assert _language.get("result") is True
        # 3.1 更新header的语言
        headers.update({
            "Lang": lang
        })
        if lang == 'en':
            # 4.1 更新store
            ApiStore().store_select(headers=headers, store_id=store_id, zipcode=int(zipcode))
            # 4.2 更新header里的store
            headers.update({
                "Weee-Store": "cn"
            })

        # # 获取用户的preorder
        # porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=headers).get("object")
        # new_date = (datetime.datetime.now() + datetime.timedelta(days=2)).strftime("%Y-%m-%d")
        # headers.update({
        #     "Weee-Zipcode": porder.get("zipcode"),
        #     "Weee-Date": porder.get('delivery_pickup_date') if porder.get('delivery_pickup_date') != str(
        #         datetime.date.today()) else (datetime.datetime.now() + datetime.timedelta(days=2)).strftime(
        #         "%Y-%m-%d"),
        #     "weee-sales-org-id": str(porder.get('sales_org_id')),
        #     "Zipcode": porder.get("zipcode")
        # })
        # if porder.get('delivery_pickup_date') == str(datetime.date.today()):
        #     PorderDate().porder_date(headers=headers, delivery_pickup_date=new_date)
        # # 断言
        # assert self.response["result"] is True
        # return self.response["object"]

        #
        # # 更新用户的zipcode
        # UpdateZipcode().update_zipcode_v1(headers=headers, zipcode=zipcode)
        # # 断言
        # assert self.response["result"] is True
        # # 更新用户的语言为en
        # LanguageRest().update_account_language(headers=headers, lang=lang)
        # # 断言
        # assert self.response["result"] is True
        # if lang == "en":
        #     # 切换用户store到cn
        #     ApiStore().store_select(headers=headers, store_id=store_id, zipcode=zipcode)
        #     # 断言
        #     assert self.response["result"] is True
        # # 获取用户送货日期
        # GetValidDeliveryDates().so_delivery_date(headers=headers)
        # # 断言
        # assert self.response["result"] is True
        # delivery_pickup_date = self.response["object"]["delivery"][1]["date"]
        # # 更新用户送货日期
        # PorderDate().porder_date(headers=headers, delivery_pickup_date=delivery_pickup_date)
        # # 断言
        # assert self.response["result"] is True
        # # 获取用户的preorder
        # porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=headers).get("object")
        # new_date = (datetime.datetime.now() + datetime.timedelta(days=2)).strftime("%Y-%m-%d")
        # headers.update({
        #     "Weee-Zipcode": porder.get("zipcode"),
        #     "Weee-Date": porder.get('delivery_pickup_date') if porder.get('delivery_pickup_date') != str(
        #         datetime.date.today()) else (datetime.datetime.now() + datetime.timedelta(days=2)).strftime("%Y-%m-%d"),
        #     "weee-sales-org-id": str(porder.get('sales_org_id')),
        #     "Zipcode": porder.get("zipcode")
        # })
        # if porder.get('delivery_pickup_date') == str(datetime.date.today()):
        #     PorderDate().porder_date(headers=headers, delivery_pickup_date=new_date)
        # # 断言
        # assert self.response["result"] is True
        # return self.response["object"]

    def get_last_delivery_date(self, headers):
        # 获取用户最后一个送货日期
        delivery_date = GetValidDeliveryDates().so_delivery_date(headers=headers)
        # 断言
        assert delivery_date["result"] is True
        delivery_pickup_date = delivery_date["object"]["delivery"][-1]["date"]
        # 更新用户送货日期
        porder_date = PorderDate().porder_date(headers=headers, delivery_pickup_date=delivery_pickup_date)
        assert porder_date["result"] is True
        # 2.2 更新header里的日期
        headers.update({
            "Weee-Date": delivery_pickup_date
        })
        # 获取用户的preorder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=headers)["object"]
        return porder
