import sys


def get_project_dir():
    """
    获取项目根目录-run文件所在目录
    """
    root_path = ''
    print("sys.path===>", sys.path)
    for p in sys.path:
        if ((p.endswith("qa-ec") or p.endswith("EC-Debug-Dev-Single")
                or p.endswith("EC-Track-Data") or p.endswith("EC-Daily") or p.endswith("EC-Monitor")
                or p.endswith("EC-Smoke-Product")) or p.endswith("EC-Daily-Dev")
                or p.endswith("EC-New-Sales-Organs-Monitor") or p.endswith("EC-EventTracking")) or p.endswith("EC-Monitor-Simplified-Version"):
            root_path = p
            break
    if root_path:
        return root_path
    else:
        raise Exception(f"获取root_path失败：root_path={root_path}")


if __name__ == '__main__':
    r = get_project_dir()
    print(r)
