import json
import os

import weeeTest
from weeeTest import jmespath, log
from random import choice

from test_data.ec.simple.common import Header
from test_dir.api.ec.ec_customer.account_rest.account_rest import AccountRest
from test_dir.api.ec.ec_item.search_v3.search_by_catalogue_content import SearchByCatalogueContent
from test_dir.api.ec.ec_so.address.apply_user_address_information import ApplyUserAddressInformation
from test_dir.api.ec.ec_so.address.query_user_address_list import QueryUserAddressList
from test_dir.api.ec.ec_so.order.checkout import Checkout
from test_dir.api.ec.ec_so.order.prepare_checkout import PrepareCheckout
from test_dir.api.ec.ec_so.preorder.payment_category import PaymentCategory
from test_dir.api.ec.ec_so.preorder.query_preorder import QueryPreOrder
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine
from test_dir.api.ec.ec_so.preorder.update_zipcode import UpdateZipcode
from test_dir.api_case.ec.common.comm_check_products_with_cart import CommCheckProductsWithCart
from test_dir.api_case.ec.common.remove_all_products_in_cart import RemoveAllProductsInCart


class CommonOrder(weeeTest.TestCase):
    def get_order_id(self, headers, add_id):
        """
        get order with payment, just for tb1 env
        前提条件：
        **** 1. 要确保要购买的商品在所有地址列表的zipcode上有货，且库存充足 ****
        **** 2. 要确保积分充足 ****
        """
        # 1. 获取用户porder
        UpdateZipcode().update_zipcode_v2(headers=headers)
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=headers)
        deal_date = porder["object"]["deal_date"]
        zipcode = porder["object"]["zipcode"]
        # **大量造订单时，清空购物车的步骤可去掉**
        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=headers)
        # 2. 查询可售商品，准备加购
        # normal = SearchByCatalogueContent().search_by_catalogue_content(
        #     headers=headers,
        #     zipcode=zipcode,
        #     date=deal_date,
        #     sort="recommend",
        #     filter_sub_category="sale",
        #     filters=json.dumps({"delivery": True})
        # )
        # items = jmespath(normal, "object.contents")
        # product_list = []
        # for item in items:
        #     # 通过分类获取type不是carousel并且商品状态不是change_other_day的商品
        #     if item["data"]["sold_status"] == "available" and item['type'] == 'product':
        #         product_list.append([item["data"]["id"], item['data']['min_order_quantity']])
        #
        # assert product_list, f'filter_sub_category=snack的商品不存在，product list为空，{product_list}'
        # 3. 加购商品， 固定prouct_id=101090
        # detail = PdpDetail().pdp_detail(headers=headers, product_id=101090)
        # assert detail['object']['product']['sold_status'] == 'available', f"101090商品不可出售，商品详情为：{detail}"
        r = UpdatePreOrderLine().porder_items_v3(
            headers=headers,
            # product_id=95442,   # 这个商品库存经过特殊处理过，应该一直可以购买 85527
            product_id=44838,   # 这个商品库存经过特殊处理过，应该一直可以购买 85527
            quantity=1
        )

        print("r===>", r)

        # assert self.response['result'] is True and self.response['object'][
        #     'updateItems'] != [], f"加购失败，self.response is {self.response}"
        #
        # CommCheckProductsWithCart().check_product_exists_in_cart(
        #     headers=headers,
        #     cart_domain='grocery',
        #     product_id=95442)

        cart = QueryPreOrder().query_preorder_v5(headers=headers)
        # final_amount = cart["object"]["final_amount"]
        # 4. 查询及应用地址
        # address_list = QueryUserAddressList().address_list(headers)
        # address_ids = [address["id"] for address in address_list["object"] if
        #                len(address_list["object"]) > 0 and "98011" in address["address"]]
        # assert address_ids, f"没有符合条件的地址，地址列表为{address_ids}"
        # 4.1 应用地址
        ApplyUserAddressInformation().address_apply_v2(headers, add_id.get('id'))
        # 5. 切换支付方式为P, 积分支付， 必须先切换支付方式，再pre checkout, payment_category不能传None,否则此接口报错
        # 5.1 payment_category为B代表信用卡，积分没有对应的payment_category, 但不能传None，只要points=True即可
        PaymentCategory().payment_category(headers, "B")
        # 6. 预结算
        pre = PrepareCheckout().prepare_checkout_v2(headers, "grocery")
        if pre.get('message') == 'Your cart is empty.':
            log.info(f"your cart is empty: address={add_id.get('address')}, zipcode={add_id.get('addr_zipcode')}")
            return "your cart is empty."
        print("address_id===>", add_id)
        checkout_amount = pre["object"]["fee_info"]["final_amount"]
        checkout_pre_id = pre["object"]["checkout_pre_id"]

        # 6.1 用户如果积分<20000,则充值20000
        AccountRest().me_page_pc_account_portal(headers=headers)
        sections = self.response["object"]["sections"]
        customer_points = list(filter(lambda f: f["section_name"] == "my_points", sections))[0]["data"]["item_value"]
        # try:
        #     if customer_points < 20000 and self.env["env_url"] == 'https://api.tb1.sayweee.net':
        #         Engagement().buy_reward_points(headers=headers, **{
        #             "userId": porder['object']['user_id'],
        #             "points": 20000,
        #             "typeId": 28
        #         })
        #         assert self.response["object"]["pointsIssueResponseItemList"][0]["uniqBizId"] is not None
        #         assert self.response["object"]["pointsIssueResponseItemList"][0]["result"] is True
        #         assert self.response["object"]["pointsIssueResponseItemList"][0]["message"] == "success"
        # except Exception as e:
        #     log.info(f"给用户{porder['object']['user_id']}充值失败" + str(e))

        # 7. 使用积分支付
        my_order = None
        if self.env["env_url"] == 'https://api.tb1.sayweee.net':
            try:
                my_order = Checkout().checkout_v3(headers, "grocery", checkout_pre_id, checkout_amount)
            except Exception as e:
                log.info("产生订单失败===>" + str(e))
            print("my order===>", my_order)

            if my_order.get('object') and my_order.get('object').get('order_ids'):
                log.info(f"创建订单成功，订单号为：{my_order['object']['order_ids']}, address={add_id.get('address')}, zipcode={add_id.get('addr_zipcode')}")
                return my_order['object']
            else:
                log.info(f"创建订单失败，订单号为：{my_order}, address={add_id.get('address')}, zipcode={add_id.get('addr_zipcode')}")
                return {
                    "error": "获取订单号失败",
                    "data": my_order
                }
        else:
            return {
                "error": "目前不是tb1环境，不能支付"
            }

    def get_unpaid_order(self, headers):
        """
            get order without payment
        """
        # 1. 获取用户porder
        UpdateZipcode().update_zipcode_v2(headers=headers)
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=headers)
        deal_date = porder["object"]["deal_date"]
        zipcode = porder["object"]["zipcode"]

        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=headers)
        # 2. 查询可售商品，准备加购
        normal = SearchByCatalogueContent().search_by_catalogue_content(
            headers=headers,
            zipcode=zipcode,
            date=deal_date,
            sort="recommend",
            filter_sub_category="snack",
            filters=json.dumps({"delivery": True})
        )
        items = jmespath(normal, "object.contents")
        product_list = []
        for item in items:
            # 通过分类获取type不是carousel并且商品状态不是change_other_day的商品
            if item["data"]["sold_status"] == "available" and item['type'] == 'product':
                product_list.append([item["data"]["id"], item['data']['min_order_quantity']])

        assert product_list, f'filter_sub_category=snack的商品不存在，product list为空，{product_list}'
        # 3. 加购商品
        for index, product in enumerate(product_list):
            UpdatePreOrderLine().porder_items_v3(
                headers=headers,
                product_id=product[0],
                quantity=product[1]
            )

            assert self.response['result'] is True and self.response['object'][
                'updateItems'] != [], f"加购失败，self.response is {self.response}"

            CommCheckProductsWithCart().check_product_exists_in_cart(
                headers=headers,
                cart_domain='grocery',
                product_id=product[0])

            if index == 2:
                break

        cart = QueryPreOrder().query_preorder_v5(headers=headers)
        # final_amount = cart["object"]["final_amount"]
        # 4. 查询及应用地址
        # address_list = QueryUserAddressList().address_list(headers)
        # address_ids = [address["id"] for address in address_list["object"] if
        #                len(address_list["object"]) > 0 and "98011" in address["address"]]
        # assert address_ids, f"没有符合条件的地址，地址列表为{address_ids}"
        # 4.1 应用地址
        ApplyUserAddressInformation().address_apply_v2(headers, address_ids[0])
        # 5. 切换支付方式，一定要放在pre checkout之前
        payment = PaymentCategory().payment_category(headers, "P", False)
        print("payment===>", payment)
        # 6. 预结算
        pre = PrepareCheckout().prepare_checkout_wechat_or_paypal_v2(headers, data={
            "cart_domain": "grocery",
            # 不需要填写小费信息
            # "tip_option": {
            #     "index": 0, "rate": None, "tip": 2
            # },
            "selected_points": False,   # 不使用积分支付，最好带上这个参数
            "delivery_window_id": ""
        })
        print("pre checkout result===>", pre)
        checkout_amount = pre["object"]["fee_info"]["final_amount"]
        checkout_pre_id = pre["object"]["checkout_pre_id"]
        # 小费信息需要手动计算，否则checkout会失败
        if pre["object"]["fee_info"]["tip"] == '2.00':
            tip_options = {"index": 0, "rate": None, "tip": 2}
        elif pre["object"]["fee_info"]["tip"] == '3.00':
            tip_options = {"index": 1, "rate": None, "tip": 3}
        elif pre["object"]["fee_info"]["tip"] == '4.00':
            tip_options = {"index": 2, "rate": None, "tip": 4}
        else:
            tip_options = {"index": 3, "rate": None, "tip": 0}

        print("tip_options===>", tip_options)

        # 7. 使用paypal支付
        print("checkout_request_body===>", {
                "referral_id": 0,
                "checkout_pre_id": checkout_pre_id,
                "checkoutAmount": checkout_amount,
                "cart_domain": "grocery",
                "plan_id": "",
                "delivery_window_id": "",
                "braintree_device_data": "",
                "tip_info": tip_options
            })

        my_order = Checkout().checkout_wechat_or_paypal_v3(
            headers=headers,
            data={
                "referral_id": 0,
                "checkout_pre_id": checkout_pre_id,
                "checkoutAmount": checkout_amount,
                "cart_domain": "grocery",
                "plan_id": "",
                "delivery_window_id": "",
                "braintree_device_data": "",
                "tip_info": tip_options
            }
        )
        print("my order===>", my_order)
        if my_order['object']['order_ids']:

            return my_order['object']
        else:
            return {
                "error": "获取订单号失败",
                "data": my_order
            }


if __name__ == '__main__':
    # header = Header().login_header("<EMAIL>", "1234abcd")
    header = Header().login_header(os.getenv("user_name", None) or "<EMAIL>", os.getenv("password", None) or "1234abcd")
    address_list = QueryUserAddressList().address_list(header)
    all_address = address_list.get('object')
    address_ids = [address["id"] for address in address_list["object"]]
    assert address_ids, f"没有符合条件的地址，地址列表为{address_ids}"
    addr_reverse = address_ids[::-1]
    for index, add in enumerate(all_address):
        try:
            # res = CommonOrder().get_order_id(header, add)
            res = CommonOrder().get_order_id(header, choice(all_address))
            # if res['order_ids']:    # 以防止获取失败
            #     "你的业务代码"

            # res = CommonOrder().get_unpaid_order(header)
            if index == int(os.getenv("order_count")) if os.getenv("order_count", None) else 100:
                break
            print("res===>", res)
        except Exception as e:
            print("获取订单失败" + str(e))


