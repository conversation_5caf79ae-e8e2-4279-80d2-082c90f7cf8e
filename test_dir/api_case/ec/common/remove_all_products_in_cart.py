"""
<AUTHOR>  suqin
@Version        :  V1.0.0
------------------------------------
@File           :  RemoveAllProductsInCart.py.py
@Description    :
@CreateTime     :  2023/8/12 17:40
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/8/12 17:40
"""
import logging

import weeeTest
from weeeTest import jmespath

from test_dir.api.ec.ec_so.preorder.query_preorder import QueryPreOrder
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine
from test_dir.api.ec.ec_so.save_for_later.save_for_later_item_move_to_cart import SaveForLaterItemMoveToCart


class RemoveAllProductsInCart(weeeTest.TestCase):
    def remove_all_products_in_grocery_cart(self, headers):
        """清空用户生鲜购物车"""
        # 获取用户的preorder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=headers).get("object")
        # 获取生鲜购物车成功
        preorder_v5 = QueryPreOrder().query_preorder_v5(headers=headers, cart_domain="grocery")
        assert preorder_v5["result"] is True, f'购物车接口一次，请确认{preorder_v5}'
        sections = preorder_v5["object"]["sections"]
        invalid_items = preorder_v5["object"]["invalid_items"]
        # 获取购物车下稍后购买商品模块
        save_for_later = SaveForLaterItemMoveToCart().save4later_v2(headers=headers)
        assert save_for_later["result"] is True, f'save for later 接口异常，请确认{save_for_later}'
        save_for_later_items = save_for_later["object"]["items"]

        if len(sections) > 0:
            # 获取购物车下sections模块
            for section in sections:
                items = section.get("items")

                for item in items:
                    # 移除购物车正常商品
                    remove_cart = UpdatePreOrderLine().remove_cart_v3(headers=headers,
                                                                      product_id=item.get("product_id"),
                                                                      date=porder["delivery_pickup_date"],
                                                                      refer_type=section["cart_id"])
                    assert remove_cart["result"] is True
                # # 删除活动换购商品、赠品
                # # 获取grocery 购物车
                # grocery_cart = QueryPreOrder().query_preorder_v5(headers=headers, cart_domain="grocery")
                # # 购物车活动商品： *多个购物车，购物车有*多个活动，活动下有*多个商品
                nested_activity_items = jmespath(preorder_v5, "object.sections[*].activity_info[*].items[*].product_id")
                activity_item_lst = [item for cart in nested_activity_items for activity in cart for item in activity]
                if activity_item_lst:
                    for item in activity_item_lst:
                        # 移除换购商品、赠品
                        UpdatePreOrderLine().remove_cart_v3(headers=headers, product_id=item,
                                                            date=porder["delivery_pickup_date"])


        # 获取购物车下失效商品模块
        if len(invalid_items) > 0:
            for item in invalid_items:
                # 移除购物车失效商品
                remove_cart = UpdatePreOrderLine().remove_cart_v3(headers=headers, product_id=item.get("product_id"),
                                                                  date=porder["delivery_pickup_date"])
                assert remove_cart["result"] is True
            # # 删除活动换购商品、赠品
            # # 获取grocery 购物车
            # grocery_cart = QueryPreOrder().query_preorder_v5(headers=headers, cart_domain="grocery")
            # # 购物车活动商品： *多个购物车，购物车有*多个活动，活动下有*多个商品
            # nested_activity_items = jmespath(grocery_cart, "object.sections[*].activity_info[*].items[*].product_id")
            # activity_item_lst = [item for cart in nested_activity_items for activity in cart for item in activity]
            # if activity_item_lst:
            #     for item in activity_item_lst:
            #         # 移除换购商品、赠品
            #         UpdatePreOrderLine().remove_cart_v3(headers=headers, product_id=item,
            #                                             date=porder["delivery_pickup_date"])

            # 获取购物车下稍后购买商品模块
        if save_for_later_items:
            for item in save_for_later_items:
                # 移除购物车save_for_later商品
                remove_cart = UpdatePreOrderLine().remove_save_for_later(headers=headers,
                                                                         product_keys=item.get("product_id"))
                assert remove_cart["result"] is True
        else:
            # assert len(sections) == 0, f'购物车接口一次，请确认{preorder_v5}'
            logging.info("购物车已为空")

        # 再次获取购物车接口，确认购物车已清空
        preorder_v5 = QueryPreOrder().query_preorder_v5(headers=headers, cart_domain="grocery")

        assert len(preorder_v5["object"]["sections"]) == 0, f'删除购物车商品后，第二次查询购物车，请确认{preorder_v5}'

    def clear_grocery_cart(self, headers):
        """
        清空购物车item
        """
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=headers)["object"]
        date = porder["delivery_pickup_date"]
        grocery_cart = QueryPreOrder().query_preorder_v5(headers=headers, cart_domain="grocery")
        # 最外层invalid_items
        invalid_items = jmespath(grocery_cart, "object.invalid_items[*].product_id")
        # save_for_later_items
        save_for_later_items = jmespath(grocery_cart, "object.save_for_later_response.items[*].product_id")
        # section items
        all_section_items = []
        # 遍历section
        for section in grocery_cart['object']['sections']:
            section_items = []
            # 遍历 section下activity中的activity_items gift_items items
            for activity in section['activity_info']:
                activity_items = jmespath(activity, 'activity_items[*].product_id')
                gift_items = jmespath(activity, 'gift_items[*].product_id')
                items = jmespath(activity, 'items[*].product_id')
                # activity下商品合并list
                if activity_items:
                    section_items.extend(activity_items)
                if gift_items:
                    section_items.extend(gift_items)
                if items:
                    section_items.extend(items)
            # 购物车普通商品
            common_items = jmespath(section, 'items[*].product_id')
            if common_items:
                section_items.extend(common_items)
            all_section_items.extend(section_items)
        # 合并所有item list
        all_items = []
        if invalid_items:
            all_items.extend(invalid_items)
        if save_for_later_items:
            all_items.extend(save_for_later_items)
        if all_section_items:
            all_items.extend(all_section_items)
        # 清空购物车item
        for item in all_items:
            UpdatePreOrderLine().remove_cart_v3(headers=headers, product_id=item,
                                                date=date)

    def remove_all_products_in_restaurant_cart(self, headers):
        """清空用户餐馆菜购物车"""
        # 餐馆菜的日期有问题，不是preorder
        # 获取登录header
        # headers = LoginHeader().login_header(email=args[0]["login"]["email"], password=args[0]["login"]["password"])
        # 获取用户的preorder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=headers)["object"]
        # 获取餐馆菜购物车成功
        QueryPreOrder().query_preorder_v5(headers=headers, cart_domain="restaurant")
        if self.response["object"] is not None:
            # 获取购物车下sections模块
            sections = QueryPreOrder().query_preorder_v5(headers=headers, cart_domain="restaurant")["object"][
                "sections"]
            if sections is not None:
                for section in sections:
                    items = section.get("items")
                    for item in items:
                        # 移除购物车正常商品
                        UpdatePreOrderLine().remove_cart_v3(headers=headers, product_id=item.get("product_id"),
                                                            date=porder["delivery_pickup_date"],
                                                            refer_type="hotdish")
            # 获取购物车下失效商品模块
            invalid_items = QueryPreOrder().query_preorder_v5(headers=headers, cart_domain="grocery")["object"][
                "invalid_items"]
            if invalid_items is not None:
                for item in invalid_items:
                    # 移除购物车失效商品
                    UpdatePreOrderLine().remove_cart_v3(headers=headers, product_id=item.get("product_id"),
                                                        date=porder["delivery_pickup_date"])

            # 获取购物车下稍后购买商品模块
            save_for_later = QueryPreOrder().query_preorder_v5(headers=headers, cart_domain="grocery")["object"][
                "save_for_later_response"]
            if save_for_later is not None:
                if save_for_later["items"] is not None:
                    for item in save_for_later["items"]:
                        # 移除购物车save_for_later商品
                        UpdatePreOrderLine().remove_save_for_later(headers=headers, product_keys=item.get("product_id"))
        else:
            logging.info("购物车为空")


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
