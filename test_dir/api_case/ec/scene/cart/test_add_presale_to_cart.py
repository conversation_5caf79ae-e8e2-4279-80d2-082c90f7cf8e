import json

import pytest
import weeeTest

from test_dir.api.ec.ec_item.search_v3.search_by_catalogue_content import SearchByCatalogueContent
from test_dir.api.ec.ec_so.preorder.query_preorder import QueryPreOrder
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine
from test_dir.api.ec.ec_so.save_for_later.cart_to_save_for_later import CartToSaveForLater
from test_dir.api_case.ec.common.common_check import CommonCheck
from test_dir.api_case.ec.common.remove_all_products_in_cart import RemoveAllProductsInCart
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder


class TestPresaleToCart(weeeTest.TestCase):
    @pytest.fixture(scope='function', autouse=True)
    def setup_class(self, ec_zhuli_header):
        SetUserPorder().set_user_zipcode(ec_zhuli_header, "99991")
        yield
        SetUserPorder().set_user_zipcode(ec_zhuli_header, "98011")

    @weeeTest.mark.list('Regression')
    def test_presale_product_to_cart(self, ec_zhuli_header):
        """mobile购物车-空购物车的样式 预售"""
        # 获取用户的preorder

        QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_zhuli_header)

        assert self.response["result"] is True
        porder = self.response["object"]
        deal_date = porder["delivery_pickup_date"]
        zipcode = porder["zipcode"]

        # 1. 清空购物车商品
        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=ec_zhuli_header)

        # 84942是99991特定的presale商品"sale_event_id": 26（产品）
        # 97628是98011特定的presale商品，"sale_event_id": 88,（tb1）
        presale_res = UpdatePreOrderLine().add_to_cart_v3(
            headers=ec_zhuli_header,
            data=[
                {"product_id":84942,
                 "source":"dweb_product-product_detail-84942",
                 "refer_type":"normal",
                 "refer_value":"",
                 "delivery_date":deal_date,
                 "min_order_quantity":1,
                 "is_mkpl":False,
                 "biz_type":"normal",
                 "is_presale": True,
                 "quantity":1,
                 "sale_event_id":26,
                 "volume_price_support": False
                 }]
        )

        assert presale_res.get('object') and presale_res.get('object').get('updateItems'), f"加购presale商品失败，response is {presale_res}"
        assert presale_res.get('object').get('preSaleAddItemTagInfo'), f"加购presale商品失败，response is {presale_res}"
        CommonCheck.list_check(['product_img_url', 'added_content', 'shipping_content', 'button_content'], presale_res.get('object').get('preSaleAddItemTagInfo').keys()), f"加购presale商品失败，response is {presale_res}"

        update_item = presale_res.get('object').get('updateItems')[0]
        CommonCheck.list_check(["product_id", "quantity", "is_presale", "refer_type"], update_item.keys())
        assert update_item.get("product_id") == 84942, f"加购presale商品失败，response is {presale_res}"
        assert update_item.get("refer_type") == 'presale_normal', f"加购presale商品失败，response is {presale_res}"
