# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_item.py
@Description    :
@CreateTime     :  2023/5/16 16:53
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 16:53
"""
import copy
import json

import pytest
import weeeTest
from datetime import datetime, timedelta
from test_dir.api.ec.ec_customer.account_rest.account_rest import AccountRest
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction
from test_dir.api_case.ec.common.get_root_dir import get_project_dir


class TestBffAccountPage(weeeTest.TestCase):

    @pytest.fixture(scope='class', autouse=True)
    def setup(self, ec_login_header, ec_anony_header):
        global specail_h5_header, specail_pc_header,specail_h5_header_anony,specail_pc_header_anony
        with open(get_project_dir() + "/test_data/autotest_token.json", "r", encoding='utf-8') as f:
            specail_h5_header = json.load(f)
            specail_pc_header = copy.deepcopy(specail_h5_header)
        specail_h5_header_anony = copy.deepcopy(ec_anony_header)
        specail_pc_header_anony = copy.deepcopy(ec_anony_header)
        specail_h5_header[
            'user-agent'] = 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1'
        specail_pc_header[
            'user-agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'

        specail_h5_header_anony[
            'user-agent'] = 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1'
        specail_pc_header_anony[
            'user-agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'

        specail_pc_header['platform'] = 'pc'
        specail_pc_header_anony['platform'] = 'pc'

    @weeeTest.mark.list('bff_account', 'Regression', 'Smoke',  'Transaction')
    def test_bff_account_page_h5_anony(self):
        """ H5-匿名用户Account页面点击验证流程 """
        account_h5_anony = AccountRest().me_page_h5_bff_account_portal(headers=specail_h5_header_anony)
        assert account_h5_anony["object"] is not None
        self.section_portal_assert(account=account_h5_anony["object"], status_type="h5_anony", headers=specail_h5_header_anony)

    @weeeTest.mark.list('bff_account', 'Regression', 'Smoke',  'Transaction')
    def test_bff_account_page_h5(self):
        """ H5-登录用户Account页面点击验证流程 """

        account_h5 = AccountRest().me_page_h5_bff_account_portal(headers=specail_h5_header)

        assert account_h5["object"] is not None, f'account_h5["object"]为{account_h5["object"]}'
        self.section_portal_assert(account=account_h5["object"], status_type="h5_login", headers=specail_h5_header)

    def test_bff_account_page_pc_anony(self):
        """ PC-匿名用户Account页面点击验证流程 """
        account_pc_anony = AccountRest().me_page_pc_bff_account_portal(headers=specail_pc_header_anony)

        assert account_pc_anony["object"] is not None
        self.section_portal_assert(account=account_pc_anony["object"], status_type="pc_anony", headers=specail_pc_header_anony)

    @weeeTest.mark.list('bff_account', 'Regression', 'Smoke',  'Transaction')
    def test_bff_account_page_pc(self):
        """ PC-登录用户Account页面点击验证流程 """
        account_pc = AccountRest().me_page_pc_bff_account_portal(headers=specail_pc_header)

        assert account_pc["object"] is not None
        self.section_portal_assert(account=account_pc["object"], status_type="pc_login", headers=specail_pc_header)

    def get_rewards_level(self, headers):
        rewards_level = AccountRest().get_account_rewards_level(headers=headers)
        assert rewards_level["object"] is not None
        return rewards_level["object"]

    def section_portal_assert(self, account, status_type, headers):
        message_url = account["message_url"]
        assert "/account/notification" in message_url, f"这个链接返回的不是/account/notification，请确认~,{account}"
        # 点击跳转
        CommCheckFunction().comm_check_link(message_url, headers=headers)
        print(account)
        print(account["sections"])
        # 需要断言存在的section_name值
        mobile_required_section_names = ["my_profile", "my_today_orders", "my_orders",
                                         "my_lists", "my_referral", "my_weeebates",
                                         "my_points", "my_coupons", "my_giftcard", "my_community_profile",
                                         "my_posts", "common_group", "language", "user_info"]
        pc_required_section_names = ["my_profile", "my_orders", "saved_items","buy_again",
                                     "my_points", "my_rewards", "my_weeebates", "my_referral",
                                     "my_coupons", "my_giftcard","my_posts", "settings", "help"]

        # 获取sections列表中所有字典的section_name值
        section_names = [section["section_name"] for section in account["sections"]]
        if status_type in ("pc_login", ):
            # 断言所有必须的section_name都在section_names列表中
            assert all(required_name in section_names for required_name in
                       pc_required_section_names), "Not all required section_names are present"
        elif status_type in ("h5_login", ):
            # 断言所有必须的section_name都在section_names列表中
            assert all(required_name in section_names for required_name in
                       mobile_required_section_names), "Not all required section_names are present"

        for section in account["sections"]:
            # print(section)
            # if status_type in ("pc_login", "pc_anony"):
            #     section = section
            # else:
            #     section = section[0]
            link_url = section["link_url"]
            # print(link_url)

            assert ["section_title"] is not None, f'用户account 信息返回异常{account}'
            if section["section_type"] == "accounts" and section['section_name'] == 'my_profile':
                data = section["data"]
                if status_type in ("h5_anny", "pc_anony"):
                    # 断言这个链接
                    assert "/account/login" in link_url, f"这个链接返回的不是/account/login，请确认~, 链接为：{link_url}"

                    assert data["alias"] is None, f'用户account 信息返回异常{account}'
                    assert data["button_title"] is not None, f'用户account 信息返回异常{account}'
                    assert data["banner_item"]["item_image"] is not None, f'用户account 信息返回异常{account}'
                    assert data["banner_item"][
                               "item_key"] == "new_user_banner_no_login", f'用户account 信息返回异常{account}'
                elif status_type in ("h5_login", "pc_login"):
                    # 断言这个链接
                    assert "/account/rewards/level" in link_url, f"这个链接返回的不是/account/rewards/level，请确认~，链接为：{link_url}"
                    # 点击跳转
                    CommCheckFunction().comm_check_link(link_url, headers=headers)

                    assert data["alias"] is not None, f'用户account 信息返回异常{account}'
                    assert data["button_title"] is not None, f'用户account 信息返回异常{account}'
                    assert data["is_login"] is True, f'用户account 信息返回异常{account}'
                    rewards_level = self.get_rewards_level(headers=headers)
                    expire_time = rewards_level["end_time_day"]
                    loyalty_info = data["loyalty_info"]
                    account_loyalty_titles = loyalty_info["account_loyalty_titles"]
                    account_progress_items = loyalty_info["account_progress_items"]
                    account_title_tip = loyalty_info["account_title_tip"]
                    loyalty_points_buy_items = loyalty_info["loyalty_points_buy_items"]
                    loyalty_reward_items = loyalty_info["loyalty_reward_items"]
                    loyalty_reward_title_items = loyalty_info["loyalty_reward_title_items"]
                    current_level = loyalty_info["current_level"]
                    print(current_level)
                    print(type(current_level))
                    # expire_time = loyalty_info["expire_time"]
                    # 将字符串转换为datetime对象
                    # 获取当前时间
                    # 计算两个日期之间的差值

                    assert loyalty_info[
                               "account_loyalty_reward_title_color"] is not None, f'用户account 信息返回异常{account}'
                    assert loyalty_info["loyalty_icon"] is not None, f'用户account 信息返回异常{account}'
                    assert loyalty_info["current_level"] is not None, f'用户account 信息返回异常{account}'

                    for account_loyalty_title in account_loyalty_titles:
                        assert account_loyalty_title["title"] is not None, f'用户account 信息返回异常{account}'
                        assert account_loyalty_title["color"] is not None, f'用户account 信息返回异常{account}'
                    for account_progress_item in account_progress_items:
                        assert account_progress_item["title"] is not None, f'用户account 信息返回异常{account}'
                        assert account_progress_item["color"] is not None, f'用户account 信息返回异常{account}'

                    assert account_title_tip["bg_color"] is not None, f'用户account 信息返回异常{account}'
                    assert account_title_tip["color"] is not None, f'用户account 信息返回异常{account}'
                    assert account_title_tip["title"] is not None, f'用户account 信息返回异常{account}'
                    # 过期时间减去当前时间
                    # 解析完整的日期时间字符串
                    expire_time_old = expire_time.split("/")
                    date_part = datetime.strptime(str(datetime.today()).split(" ")[0], "%Y-%m-%d")
                    # 过期时间-当前时间
                    difference = datetime.strptime(f"{expire_time_old[0]}", "%Y-%m-%d %H:%M:%S") - date_part
                    print(difference)

                    # 判断差值是否小于10天
                    if current_level == 3 and difference < timedelta(days=10):
                        # 如果是金用户，小于10天显示banner
                        assert len(loyalty_points_buy_items) > 0, f'用户account 信息返回异常{account}'
                    if current_level == 3 and difference >= timedelta(days=10):
                        # 如果是金用户，大于10天不显示banner
                        assert len(loyalty_points_buy_items) == 0, f'用户account 信息返回异常{account}'
                    elif current_level in (1, 2):
                        # 也会显示banner
                        assert len(loyalty_points_buy_items) > 0, f'用户account 信息返回异常{account}'
                    for loyalty_points_buy_item in loyalty_points_buy_items:
                        assert loyalty_points_buy_item[
                                   "background_color"] is not None, f'用户account 信息返回异常{account}'
                        assert loyalty_points_buy_item["buy_label"] is not None, f'用户account 信息返回异常{account}'
                        assert loyalty_points_buy_item["buy_url"] is not None, f'用户account 信息返回异常{account}'
                        assert "/account/rewards/level" in loyalty_points_buy_item[
                            "buy_url"], f'用户account 信息返回异常{account}'
                        # 点击跳转
                        CommCheckFunction().comm_check_link(loyalty_points_buy_item["buy_url"], headers=headers)
                        assert loyalty_points_buy_item["icon_url"] is not None, f'用户account 信息返回异常{account}'
                        assert loyalty_points_buy_item["item_key"] == "point_sale", f'用户account 信息返回异常{account}'
                        assert loyalty_points_buy_item["sub_title"] is not None, f'用户account 信息返回异常{account}'
                        assert loyalty_points_buy_item["title"] is not None, f'用户account 信息返回异常{account}'
                        assert loyalty_points_buy_item["title_second"] is not None, f'用户account 信息返回异常{account}'

                    for loyalty_reward_item in loyalty_reward_items:
                        assert loyalty_reward_item["color"] is not None, f'用户account 信息返回异常{account}'
                        assert loyalty_reward_item["level"] is not None, f'用户account 信息返回异常{account}'
                        assert loyalty_reward_item["level_title"] is not None, f'用户account 信息返回异常{account}'
                        assert loyalty_reward_item["level_value"] is not None, f'用户account 信息返回异常{account}'
                        # 金银铜等级积分
                        if loyalty_reward_item["level"] == 1:
                            assert loyalty_reward_item["level_value"] == 0, f'用户account 信息返回异常{account}'
                        elif loyalty_reward_item["level"] == 2:
                            assert loyalty_reward_item["level_value"] == 150, f'用户account 信息返回异常{account}'
                        elif loyalty_reward_item["level"] == 3:
                            assert loyalty_reward_item["level_value"] == 350, f'用户account 信息返回异常{account}'
                    loyalty_reward_items2 = loyalty_reward_title_items["loyalty_reward_items"]

                    for loyalty_reward_item2 in loyalty_reward_items2:
                        assert loyalty_reward_item2["reward_key"] is not None, f'用户account 信息返回异常{account}'
                        assert loyalty_reward_item2[
                                   "reward_sub_title"] is not None, f'用户account 信息返回异常{account}'
                        assert loyalty_reward_item2["reward_title"] is not None, f'用户account 信息返回异常{account}'
                        assert loyalty_reward_item2["reward_url"] is not None, f'用户account 信息返回异常{account}'
                        assert "/account/rewards/level" in loyalty_reward_item2[
                            "reward_url"], f'用户account 信息返回异常{account}'
                        # 点击跳转
                        CommCheckFunction().comm_check_link(loyalty_reward_item2["reward_url"], headers=headers)

                    loyalty_reward_total_savings = loyalty_reward_title_items["loyalty_reward_total_savings"]
                    for loyalty_reward_total_saving in loyalty_reward_total_savings:
                        assert loyalty_reward_total_saving[
                                   "reward_key"] is not None, f'用户account 信息返回异常{account}'
                        assert loyalty_reward_total_saving[
                                   "reward_sub_title"] is not None, f'用户account 信息返回异常{account}'
                        assert loyalty_reward_total_saving[
                                   "reward_title"] is not None, f'用户account 信息返回异常{account}'
            elif section['section_type'] == 'icon_common' and section['section_name'] == 'my_profile':
                items = section['data']['items']
                for item in items:
                    link_url = item['link_url']
                    if item['section_name'] == 'help':
                        # 断言这个链接
                        assert "/account/help" in link_url, f"这个链接返回的不是/account/help，请确认~, 链接为：{link_url}"
                        # 点击跳转
                        CommCheckFunction().comm_check_link(link_url, headers=headers)
                    elif item['section_name'] == 'about':
                        # 断言这个链接
                        assert "/about/us" in link_url, f"这个链接返回的不是/about/us，请确认~, 链接为：{link_url}"
                        # 点击跳转
                        CommCheckFunction().comm_check_link(link_url, headers=headers)
                    elif item['section_name'] == 'language':
                        # 断言这个链接
                        assert "/account/setting-language" in link_url, f"这个链接返回的不是/setting-languages，请确认~, 链接为：{link_url}"
                        # 点击跳转
                        CommCheckFunction().comm_check_link(link_url, headers=headers)
            elif section["section_type"] == "today_orders" and section['section_name'] == 'my_today_orders':
                # 断言这个链接
                assert "/order/today-order" in link_url, f"这个链接返回的不是/order/today-order，请确认~, 链接为：{link_url}"
            elif section["section_type"] == "order" and section['section_name'] == 'my_orders':
                if status_type == "h5_login":
                    for item in section["data"]["items"]:
                        if item["item_type"] == "pending":
                            assert "/order/list?filter_status=1" in item[
                                "link_url"], f'用户account 信息返回异常{account}'
                        elif item["item_type"] == "unshipped":
                            assert "/order/list?filter_status=2" in item[
                                "link_url"], f'用户account 信息返回异常{account}'
                        elif item["item_type"] == "shipped":
                            assert "/order/list?filter_status=3" in item[
                                "link_url"], f'用户account 信息返回异常{account}'

                        elif item["item_type"] == "to_review":
                            assert "/social/post-review/review-product" in item[
                                "link_url"], f'用户account 信息返回异常{account}'

                        elif item["item_type"] == "returns":
                            assert "/order/case/list" in item["link_url"], f'用户account 信息返回异常{account}'
                        # 点击跳转
                        CommCheckFunction().comm_check_link(item["link_url"], headers=headers)

                    # 断言这个链接
                    assert "/order/list" in link_url, f"这个链接返回的不是/order/list，请确认~, 链接为：{link_url}"
                    # # 点击跳转
                    CommCheckFunction().comm_check_link(link_url, headers=headers)
                elif status_type == "pc_login":
                    assert "/account/my_orders" in link_url, f'用户account 信息返回异常{account}'
                    # # 点击跳转
                    CommCheckFunction().comm_check_link(link_url, headers=headers)
            elif section["section_type"] == "order" and section['section_name'] == 'saved_items':
                # 断言这个链接
                assert link_url is not None, f'用户account 信息返回异常{account}'
                assert "/account/saved_items" in link_url, f'用户account 信息返回异常{account}'
                # # 点击跳转
                CommCheckFunction().comm_check_link(link_url, headers=headers)
            elif section["section_type"] == "order" and section['section_name'] == 'buy_again':
                # 断言这个链接
                assert link_url is not None, f'用户account 信息返回异常{account}'
                assert "/account/buy_again" in link_url, f'用户account 信息返回异常{account}'
                # # 点击跳转
                CommCheckFunction().comm_check_link(link_url, headers=headers)
            elif section["section_type"] == "my_lists" and section['section_name'] == 'my_lists':
                for item in section["data"]["items"]:
                    if item["item_type"] == "saved_items":
                        assert "/account/my-list" in item["link_url"], f'用户account 信息返回异常{account}'
                    elif item["item_type"] == "buy_again":
                        assert "/account/my-list?type=bought" in item["link_url"], f'用户account 信息返回异常{account}'
                    # 点击跳转
                    CommCheckFunction().comm_check_link(item["link_url"], headers=headers)

                # 断言这个链接
                assert "/account/my-list" in link_url, f"这个链接返回的不是/account/my-list，请确认~, {account}"
                # # 点击跳转
                CommCheckFunction().comm_check_link(link_url, headers=headers)
            elif section["section_type"] == "icon_color" and section['section_name'] == 'my_freebies':
                # 断言这个链接
                assert link_url is not None, f'用户account 信息返回异常{account}'
                assert "/freebie/landing" in link_url, f'用户account 信息返回异常{account}'
                # # 点击跳转
                CommCheckFunction().comm_check_link(link_url, headers=headers)
            elif section["section_type"] == "banners" and section['section_name'] == 'my_banners':
                # 断言这个链接
                items = section['data']['carousel']
                for item in items:
                    assert item["img"] is not None, f'用户account 信息返回异常{account}'
                    assert item["key"] == "me_page", f'用户account 信息返回异常{account}'
                    link_url = item["link_url"]
                    # # 点击跳转
                    CommCheckFunction().comm_check_link(link_url, headers=headers)
            elif section["section_type"] == "icon_color" and section['section_name'] == 'my_rewards':
                # pc 才有
                # 断言这个链接
                assert "/account/my_rewards" in link_url, f"这个链接返回的不是/account/my_rewards，请确认~, {account}"
                # # 点击跳转
                CommCheckFunction().comm_check_link(link_url, headers=headers)
            elif section["section_type"] == "icon_color" and section['section_name'] == 'my_referral':
                # 断言这个链接
                assert "/account/referral" in link_url, f"这个链接返回的不是/account/referral，请确认~, {account}"
                # # 点击跳转
                CommCheckFunction().comm_check_link(link_url, headers=headers)
            elif section["section_type"] == "icon_color" and section['section_name'] == 'affiliate_dash':
                # 断言这个链接
                assert "/account/affiliate" in link_url, f"这个链接返回的不是/account/affiliate，请确认~, {account}"
                # # 点击跳转
                CommCheckFunction().comm_check_link(link_url, headers=headers)
            elif section["section_type"] == "icon_color" and section['section_name'] == 'affiliate_list':
                # 断言这个链接
                assert "/account/affiliate-list" in link_url, f"/account/affiliate-list，请确认~, {account}"
                # # 点击跳转
                CommCheckFunction().comm_check_link(link_url, headers=headers)
            elif section["section_type"] == "icon_color" and section['section_name'] == 'my_live_stream':
                # 断言这个链接
                assert "/social/livestream" in link_url, f"/social/livestream，请确认~, {account}"
                # # 点击跳转
                CommCheckFunction().comm_check_link(link_url, headers=headers)
            elif section["section_type"] == "share_order" and section['section_name'] == 'my_weeebates':
                # 断言这个链接
                assert "/order/share/list" in link_url, f"这个链接返回的不是/order/share/list，请确认~, {account}"
                # # 点击跳转
                CommCheckFunction().comm_check_link(link_url, headers=headers)
            elif section["section_type"] == "icon_color" and section['section_name'] == 'my_points':
                # 断言这个链接
                assert "/account/perks" in link_url, f"这个链接返回的不是/account/perks，请确认~,  {account}"
                # # 点击跳转
                CommCheckFunction().comm_check_link(link_url, headers=headers)
            elif section["section_type"] == "icon_color" and section['section_name'] == 'my_coupons':
                # 断言这个链接
                assert "/account/my-coupons" in link_url, f"这个链接返回的不是/account/my-coupons，请确认~, {account}"
                # # 点击跳转
                CommCheckFunction().comm_check_link(link_url, headers=headers)
            elif section["section_type"] == "icon_color" and section['section_name'] == 'my_giftcard':
                # 断言这个链接
                assert "/product/gift-card/" in link_url, f"这个链接返回的不是/account/my-coupons，请确认~, {account}"
                # # 点击跳转
                CommCheckFunction().comm_check_link(link_url, headers=headers)
            elif section["section_type"] == "icon_color" and section['section_name'] == "my_community_profile":
                # 断言这个链接
                assert "/social/user/" in link_url, f"{account}"
                # # 点击跳转
                CommCheckFunction().comm_check_link(link_url, headers=headers)
            elif section["section_type"] == "icon_color" and section['section_name'] == "my_posts":
                # 断言这个链接
                assert "/social/post-review/review-product" in link_url, f"这个链接返回的不是/social/post-review/review-product，请确认~, 链接为：{link_url}"
                # # 点击跳转
                CommCheckFunction().comm_check_link(link_url, headers=headers)
            elif section["section_type"] == "icon_color" and section['section_name'] == "my_vip":
                # 断言这个链接
                assert "/account/perks" in link_url, f"这个链接返回的不是/account/perks，请确认~, 链接为：{link_url}"
                # # 点击跳转
                CommCheckFunction().comm_check_link(link_url, headers=headers)
            elif section['section_type'] == "icon_common" and section['section_name'] == "common_group":
                items = section['data']['items']
                for item in items:
                    link_url = item["link_url"]
                    if item['section_name'] == "help":
                        # 断言这个链接
                        assert "/account/help" in link_url, f"这个链接返回的不是/account/help，请确认~, 链接为：{link_url}"
                        # 点击跳转
                        CommCheckFunction().comm_check_link(link_url, headers=headers)
                    elif item['section_name'] == 'about':
                        # 断言这个链接
                        assert "/about/us" in link_url, f"这个链接返回的不是/about/us，请确认~, 链接为：{link_url}"
                        # 点击跳转
                        CommCheckFunction().comm_check_link(link_url, headers=headers)
                    elif item['section_name'] == 'settings':
                        # 断言这个链接
                        assert "/account/settings" in link_url, f"这个链接返回的不是/account/settings，请确认~, 链接为：{link_url}"
                        # 点击跳转
                        CommCheckFunction().comm_check_link(link_url, headers=headers)
            elif section["section_type"] == "icon_simple" and section['section_name'] == 'language':
                # H5 才有
                # 断言这个链接
                assert "/account/setting-language" in link_url, f"这个链接返回的不是/account/setting-language，请确认~, 链接为：{link_url}"
                # # 点击跳转
                CommCheckFunction().comm_check_link(link_url, headers=headers)
            elif section["section_type"] == "icon_color" and section['section_name'] == 'settings':
                # PC 才有
                # 断言这个链接
                assert "/account/settings" in link_url, f"这个链接返回的不是/account/settings，请确认~, 链接为：{link_url}"
                # # 点击跳转
                CommCheckFunction().comm_check_link(link_url, headers=headers)
            elif section["section_type"] == "icon_color" and section['section_name'] == 'help':
                # PC 才有
                # 断言这个链接
                assert "/account/help" in link_url, f"这个链接返回的不是/account/help，请确认~, 链接为：{link_url}"
                # # 点击跳转
                CommCheckFunction().comm_check_link(link_url, headers=headers)
            elif section["section_type"] == "icon_simple_only" and section['section_name'] == 'user_info':
                # 断言这个链接
                assert section['section_title'] is not None, f"这个返回的section_title 为空，请确认~"
            elif section["section_type"] == "button_simple" and section['section_name'] == 'logout':
                assert section['section_title'] is not None, f'用户account 信息返回异常{account}'

                # 断言这个链接
                # assert "/login/log_out" in link_url, f"这个链接返回的不是/login/log_out，请确认~, 链接为：{link_url}"
                # 点击跳转
                # CommCheckFunction().comm_check_link(link_url)
            if section['section_name'] in ("my_profile", "my_lists", "common_group", "logout"):
                assert section['icon_url'] is None, f'用户account 信息返回异常{account}'
            elif section['section_name'] in ("user_info"):
                assert section['link_url'] is None, f'用户account 信息返回异常{account}'
            else:
                # 当section_type为banner时，section_title就为None
                # assert section['section_title'] is not None, f'用户account 信息返回异常{account}'
                assert section['section_type'] is not None, f'用户account 信息返回异常{account}'
                # assert section['icon_url'] is not None, f'用户account 信息返回异常{account}'
                # assert section['link_url'] is not None, f'用户account 信息返回异常{account}'
            # if link_url is not None:
            #     # 点击跳转
            #     CommCheckFunction().comm_check_link(link_url)
