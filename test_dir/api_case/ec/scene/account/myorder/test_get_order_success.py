# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON> <PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_item.py
@Description    :
@CreateTime     :  2023/5/16 16:53
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 16:53
"""
import pytest
import weeeTest

from test_dir.api.ec.ec_item.recommend.api_bought import GetBoughtProducts
from test_dir.api.ec.ec_so.order_query.order_success_page_information import OrderSuccessPageInformation
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api_case.ec.common.common_check import CommonCheck


class TestGetOrderSuccess(weeeTest.TestCase):
    @weeeTest.mark.list('test_check_help_center_page', 'Regression', 'Transaction','product')
    def test_get_order_success_purchased_product(self, ec_login_header):
        """ 我的订单-订单成功页面推荐数据验证流程 """
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 订单成功页面推荐数据
        bought_products = GetBoughtProducts().get_bought_products_orderlist(headers=ec_login_header)
        assert bought_products["object"][
                   "total_count"] > 0, f"订单成功页没有商品，商品列表为：{bought_products['object']}"
        purchased_product = OrderSuccessPageInformation().order_query_purchased_product(
            headers=ec_login_header,
            delivery_date=porder["delivery_pickup_date"],
            zipcode=porder["zipcode"])
        assert purchased_product["result"] is True
        products = bought_products["object"]["products"]
        # 检查商品基础信息
        for index, product in enumerate(products):
            CommonCheck().check_product_info(product=product, category_type="others", source="order_confirmation",
                                             filters="others", headers=ec_login_header)

        # assert len(purchased_product[
        #                'object']) > 0, f"订单成功页没有商品，商品列表为：{purchased_product['object']}"

    @pytest.mark.parametrize("checkout_id, order_type", [("49813137", "R-alcohol-0"), ("48554523", "R-normal-0"), ("47153548", "S-normal-0")])
    @weeeTest.mark.list('Regression', 'Smoke', 'Transaction','product')
    def test_get_order_success_page(self, checkout_id, order_type, ec_login_header):
        # 获取订单的商品id

        success_page = \
            OrderSuccessPageInformation().order_success_page_information_v2(headers=ec_login_header,
                                                                            checkout_id=checkout_id)
        assert success_page["result"] is True, f'订单成功页面数据异常{success_page}'
        self.check_order_success_page(success_page=success_page["object"], order_type=order_type)

    def check_order_success_page(self, success_page, order_type):
        assert success_page["delivery_info"] is not None, f'订单成功页面数据异常{success_page}'
        assert len(success_page["order_infos"]) > 0, f'订单成功页面数据异常{success_page}'
        assert success_page["share_order"] is not None, f'订单成功页面数据异常{success_page}'
        assert success_page["title"] is not None, f'订单成功页面数据异常{success_page}'
        if order_type == "R-alcohol-0":
            assert success_page["alcohol_desc"] is not None, f'订单成功页面数据异常{success_page}'
            assert success_page["delivery_info"]["delivery_time"] is not None, f'订单成功页面数据异常{success_page}'
            assert success_page["delivery_info"]["delivery_date"] is not None, f'订单成功页面数据异常{success_page}'
            assert success_page["delivery_info"]["delivery_address"] is not None, f'订单成功页面数据异常{success_page}'
            assert success_page["delivery_info"]["delivery_window"] is None, f'订单成功页面数据异常{success_page}'
            assert success_page["delivery_info"][
                       "shipping_shipment_date"] is not None, f'订单成功页面数据异常{success_page}'
            assert success_page["delivery_info"][
                       "delivery_date_content"] is None, f'订单成功页面数据异常{success_page}'
            assert success_page["delivery_info"][
                       "delivery_address_content"] is None, f'订单成功页面数据异常{success_page}'
        elif order_type == "S-normal-0":
            assert success_page["delivery_info"]["delivery_time"] is None, f'订单成功页面数据异常{success_page}'
            assert success_page["delivery_info"]["delivery_date"] is None, f'订单成功页面数据异常{success_page}'
            assert success_page["delivery_info"]["delivery_window"] is None, f'订单成功页面数据异常{success_page}'
            assert success_page["delivery_info"]["delivery_date_content"] is None, f'订单成功页面数据异常{success_page}'
            assert success_page["delivery_info"][
                       "delivery_address_content"] is None, f'订单成功页面数据异常{success_page}'
        elif order_type == "R-normal-0":
            assert success_page["delivery_info"]["delivery_time"] is not None, f'订单成功页面数据异常{success_page}'
            assert success_page["delivery_info"]["delivery_date"] is not None, f'订单成功页面数据异常{success_page}'
            assert success_page["delivery_info"]["delivery_address"] is not None, f'订单成功页面数据异常{success_page}'
            assert success_page["delivery_info"]["delivery_window"] is not None, f'订单成功页面数据异常{success_page}'
            assert success_page["delivery_info"][
                       "shipping_shipment_date"] is not None, f'订单成功页面数据异常{success_page}'
            assert success_page["delivery_info"][
                       "delivery_date_content"] is not None, f'订单成功页面数据异常{success_page}'
            assert success_page["delivery_info"][
                       "delivery_address_content"] is not None, f'订单成功页面数据异常{success_page}'
