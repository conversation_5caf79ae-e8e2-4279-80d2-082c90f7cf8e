# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_item.py
@Description    :
@CreateTime     :  2023/5/16 16:53
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 16:53
"""
import pytest
import weeeTest

from test_dir.api.ec.ec_so.order_query.list_my_order import ListMyOrder
from test_dir.api_case.ec.common.common_check import CommonCheck


class TestListMyOrder(weeeTest.TestCase):
    @classmethod
    def setup_class(cls):
        cls.order_list = ListMyOrder()

    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.list('B2B','Regression', 'Transaction')
    def test_list_my_order_v1(self, *args, ec_login_header):
        """ 我的订单-PC我的订单_v1验证流程 """
        # ["all","1","2","3","6","4"]
        # 这个接口不太稳定，有几次调试在item=4的时候失败，现在PC端用的也是v2版本
        filter_status = args[0]["listmyorder"]["filter_status"]
        for item in filter_status:
            self.order_list.list_my_order_v1(headers=ec_login_header, filter_status=item)
            # 断言
            assert self.response["result"] is True, f"item={item}的订单查询失败"
            assert isinstance(self.response["object"]["total"],
                              int), f"isinstance(self.response[’object‘]是{self.response['object']}"
            assert isinstance(self.response['object']['myOrders'],
                              list), f"isinstance(self.response[’object‘]是{self.response['object']['myOrders']}"

    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.list('B2B','Regression', 'Transaction')
    def test_list_my_order_v1_query(self, *args, ec_login_header):
        """ 我的订单- PC我的订单查询_v1验证流程 """

        # ["all","1","2","3","6","4"]
        filter_status = args[0]["listmyorder"]["filter_status"]
        keyword = args[0]["listmyorder"]["keyword"]
        for item in filter_status:
            self.order_list.list_my_order_v1(headers=ec_login_header, filter_status=item, keyword=keyword)
            # 断言
            assert self.response["result"] is True, f"item={item}的订单查询失败"
            assert CommonCheck.list_check(['total', 'user'], self.response[
                'object'].keys()), f"self.response['object']是：{self.response['object']}"
            assert isinstance(self.response["object"]["myOrders"], list)

    @weeeTest.mark.list('test_list_my_order_list', 'Regression', 'Smoke', 'Transaction')
    @pytest.mark.parametrize("B2B", ["all", "1", "2", "3", "6", "4"])
    def test_list_my_order_list(self, filter_status, ec_login_header):
        """ 我的订单-H5我的订单_全部订单验证流程 """
        # ["all","1","2","3","6","4"]
        # for filter_statu in filter_status:
        listall = ListMyOrder().list_my_order_v2(headers=ec_login_header, filter_status=filter_status)
        assert listall["result"] is True, f'我的订单数据异常{listall}'

        if filter_status in ("all", "4"):
            # 这个账号全部订单、已发货订单、取消订单是有数据的
            assert listall["object"]["total"] > 0, f'我的订单数据异常{listall}'
            for index, item in enumerate(listall["object"]["myOrders"]):
                CommonCheck().check_my_orders_assertion(myorder=item, filter_status=filter_status)
                if index == 5:
                    break
        else:
            if listall["object"]["total"] > 0:
                # 如果有待支付，待发货订单
                for index, item in enumerate(listall["object"]["myOrders"]):
                    CommonCheck().check_my_orders_assertion(myorder=item, filter_status=filter_status)
                    if index == 5:
                        break

    @weeeTest.mark.list('B2B','Regression', 'Transaction')
    def test_list_my_order_v2_query(self, ec_login_header):
        """ 我的订单-H5我的订单查询_搜索验证流程  """


        listall = ListMyOrder().list_my_order_v2(headers=ec_login_header, filter_status="all",
                                                 keyword="fish")
        assert listall["result"] is True, f'我的订单数据异常{listall}'
        # 这个账号全部订单、已发货订单、取消订单是有数据的
        assert listall["object"]["total"] > 0, f'我的订单数据异常{listall}'

    @weeeTest.mark.list("not_regression")
    @pytest.mark.parametrize("B2B", ["all", "1", "2", "3", "6", "4"])
    def test_my_orders_for_each_filter_status(self, filter_status, ec_login_header):
        """
        caseId: 111592, 111629 订单列表校验
        """
        list_my_orders = ListMyOrder().list_my_order_v2(headers=ec_login_header,
                                                        filter_status=filter_status, page_size=200)
        match filter_status:
            case "all":
                # 全部订单
                assert list_my_orders.get('object').get('total') > 0, f"resp={list_my_orders}"
                assert all(["status" in item.keys() for item in list_my_orders.get('object').get('myOrders')])
                # 不显示已取消(Cancelled)的订单 111592用例校验
                assert all([item.get("status") != "X" for item in list_my_orders.get('object').get('myOrders')])
            case "1":
                # pending autotest账户没有pending数据
                if list_my_orders.get('object').get('myOrders'):
                    assert all([item.get("status") == "C" for item in list_my_orders.get('object').get('myOrders')])
            case "2":
                # 待发货（unshipped) autotest账户没有pending数据
                if list_my_orders.get('object').get('myOrders'):
                    assert all([item.get("status") == "A" for item in list_my_orders.get('object').get('myOrders')])
            case "3":
                # shipped
                if list_my_orders.get('object').get('myOrders'):
                    assert all([item.get("status") == "F" for item in list_my_orders.get('object').get('myOrders')])
                    assert all(
                        [item.get("shipping_status") == 5 for item in list_my_orders.get('object').get('myOrders') if
                         item.get("status_label") == "Delivered"])
                    assert all(
                        [item.get("shipping_status") == 6 for item in list_my_orders.get('object').get('myOrders') if
                         item.get("status_label") == "Not delivered"])
            case "6":
                # review, autotest账户无数据
                if list_my_orders.get('object').get('myOrders'):
                    pass
            case "4":
                # cancelled
                if list_my_orders.get('object').get('myOrders'):
                    assert all([item.get("status") == "X" or item.get('oos_status') == "Y" for item in
                                list_my_orders.get('object').get('myOrders')])
                    # 这个断言有1个label==Canceled, 正在排查原因, gift_card的label就是Canceled
                    assert all([item.get("status_label") in ["Cancelled", "Canceled"] for item in
                                list_my_orders.get('object').get('myOrders')])


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
