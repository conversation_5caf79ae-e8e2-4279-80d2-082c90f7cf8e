# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_item.py
@Description    :
@CreateTime     :  2023/5/16 16:53
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 16:53
"""
import pytest
import weeeTest
from weeeTest import log

from test_data.ec.simple.common import Header
from test_dir.api.ec.ec_so.order.cancel_order import CancelOrder
from test_dir.api.ec.ec_so.order_query.list_my_order import ListMyOrder
from test_dir.api_case.ec.common.common_check import CommonCheck


class TestCancelOrder(weeeTest.TestCase):
    @weeeTest.mark.list('Regression', 'Transaction', 'product')
    def test_cancel_order(self, ec_login_header):
        """ 我的订单-取消订单验证流程 """
        # 没有可取消的订单
        # 获取全部订单，拿到订单id # ["all","1","2","3","6","4"]
        listall = ListMyOrder().list_my_order_v2(headers=ec_login_header, filter_status="4")
        assert listall["object"]["total"] > 0
        for index, item in enumerate(listall["object"]["myOrders"]):
            CommonCheck().check_my_orders_assertion(myorder=item, filter_status="4")
            if index == 5:
                break

        # order_id = listall["object"]["myOrders"][0]["id"]
        #
        # CancelOrder().cancel_order(RequestHeader.ec_login_header, order_id)
        # # 断言
        # assert self.response["result"] is True

    @weeeTest.mark.list('Regression', 'Transaction', 'product')
    def test_cancel_specific_order(self, ec_login_header):
        list_all_order = ListMyOrder().list_my_order_v2(headers=ec_login_header, filter_status="2")
        if list_all_order.get('object').get('total'):
            order_ids = [item['id'] for item in list_all_order["object"]["myOrders"] if item.get('status') == 'P']
            for order_id in order_ids:
                CancelOrder().cancel_order(ec_login_header, str(order_id))
        else:
            log.info("没有已支付(unshipped)的订单，请检查")

    # @weeeTest.mark.list('Regression', 'Transaction', 'product')
    # def test_cancel_specific_order(self):
    #     z_header = Header.login_header(email='<EMAIL>', password='1qaz2wsx')
    #     list_all_order = ListMyOrder().list_my_order_v2(headers=z_header, filter_status="2")
    #     if list_all_order.get('object').get('total'):
    #         order_ids = [item['id'] for item in list_all_order["object"]["myOrders"] if item.get('status') == 'P']
    #         for order_id in order_ids:
    #             CancelOrder().cancel_order(z_header, str(order_id))
    #     else:
    #         log.info("没有已支付(unshipped)的订单，请检查")

    @weeeTest.mark.list('Regression-store', 'Transaction', 'product-store')
    @pytest.mark.parametrize("email,password", [
                                                ("<EMAIL>", "123456"),
                                                ("<EMAIL>", "Jtm123456"),
                                                ("<EMAIL>", "Jtm123456"),
                                                ("<EMAIL>", "123"),
                                                ("<EMAIL>", "A1234567")
                                                ])
    def test_cancel_all_order(self, email, password):
        z_header = Header.login_header(email=email, password=password)
        list_all_order = ListMyOrder().list_my_order_v2(headers=z_header, filter_status="2")
        if list_all_order.get('object').get('total'):
            order_ids = [item['id'] for item in list_all_order["object"]["myOrders"] if item.get('status') == 'P']
            for order_id in order_ids:
                CancelOrder().cancel_order(z_header, str(order_id))
        else:
            log.info("没有已支付(unshipped)的订单，请检查")

    @weeeTest.mark.list('Regression', 'Transaction', 'product')
    def test_cancel_unshipped_order(self):
        z_header = Header.login_header(email="<EMAIL>", password="123456")
        list_all_order = ListMyOrder().list_my_order_v2(headers=z_header, filter_status="2")
        if list_all_order.get('object').get('total'):
            order_ids = [item['id'] for item in list_all_order["object"]["myOrders"] if item.get('status') == 'P']
            for order_id in order_ids:
                CancelOrder().cancel_order(z_header, str(order_id))
        else:
            log.info("没有已支付(unshipped)的订单，请检查")


    @weeeTest.mark.list('Regression', 'Transaction', 'product')
    def test_cancel_unpaid_order(self, ec_login_header):
        """ # 我的订单-待支付订单取消订单流程验证 """
        # 获取待支付订单，拿到订单id
        listall = ListMyOrder().list_my_order_v1(headers=ec_login_header, filter_status="1")
        if listall["object"]["total"] > 0:
            order_id = listall["object"]["myOrders"][0]["id"]
            CancelOrder().cancel_unpaid_order(ec_login_header, str(order_id))
            # 断言
            assert self.response["result"] is True
            assert self.response["object"] == "success"
        else:
            log.info("没有待支付订单，请先下单")

    @weeeTest.mark.list('Regression', 'Transaction', 'product')
    def test_cancel_order_tip(self, ec_login_header):
        """ # 我的订单-取消小费流程验证 """
        # 获取全部订单，拿到订单id
        listall = ListMyOrder().list_my_order_v1(headers=ec_login_header, filter_status="1")
        if listall["object"]["total"] > 0:
            order_id = listall["object"]["myOrders"][0]["id"]
            CancelOrder().cancel_order_tip(ec_login_header, str(order_id))
            # 断言
            assert self.response["result"] is True
            assert self.response["object"] == "success"
        else:
            log.info("没有订单，请先下单")
