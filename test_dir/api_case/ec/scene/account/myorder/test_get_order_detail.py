# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_item.py
@Description    :
@CreateTime     :  2023/5/16 16:53
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 16:53
"""

import weeeTest

from test_dir.api.ec.ec_so.order_query.get_order_detail import GetOrderDetail
from test_dir.api_case.ec.common.common_check import CommonCheck


class TestGetOrderDetail(weeeTest.TestCase):
    @weeeTest.mark.list('test_check_help_center_page', 'Regression', 'Transaction','product')
    def test_get_alcohol_order_detail(self, ec_login_header):
        """ 我的订单-订单详情页面验证流程 """
        order_id = 49813137
        order_detail = GetOrderDetail().get_order_detail(headers=ec_login_header,
                                                         order_id=str(order_id))
        CommonCheck().check_order_detail(order_detail=order_detail['object'], order_id=order_id)

    @weeeTest.mark.list('test_check_help_center_page', 'Regression',  'Transaction','product')
    def test_get_seller_order_detail(self, ec_login_header):
        """ 我的订单-订单详情页面验证流程 """
        order_id = 47153548
        order_detail = GetOrderDetail().get_order_detail(headers=ec_login_header,
                                                         order_id=str(order_id))
        CommonCheck().check_order_detail(order_detail=order_detail['object'], order_id=order_id)

    @weeeTest.mark.list('B2B', 'Regression', 'Transaction','product')
    def test_get_normal_order_detail(self, ec_login_header):
        """ 我的订单-订单详情页面验证流程 """
        order_id = 48554571
        order_detail = GetOrderDetail().get_order_detail(headers=ec_login_header,
                                                         order_id=str(order_id))
        CommonCheck().check_order_detail(order_detail=order_detail['object'], order_id=order_id)

    @weeeTest.mark.list('test_check_help_center_page', 'Regression', 'Transaction','product')
    def test_get_points_order_detail(self, ec_login_header):
        """ 我的订单-订单详情页面验证流程 """
        order_id = 55213327
        order_detail = GetOrderDetail().get_order_detail(headers=ec_login_header,
                                                         order_id=str(order_id))
        CommonCheck().check_order_detail(order_detail=order_detail['object'], order_id=order_id)

    @weeeTest.mark.list('test_check_help_center_page', 'Regression', 'Transaction','product')
    def test_get_gift_order_detail(self, ec_login_header):
        """ 我的订单-订单详情页面验证流程 """
        order_id = 56164282
        order_detail = GetOrderDetail().get_order_detail(headers=ec_login_header,
                                                         order_id=str(order_id))
        CommonCheck().check_order_detail(order_detail=order_detail['object'], order_id=order_id)


