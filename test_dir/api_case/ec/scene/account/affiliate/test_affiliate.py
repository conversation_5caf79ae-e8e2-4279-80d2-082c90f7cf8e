# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON> <PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import json

import weeeTest
import random

from test_dir.api.ec.ec_growth.bar_info import BarInfo
from test_dir.api.ec.ec_so.preorder.create_preorder import CreatePreOrder
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api_case.ec.common.common_check import CommonCheck
from test_data.ec.simple.common import Header
from test_dir.api.ec.ec_growth.affiliate import Affiliate
from test_dir.api.ec.ec_item.search_v3.search_by_catalogue_content import SearchByCatalogueContent
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder


class TestAffiliate(weeeTest.TestCase):
    @weeeTest.mark.list( 'Transaction', 'tb1')
    def test_anony_user_affiliate(self, ec_anony_header):
        """ 返利联盟-匿名用户加入返利联盟相关验证流程 """
        # 匿名用户之前要创建porder
        CreatePreOrder().create_preorder(headers=ec_anony_header)
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_anony_header)["object"]
        # 1.注册账号
        email = f'{random.randint(10 ** 9, (10 ** 10) - 1)}@autest.com'
        s_headers = Header().signup_header(email=email)
        assert self.response["result"] is True
        # 2、申请加入返利联盟计划
        Affiliate().affiliate_sign_up(headers=s_headers, email=email)
        assert self.response["result"] is True
        # 新加断言
        assert self.response["object"] is True

    @weeeTest.data.file(file_name='ec_item_data.json', return_type='dict')
    @weeeTest.mark.list( 'Transaction', 'tb1')
    def test_new_user_affiliate(self, *args):
        """ 返利联盟-新用户加入返利联盟相关验证流程 """
        # 1.注册账号
        email = f'{random.randint(10 ** 9, (10 ** 10) - 1)}@autest.com'
        s_headers = Header().signup_header(email=email)
        assert self.response["result"] is True
        # 2、申请加入返利联盟计划
        Affiliate().affiliate_sign_up(headers=s_headers, email=email)
        assert self.response["result"] is True
        # 新加断言
        assert self.response["object"] is True

    @weeeTest.mark.list('old_user_affiliate', 'Regression', 'Transaction', 'product')
    def test_old_user_affiliate(self, ec_login_header):
        """ 返利联盟-返利联盟用户返利联盟相关验证流程 """

        # 1.获取返利联盟分享清单，有-直接操作并分享，没有-新增返利联盟清单
        share_lists = Affiliate().share_list_product(headers=ec_login_header)
        if share_lists["object"] is None:
            trending = SearchByCatalogueContent().search_by_catalogue_content(
                headers=ec_login_header,
                filter_sub_category="trending",
                filters=json.dumps({"delivery": True})
            )
            product_id = trending["object"]["contents"][-1]["data"]["id"]
            # 如果分享清单为空， 创建公开分享清单
            create_list_id_a = self.create_list_assertion("A", headers=ec_login_header)
            # 如果分享清单为空， 创建私有分享清单
            create_list_id_d = self.create_list_assertion("D", headers=ec_login_header)
            # pdp 添加商品进返利联盟分享清单
            self.share_list_add_pdp_product_assertion([create_list_id_a["id"], create_list_id_d["id"]], product_id, headers=ec_login_header)
            # 删除返利联盟分享清单里的商品
            self.share_list_delete_product_assertion(create_list_id_a["id"], product_id, headers=ec_login_header)

        else:
            # 创建公开分享清单
            create_list_id_a = self.create_list_assertion("A", headers=ec_login_header)
            share_lists = share_lists["object"]
            for share_list in share_lists:
                share_list_id = share_list["id"]
                on_sale = SearchByCatalogueContent().search_by_catalogue_content(
                    headers=ec_login_header,
                    filter_sub_category="sale",
                    filters=json.dumps({"delivery": True})
                )
                product_id = on_sale["object"]["contents"][-1]["data"]["id"]
                # 查看返利联盟分享清单详情
                list_detail = Affiliate().share_list_detail(
                    headers=ec_login_header,
                    share_list_id=share_list_id
                )
                if list_detail["object"]["product_list"] is None:
                    # 如果返利联盟分享清单为空，清单页面添加商品进返利联盟清单
                    self.share_list_add_product_assertion(share_list_id, product_id, headers=ec_login_header)
                    # # 删除返利联盟分享清单
                    # Affiliate().share_list_delete(headers=af_headers, share_list_id=share_list_id)

                else:
                    # 返利联盟分享清单详情有清单
                    assert list_detail["object"] is not None, f"self.response为： {list_detail['object']}"
                    assert list_detail["object"]["id"] == share_list_id, f"self.response为： {list_detail['object']}"
                    new = SearchByCatalogueContent().search_by_catalogue_content(
                        headers=ec_login_header,
                        filter_sub_category="new",
                        filters=json.dumps({"delivery": True})
                    )
                    # 换个商品再添加
                    product_id = new["object"]["contents"][-2]["data"]["id"]
                    self.share_list_add_product_assertion(share_list_id=share_list_id, product_id=product_id, headers=ec_login_header)

                    # 返利联盟分享
                    affiliate_share = Affiliate().affiliate_share(headers=ec_login_header)
                    assert affiliate_share["object"] is not None, f"self.response为： {affiliate_share['object']}"
                # 删除返利联盟分享清单
                self.share_list_delete_assertion(share_list_id, headers=ec_login_header)
                self.share_list_delete_assertion(create_list_id_a["id"], headers=ec_login_header)

    # @weeeTest.mark.list('anony_user_message', 'Regression', 'Transaction', 'tb1')
    def test_time_banner_anony_user(self, ec_anony_header):
        """返利联盟页面time_banner匿名用户验证流程 """
        # 匿名用户之前要创建porder
        CreatePreOrder().create_preorder(headers=ec_anony_header)
        # 获取用户的porder

        porder = SetUserPorder().set_user_porder(headers=ec_anony_header)
        bar_info = BarInfo().bar_info(headers=ec_anony_header, referrer_id=12491964)
        assert len(bar_info["object"]) > 0
        self.bar_info(bar_info["object"])

    # @weeeTest.mark.list('anony_user_message', 'Regression','Transaction', 'tb1')
    def test_affiliate_get_referral_user(self, ec_af_header):
        """返利联盟邀请用户验证流程 """
        # 待补充
        get_referral_user = Affiliate().affiliate_get_referral_user(headers=ec_af_header
                                                                    )
        assert get_referral_user["result"] is True

    def bar_info(self, content):
        for item in content:
            assert item["bg_color"] is not None
            assert item["bg_img"] is not None
            assert item["title"]["text"] is not None
            assert item["link"] is not None
            assert "/account/login" in item["link"]

    def create_list_assertion(self, status, headers):
        # 创建分享清单
        create_list = Affiliate().share_list_create(headers=headers, status=status)
        assert create_list["object"] is not None, f"create_list_a['object']为：{create_list['object']}"
        assert create_list["object"]["status"] == status, f"create_list_a['object']为：{create_list['object']}"
        assert CommonCheck.list_check(["id", "user_id", 'description', 'source', 'product_ids'], create_list[
            'object'].keys()), f"create_list_a['object'].keys为：{create_list['object'].keys()}"

        share_list_id = create_list["object"]["id"], f"create_list_a['object']为：{create_list['object']}"
        return create_list["object"]

    def share_list_add_pdp_product_assertion(self, share_list_id, product_id, headers):
        # pdp 添加商品进返利联盟分享清单
        pdp_add_aff = Affiliate().share_list_add_pdp_product(headers=headers,
                                                             share_list_ids=share_list_id,
                                                             product_id=product_id
                                                             )
        assert pdp_add_aff["object"] is not None, f'分享返利联盟失败，结果：{pdp_add_aff["object"]}'
        assert "/account/affiliate-list" in pdp_add_aff["object"][
            "view_link"], f'分享返利联盟断言失败，结果：{pdp_add_aff["object"]["view_link"]}'

    def share_list_add_product_assertion(self, share_list_id, product_id, headers):
        # 清单页面添加商品进返利联盟清单
        list_add_product = Affiliate().share_list_add_product(headers=headers,
                                                              share_list_id=share_list_id,
                                                              product_ids=product_id)
        assert list_add_product["result"] is True, f"self.response为： {list_add_product}"
        assert type(list_add_product["object"]["id"]) == int, f"self.response为： {list_add_product}"

    def share_list_delete_product_assertion(self, share_list_id, product_id, headers):
        # 删除返利联盟分享清单里的商品
        delete_product = Affiliate().share_list_delete_product(headers=headers,
                                                               share_list_id=share_list_id,
                                                               product_ids=product_id)
        assert delete_product["result"] is True, f"self.response['result']为：{delete_product['result']}"
        assert delete_product["object"] is True, f"self.response['object']为：{delete_product['object']}"

    def share_list_delete_assertion(self, share_list_id, headers):
        # 删除返利联盟分享清单
        Affiliate().share_list_delete(headers=headers, share_list_id=share_list_id)
        assert self.response["result"] is True, f"self.response为： {self.response['result']}"


