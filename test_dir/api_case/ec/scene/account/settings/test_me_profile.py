# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_item.py
@Description    :
@CreateTime     :  2023/5/16 16:53
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 16:53
"""

import weeeTest
from test_dir.api.ec.ec_customer.favorites.favorites import Favorites
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction


class TestMeProfilePage(weeeTest.TestCase):

    @weeeTest.mark.list('B2B', 'Regression', 'Smoke',  'Transaction')
    def test_check_me_profile_page(self, ec_login_header):
        """ Profile 页面验证流程 """
        me_profile = Favorites().get_user_me_profile_page(headers=ec_login_header)
        assert me_profile["object"] is not None, f'me_profile为{me_profile["object"]}'
        self.me_profile_page_assert(me_profile["object"], headers=ec_login_header)

    def me_profile_page_assert(self, me_profile, headers):
        # 头像
        assert me_profile["head_image_url"] is not None, f'用户me_profile为信息返回异常，请确认{me_profile}'
        assert me_profile["head_image_url"]["name"] is not None, f'用户me_profile为信息返回异常，请确认{me_profile}'
        assert me_profile["head_image_url"]["url"] is not None, f'用户me_profile为信息返回异常，请确认{me_profile}'
        # "个人资料"
        assert me_profile["me_personal_data"] is not None, f'用户me_profile为信息返回异常，请确认{me_profile}'
        assert me_profile["me_personal_data"]["name"] is not None, f'用户me_profile为信息返回异常，请确认{me_profile}'
        assert me_profile["me_personal_data"]["title"] is not None, f'用户me_profile为信息返回异常，请确认{me_profile}'

        assert len(me_profile["profile_list_info"]) == 5, f'用户me_profile为信息返回异常，请确认{me_profile}'
        for item in me_profile["profile_list_info"]:
            assert item["name"] is not None, f'用户me_profile为信息返回异常，请确认{me_profile}'
            assert item["tip"] is not None, f'用户me_profile为信息返回异常，请确认{me_profile}'
            assert item["title"] is not None, f'用户me_profile为信息返回异常，请确认{me_profile}'
            assert item["url"] is not None, f'用户me_profile为信息返回异常，请确认{me_profile}'
            if item["name"] == "me_user_alias":
                # "昵称"
                assert "/account/settings/change-alias" in item[
                    "url"], f'用户me_profile为信息返回异常，请确认{me_profile}'
            elif item["name"] == "me_user_id":
                # "账号"
                assert "/account/settings/change-username" in item[
                    "url"], f'用户me_profile为信息返回异常，请确认{me_profile}'
            elif item["name"] == "me_cell_phone":
                # 电话
                assert "/account/settings/bind-phone" in item["url"], f'用户me_profile为信息返回异常，请确认{me_profile}'
            elif item["name"] == "me_email":
                # 邮箱
                assert "/account/settings/bind-email" in item["url"], f'用户me_profile为信息返回异常，请确认{me_profile}'
            elif item["name"] == "me_bio":
                # 简介
                assert "/account/bio" in item["url"], f'用户me_profile为信息返回异常，请确认{me_profile}'
            CommCheckFunction().comm_check_link(item["url"], headers=headers)

        assert me_profile["show_verify_email"] is not None, f'用户me_profile为信息返回异常，请确认{me_profile}'
        assert me_profile["update_head_image"] is not None, f'用户me_profile为信息返回异常，请确认{me_profile}'
        assert me_profile["update_head_image"]["name"] is not None, f'用户me_profile为信息返回异常，请确认{me_profile}'
        assert me_profile["update_head_image"]["title"] is not None, f'用户me_profile为信息返回异常，请确认{me_profile}'
        assert me_profile["update_head_image"]["url"] is not None, f'用户me_profile为信息返回异常，请确认{me_profile}'
        assert "/account/settings/update-picture" in me_profile["update_head_image"][
            "url"], f'用户me_profile为信息返回异常，请确认{me_profile}'
        assert len(me_profile["user_settings_list_info"]) >= 4, f'用户me_profile为信息返回异常，请确认{me_profile}'
        for item2 in me_profile["user_settings_list_info"]:
            assert item2["name"] is not None, f'用户me_profile为信息返回异常，请确认{me_profile}'
            assert item2["title"] is not None, f'用户me_profile为信息返回异常，请确认{me_profile}'
            if item2["name"] == "me_delivery_address":
                # "地址薄"
                assert "/account/address" in item2["url"], f'用户me_profile为信息返回异常，请确认{me_profile}'
                CommCheckFunction().comm_check_link(item2["url"], headers=headers)
            elif item2["name"] == "me_payment_method":
                # "付款方式"
                assert "/account/settings/payment-method" in item2[
                    "url"], f'用户me_profile为信息返回异常，请确认{me_profile}'
                CommCheckFunction().comm_check_link(item2["url"], headers=headers)
            elif item2["name"] == "me_setting_notifications":
                # 通知
                assert "/account/settings/setting-notification" in item2[
                    "url"], f'用户me_profile为信息返回异常，请确认{me_profile}'
                CommCheckFunction().comm_check_link(item2["url"], headers=headers)
            elif item2["name"] == "me_password":
                # "更改密码"
                assert "/account/settings/update-password" in item2[
                    "url"], f'用户me_profile为信息返回异常，请确认{me_profile}'
                CommCheckFunction().comm_check_link(item2["url"], headers=headers)
            elif item2["name"] == "me_delete_account":
                # "删除账户"
                assert item2["url"] is None, f'用户me_profile为信息返回异常，请确认{me_profile}'
