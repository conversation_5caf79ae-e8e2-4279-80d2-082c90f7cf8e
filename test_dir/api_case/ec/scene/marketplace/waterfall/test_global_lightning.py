# !/usr/bin/python3
# -*- coding: utf-8 -*-
import pytest
import weeeTest
from weeeTest import  jmespath
from test_dir.api.ec.ec_marketplace.global_puls_interfaces.global_lightning import GlobalLightning
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder



class TestGlobalLightning(weeeTest.TestCase):

    @pytest.fixture(scope='class', autouse=True)
    def setup(self, ec_login_header):
        """
        定义全局变量-获取Global+秒杀组件key --目前PC会用到这个参数
        @return:
        """
        global  _date, zipcode
        SetUserPorder().set_user_new_porder(headers=ec_login_header, zipcode="98007", lang="en")
        auto_test_porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)['object']
        _date = auto_test_porder['deal_date']
        zipcode = int(auto_test_porder['zipcode'])

    @weeeTest.mark.list('Regression', 'Transaction', 'singlecase', 'product')
    def test_global_lightning(self, ec_login_header):
        """
        验证Global+ 秒杀
        """
        global_lightning_deals = GlobalLightning().global_lightning_for_mobile(headers=ec_login_header, date=_date, zipcode=zipcode, lang="en")
        if self.response['object'] is None:
            print(f'{global_lightning_deals}当前暂无秒杀，请确认')
            assert self.response["result"] is True

        else:
            global_lightning_title = jmespath(global_lightning_deals, 'object.title')
            global_lightning_link = jmespath(global_lightning_deals, 'object.more_link')
            global_lightning_server_timestamp_now = jmespath(global_lightning_deals, 'object.server_timestamp_now')
            global_lightning_start_time = jmespath(global_lightning_deals, 'object.server_timestamp_today_start')
            global_lightning_end_time = jmespath(global_lightning_deals, 'object.server_timestamp_today_finish')
            global_lightning_total_count = jmespath(global_lightning_deals, 'object.total_count')
            global_lightning_component_title = jmespath(global_lightning_deals, 'object.component_metadata.title')
            global_lightning_component_more_link = jmespath(global_lightning_deals, 'object.component_metadata.more_link')
            global_lightning_component_link_url = jmespath(global_lightning_deals, 'object.component_metadata.link_url')
            global_lightning_products_list = jmespath(global_lightning_deals, 'object.products')

            #Global+秒杀basic info断言：
            assert global_lightning_title is not None and isinstance(global_lightning_title, str), f'Global+秒杀标题下发错误 请检查, {global_lightning_deals}'
            assert global_lightning_link is not None and "lightning-deals" in global_lightning_link, f'Global+秒杀链接下发错误 请检查, {global_lightning_deals}'
            assert global_lightning_server_timestamp_now is not None, f'Global+秒杀时间下发错误 请检查, {global_lightning_deals}'
            assert global_lightning_start_time < global_lightning_end_time, f'Global+秒杀时间下发错误 请检查, {global_lightning_deals}'
            assert global_lightning_total_count is not None and isinstance(global_lightning_total_count, int), f'Global+秒杀商品个数下发错误 请检查, {global_lightning_deals}'
            assert global_lightning_component_title is not None and isinstance(global_lightning_component_title, str), f'Global+秒杀标题下发错误 请检查, {global_lightning_deals}'
            assert global_lightning_component_more_link is not None and "lightning-deals" in global_lightning_component_more_link, f'Global+秒杀链接下发错误 请检查, {global_lightning_deals}'
            assert global_lightning_component_link_url is not None and "lightning-deals" in global_lightning_component_link_url, f'Global+秒杀链接下发错误 请检查, {global_lightning_deals}'
            assert len(global_lightning_products_list) > 0, f'Global+秒杀商品列表下发错误 请检查, {global_lightning_deals}'

            #Global+秒杀商品信息断言：
            for items in global_lightning_products_list:
                assert items['base_price'] > items['price'], f'Global+秒杀数据异常 请检查, {global_lightning_deals}'
                assert items['biz_type'] == "seller", f'Global+秒杀数据异常 请检查, {global_lightning_deals}'
                assert isinstance(items['discount_percentage'], int), f'Global+秒杀数据异常 请检查, {global_lightning_deals}'
                assert int(items['start_time']) >= global_lightning_start_time, f'Global+秒杀数据异常 请检查, {global_lightning_deals}'
                assert int(items['end_time']) <= global_lightning_end_time, f'Global+秒杀数据异常 请检查, {global_lightning_deals}'
                assert items['is_mkpl'] is True, f'Global+秒杀数据异常 请检查, {global_lightning_deals}'
                assert items['max_order_quantity'] is not None and isinstance(items['max_order_quantity'], int), f'Global+秒杀数据异常 请检查, {global_lightning_deals}'
                assert items['min_order_quantity'] is not None and isinstance(items['min_order_quantity'], int), f'Global+秒杀数据异常 请检查, {global_lightning_deals}'
                assert items['product_id'] is not None and isinstance(items['product_id'], int), f'Global+秒杀数据异常 请检查, {global_lightning_deals}'
                assert items['progress'] is not None and isinstance(items['progress'], int), f'Global+秒杀数据异常 请检查, {global_lightning_deals}'
                assert items['vender_id'] is not None and isinstance(items['vender_id'], int), f'Global+秒杀数据异常 请检查, {global_lightning_deals}'
                assert items['vender_info_view']['delivery_desc'] is not None, f'Global+秒杀数据异常 请检查, {global_lightning_deals}'

                if global_lightning_server_timestamp_now < global_lightning_start_time:
                    assert items['status'] == "begoing", f'Global+秒杀数据异常 请检查, {global_lightning_deals}'
                else:
                    assert items['status'] == "ongoing", f'Global+秒杀数据异常 请检查, {global_lightning_deals}'

        # 最终切回原zipcode
        SetUserPorder().set_user_zipcode(headers=ec_login_header, zipcode="98011")


    @weeeTest.mark.list('Regression', 'Transaction')
    def test_global_lightning_landing(self, ec_login_header):
        """
        #进入MKPL秒杀landing页
        """
        # 获取登录header
        # headers = Header().login_header(email=args[0]["login"]["email"], password=args[0]["login"]["password"])
        # 获取用户的preorder
        proder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]

        GlobalLightning().global_lightning_landing(headers=ec_login_header, page_no="0", page_size="20", date=None)
        print('response>>>>>>>', self.response)
        if self.response["object"]:
            first_date = self.response["object"]["dates"][0]["date"]
            GlobalLightning().global_lightning_landing(headers=ec_login_header, page_no="0", page_size="20", date=first_date)
            print("默认秒杀日期：", str(first_date))
            print(self.response)
        else:
            print("当前暂无MKPL秒杀")

        assert self.response["result"] is True


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
