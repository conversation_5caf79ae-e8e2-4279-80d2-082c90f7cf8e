# !/usr/bin/python3
# -*- coding: utf-8 -*-
import pytest
import weeeTest
from weeeTest import weeeConfig, jmespath

from test_dir.api.ec.ec_content.page.qery_page_data import QueryPageData
from test_dir.api.ec.ec_item.search_v3.search_by_keyword_v3 import SearchByKeywordV3
from test_dir.api_case.ec.scene.marketplace import mkpl_util as mkpl
from test_dir.api_case.ec.scene.marketplace.mkpl_util import AllStorePage
from test_dir.api_case.ec.common.common_check_mkpl import WaterfallCheck

class TestMkplGlobalPlusPageScene(weeeTest.TestCase):
    """
    Global+ waterfall页面
    """

    @pytest.fixture(scope='class', autouse=True)
    def setup(self, ec_mkpl_header):
        """
        前置条件 登录加载global+页面cms
        recommend_session：请求waterfall需要前端随机生成，自动化暂时使用AUTO-TEST+时间
        todo：pytest fixture
        :return:
        """
        # 参数对象全局，其他case中直接使用
        global zipcode, sales_org_id, date, recommend_session, global_plus_page, global_plus_page_cms, component_key_lst, all_store

        common = mkpl.Common().user_common_params(header=ec_mkpl_header.get("addr_header"))
        zipcode, sales_org_id = common['zipcode'], common['sales_org_id']
        date = mkpl.Common().porder_date(headers=ec_mkpl_header.get("addr_header"))
        recommend_session = mkpl.Common().recommend_session
        # GlobalPlusPage类实例
        global_plus_page = mkpl.GlobalPlusPage()
        # 类方法请求页面cms
        global_plus_page_cms = global_plus_page.cms_config(ec_mkpl_header.get("addr_header"), zipcode, sales_org_id)
        # 类方法取cms中组件key组成列表返回
        component_key_lst = global_plus_page.page_component_keys(global_plus_page_cms)
        all_store = AllStorePage()

    # @weeeTest.data.file(file_name='ec_marketplace_data.json', return_type='dict')
    @weeeTest.mark.list('skip_Regression',  'Transaction')
    # 案例测试的原因，测试商家活动和coupon都已修改
    def test_waterfall_switch_tabs(self, ec_mkpl_header):
        """ waterfall 分类验证 :点击分类，切换分类 """
        # 请求global+ cms中 key为 cm_content_feed_v2的waterfall组件
        cms_component = global_plus_page.cms_key_component(global_plus_page_cms, 'cm_content_feed_v2')
        # waterfall组件detail
        waterfall = global_plus_page.waterfall_feed_v2(ec_mkpl_header.get("addr_header"), recommend_session, cms_component)
        # waterfall详情中 tabs
        category_keys = global_plus_page.get_waterfall_category_keys(waterfall)
        # 遍历请求 tabs 记录response result
        for key in category_keys:
            res = global_plus_page.waterfall_feed_v2(ec_mkpl_header.get("addr_header"), recommend_session, cms_component, key=key)
            for card_content in jmespath(res, 'object.contents'):
                if card_content['type'] == 'seller':
                    WaterfallCheck().content_seller(card_content)
                if card_content['type'] == 'item':
                    WaterfallCheck().content_item(card_content)

    @weeeTest.mark.list('Regression', 'Transaction', 'production')
    def test_waterfall_onsale_seller_trending(self, ec_mkpl_header):
        """
        特殊分类on sale 展示特价商家和商品，商家与all stores页面Trending store一样[107261]
        :param args:
        :return:
        """
        # 请求global+ cms中 key为 cm_content_feed_v2的waterfall组件
        global trending_list_seller_lst
        cms_component = global_plus_page.cms_key_component(global_plus_page_cms, 'cm_content_feed_v2')
        # 请求waterfall onsale 数据
        waterfall_onsale = global_plus_page.waterfall_feed_v2(ec_mkpl_header.get("addr_header"), recommend_session, cms_component,
                                                              key='sale')
        # waterfall 返回的商家的id list
        waterfall_onsale_seller_lst = []
        for content in jmespath(waterfall_onsale, 'object.contents'):
            if content['type'] == 'seller':
                print(content)
                waterfall_onsale_seller_lst.append(content['data']['seller']['id'])
        # print('>>>waterfall_onsale_seller_lst', waterfall_onsale_seller_lst, type(waterfall_onsale_seller_lst))
        # onsale 返回的商家id list
        onsale_cms = QueryPageData().query_page_data(headers=ec_mkpl_header.get("addr_header"), page_type='8', page_key='recommend',
                                                     lang=ec_mkpl_header.get("addr_header")['Lang'], sales_org_id=sales_org_id,
                                                     zipcode=zipcode)
        for component in jmespath(onsale_cms, 'object.layout.sections[0].components'):
            if component['component_key'] == 'cm_mkpl_seller_line':
                trending_list = all_store.cms_component_detail(ec_mkpl_header.get("addr_header"), tab_key='recommend',
                                                               cms_component=component)
                trending_list_seller_lst = jmespath(trending_list, 'object[*].seller_id')
                # print('>>>trending_list_sellers', trending_list_seller_lst, type(trending_list_seller_lst))

                # 断言 waterfall seller list 是onsale 子集
                assert set(waterfall_onsale_seller_lst).issubset(trending_list_seller_lst)

    @weeeTest.mark.list('Regression',  'Transaction', 'production')
    def test_waterfall_search_keyword(self, ec_mkpl_header):
        """
        waterfall 搜索关键词,搜索结果为Mkpl商品[107265]
        """
        # marketplace 搜索关键字
        mkpl_search_result = SearchByKeywordV3().mkpl_global_search_by_keyword_v3(headers=ec_mkpl_header.get("addr_header"),
                                                                                  date=date,
                                                                                  filter_key_word='japan',
                                                                                  lang=ec_mkpl_header.get("addr_header")['Lang'],
                                                                                  zipcode=zipcode)
        # print('>>>zipcode', zipcode, type(zipcode))
        # print('>>>date', zipcode, type(date))
        # print('>>>headers[\'lang\']', mkpl_common_headers['lang'], type(mkpl_common_headers['lang']))
        # 搜索结果中所有商品的is_mkpl字段
        # print('>>>>>mkpl_search_result', mkpl_search_result)
        product_type = jmespath(mkpl_search_result, 'object.products[*].is_mkpl')
        # print(">>>>product_type", product_type)
        # 断言全为真
        assert all(product_type)

    def test_waterfall_card_types(self, ec_mkpl_header):
        """
         waterfall瀑布流验证:第一个位置展示３个竖着的Banner,第8，15展示随机商家卡片或视频,第二页开始，在21，28，35随机展示视频或商家卡片[108503]
        """
        # 请求global+ cms中 key为 cm_content_feed_v2的waterfall组件
        cms_component = global_plus_page.cms_key_component(global_plus_page_cms, 'cm_content_feed_v2')
        # waterfall组件detail
        waterfall = global_plus_page.waterfall_feed_v2(ec_mkpl_header.get("addr_header"), recommend_session, cms_component)
        for content in waterfall['object']['contents']:
            if content['type'] == "item":
                assert WaterfallCheck.content_item(content)
            elif content['type'] == "banners":
                assert WaterfallCheck.content_banners(content)
            elif content['type'] == "seller":
                assert WaterfallCheck.content_seller(content)
        # waterfall详情中 tabs
        category_keys = global_plus_page.get_waterfall_category_keys(waterfall)
        # 请求 waterfall第一个推荐tab 加载更多第二页数据
        loadmore_waterfall_res = global_plus_page.waterfall_feed_v2(ec_mkpl_header.get("addr_header"), recommend_session,
                                                                    cms_component,
                                                                    page_num=2, key=category_keys[0])
        # 返回卡片类型
        first_load_card_types = jmespath(waterfall, 'object.contents[*].type')
        load_more_card_types = jmespath(loadmore_waterfall_res, 'object.contents[*].type')
        # 第一个位置展示３个竖着的Banner（只判断banner 生产banner数量可配置）
        card_type = [first_load_card_types[7], first_load_card_types[14], load_more_card_types[0],
                     load_more_card_types[7], load_more_card_types[14]]
        # 结果指定位置卡片类型
        result_type_set = set(card_type)
        # print('>>>>>>result_type_set', result_type_set)
        # 预期卡片类型 商家卡片 视频卡片 商品
        except_type = ['normal_content_video', 'video', 'seller', 'item']
        # print('>>>>>>except_type', except_type)

        except_type_set = set(except_type)
        # 断言:第一个位置展示banner ，第8，15展示随机商家卡片或视频,第二页开始，在21，28，35随机展示视频或商家卡片（卡片类型）
        assert first_load_card_types[0] == 'banners' and result_type_set.issubset(except_type_set)


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    weeeTest.main()
