import json
import uuid
from urllib.parse import urlparse

import weeeTest
from weeeTest import jmespath

from test_dir.api.ec.ec_content.page.qery_page_data import QueryPageData
from test_dir.api.ec.ec_marketplace.home_carousel.home_mkpl_carousel import HomeMkplCarousel
from test_dir.api_case.ec.common.common_check import CommonCheck


class TestFbwFreshDeliLandPageScene(weeeTest.TestCase):
    """
    FBW freshdeli landing  page
    """

    @weeeTest.mark.list('Regression', 'Smoke', 'Transaction', 'product', 'freshdeli_landing')
    def test_fbw_freshdeli_landing_page(self, ec_mkpl_header):
        """
        fbw freshdeli landing page
        """
        header, zipcode, sales_org_id, date = ec_mkpl_header['addr_header'], ec_mkpl_header['zipcode'], ec_mkpl_header[
            'sales_org_id'], ec_mkpl_header['date']
        fbw_landing_page_cms = QueryPageData().query_page_data(headers=header, page_key='freshdeli',
                                                               page_type='8', lang='en',
                                                               sales_org_id=sales_org_id, zipcode=zipcode)
        page_param = fbw_landing_page_cms['object']['layout']['page_param']
        components = fbw_landing_page_cms['object']['layout']['sections'][0]["components"]
        # assert page_param["share_title"] is not None
        assert page_param["page_theme"] is not None, f'餐馆卤味landing页数据异常{page_param}'
        assert page_param["page_name"] is not None, f'餐馆卤味landing页数据异常{page_param}'
        # assert page_param["share_desc"] is not None
        # assert page_param["share_image"] is not None
        for item in components:
            CommonCheck().check_fbw_component_assert(component=item, page_key="freshdeli")


    @weeeTest.data.file(file_name='ec_marketplace_data.json', return_type='dict')
    @weeeTest.mark.list('Regression', 'Transaction', 'product', 'freshdeli_landing')
    def test_fbw_freshdeli_landing_page_header_title(self, *args, ec_mkpl_header):
        """
        fbw freshdeli landing page 顶部title subtitle description
        """
        zipcode, sales_org_id, date = ec_mkpl_header['zipcode'], ec_mkpl_header[
            'sales_org_id'], ec_mkpl_header['date']
        fbw_landing_page_cms = QueryPageData().query_page_data(headers=ec_mkpl_header.get("addr_header"), page_key='freshdeli',
                                                               page_type='8', lang='en',
                                                               sales_org_id=sales_org_id, zipcode=zipcode)
        # fbw bakery 返回cms 第一个component cm_title_rich
        landing_header_component = fbw_landing_page_cms['object']['layout']['sections'][0]['components'][0]

        # 断言 sub_title title desc
        # assert bakery_header_component['properties']['sub_title'] == args[0]['fbw_bakery']['header_sub_title_en']
        # and bakery_header_component['properties']['title'] == args[0]['fbw_bakery']['header_title_en'] \
        #     and bakery_header_component['properties'][
        #         'desc'] == args[0]['fbw_bakery'][
        #         'header_desc_en'], f'bakery_header_component：{bakery_header_component}'
        # MKPL-9659组件"cm_title_rich_v2"
        assert landing_header_component['properties']['title'] == args[0]['fbw_freshdeli']['header_title_en_v2'] and \
               landing_header_component['properties']['style'] == 'v2'


    @weeeTest.data.file(file_name='ec_marketplace_data.json', return_type='dict')
    @weeeTest.mark.list('Regression', 'Transaction', 'freshdeli_landing')
    def test_fbw_freshdeli_landing_page_text_array(self, *args, ec_mkpl_header):
        """
        fbw freshdeli landing page text array文案
        """
        header, zipcode, sales_org_id, date = ec_mkpl_header['addr_header'], ec_mkpl_header['zipcode'], ec_mkpl_header[
            'sales_org_id'], ec_mkpl_header['date']
        fbw_landing_page_cms = QueryPageData().query_page_data(headers=header, page_key='freshdeli',
                                                               page_type='8', lang='en',
                                                               sales_org_id=sales_org_id, zipcode=zipcode)

        # fbw bakery 返回cms 第一个component cm_title_rich
        landing_text_component = fbw_landing_page_cms['object']['layout']['sections'][0]['components'][1]
        # cms 返回fbw  2x2 描述
        text_array = landing_text_component['properties']['text_array']
        # str 转list
        text_array_lst = eval(text_array)
        bakery_text = [i['text'] for i in text_array_lst]
        expect_text = args[0]['fbw_freshdeli']['text_array_v2']
        assert set(bakery_text).issubset(set(expect_text)), f'landing_text_array：{bakery_text}'

    @weeeTest.mark.list('Regression', 'Transaction', 'product', 'freshdeli_landing')
    def test_fbw_freshdeli_landing_page_new_arrival_carousel(self, ec_mkpl_header):
        """
        fbw freshdeli landing page New arrival组件title&link,商品biz type为fbw
        """
        header, zipcode, sales_org_id, date = ec_mkpl_header['addr_header'], ec_mkpl_header['zipcode'], ec_mkpl_header[
            'sales_org_id'], ec_mkpl_header['date']
        fbw_landing_page_cms = QueryPageData().query_page_data(headers=header, page_key='freshdeli',
                                                               page_type='8', lang='en',
                                                               sales_org_id=sales_org_id, zipcode=zipcode)
        # cms页面components
        components = fbw_landing_page_cms['object']['layout']['sections'][0]['components']
        # getPage返回 new arrival component组件 list[0]
        new_arrival_component = \
            [component for component in components if component['component_instance_key'] == 'cm_product_line_new'][0]
        # 组件more link
        link = new_arrival_component['properties']['more_link']
        # link path
        path = urlparse(link).path
        # 组件title
        title = new_arrival_component['properties']['title']
        # 断言组件title 和 more link
        assert path == '/promotion/collection/freshdeliynew' and title == 'New Arrival'
        new_arrival_component_ds = new_arrival_component['datasource'][0]
        new_arrival_component_link = \
            fbw_landing_page_cms['object']['datasource']['{}'.format(new_arrival_component_ds)][
                'now']
        self.get(headers=header, url=new_arrival_component_link,
                 params={'dataobject_key': new_arrival_component_ds})
        new_arrival_detail = self.response
        if len(new_arrival_detail['object']['products']):
            # new_arrival所有商品 biz type
            products_biz_type = jmespath(new_arrival_detail, 'object.products[*].biz_type')
            # 去重
            product_types = list(set(products_biz_type))
            # 断言所有商品biz type都是fbw 去重后type list只有fbw 1个type
            assert len(product_types) == 1 and product_types[0] == 'fbw', f'new_arrival_detail products {product_types}'

    @weeeTest.mark.list('Regression', 'Transaction', 'product', 'freshdeli_landing')
    def test_fbw_freshdeli_landing_page_buy_again_carousel(self, ec_mkpl_header):
        """
        fbw freshdeli landing page buy_again组件title&link,商品biz type为fbw
        """
        header, zipcode, sales_org_id, date = ec_mkpl_header['addr_header'], ec_mkpl_header['zipcode'], ec_mkpl_header[
            'sales_org_id'], ec_mkpl_header['date']
        fbw_landing_page_cms = QueryPageData().query_page_data(headers=header, page_key='freshdeli',
                                                               page_type='8', lang='en',
                                                               sales_org_id=sales_org_id, zipcode=zipcode)
        # cms页面components
        components = fbw_landing_page_cms['object']['layout']['sections'][0]['components']
        # getPage返回 new arrival component组件 list[0]
        buy_again_component = \
            [component for component in components if
             component['component_instance_key'] == 'cm_product_line_buy_again'][0]
        # 组件more link
        link = buy_again_component['properties']['more_link']
        # link path
        path = urlparse(link).path
        # 组件title
        title = buy_again_component['properties']['title']
        # 断言组件title 和 more link
        assert path == '/promotion/collection/freshdelibuyagain' and title == 'Buy Again'
        buy_again_component_ds = buy_again_component['datasource'][0]
        buy_again_component_link = \
            fbw_landing_page_cms['object']['datasource']['{}'.format(buy_again_component_ds)][
                'now']
        self.get(headers=header, url=buy_again_component_link,
                 params={'dataobject_key': buy_again_component_ds})
        buy_again_detail = self.response
        if len(buy_again_detail['object']['products']):
            # new_arrival所有商品 biz type
            products_biz_type = jmespath(buy_again_detail, 'object.products[*].biz_type')
            # 去重
            product_types = list(set(products_biz_type))
            # 断言所有商品biz type都是fbw 去重后type list只有fbw 1个type
            assert len(product_types) == 1 and product_types[0] == 'fbw', f'buy_again_detail products {product_types}'

    # @weeeTest.mark.list('Regression', 'Transaction', 'product', 'freshdeli_landing')
    # def test_fbw_freshdeli_landing_page_shop_by_bakery_carousel(self):
    #     """
    #     MKPL-12571 取消对应组件 注释case
    #     fbw freshdeli landing page shop by bakery 组件title&link,商品都是biz type为fbw
    #     """
    #     # cms页面components
    #     components = fbw_landing_page_cms['object']['layout']['sections'][0]['components']
    #     # getPage返回 new arrival component组件 list[0]
    #     shop_by_bakery_component = \
    #         [component for component in components if component['component_instance_key'] == 'cm_nav_line'][0]
    #
    #     # 组件title
    #     title = shop_by_bakery_component['properties']['title']
    #     # 断言组件title 和 more link
    #     assert title == 'Shop by restaurant'
    #     # cms page 返回 data source配置
    #     shop_by_landing_component_ds = shop_by_bakery_component['datasource'][0]
    #     shop_by_landing_component_link = \
    #         fbw_landing_page_cms['object']['datasource']['{}'.format(shop_by_landing_component_ds)][
    #             'now']
    #     # 请求cms详情
    #     self.get(headers=mkpl_common_headers, url=shop_by_landing_component_link,
    #              params={'dataobject_key': shop_by_landing_component_ds})
    #     shop_by_landing_detail = self.response
    #     # 组件data type
    #     shop_by_landing_detail_data_type = jmespath(shop_by_landing_detail, 'object.data[*].type')
    #     # 组件data link
    #     shop_by_landing_detail_data_link_url = jmespath(shop_by_landing_detail, 'object.data[*].link_url')
    #     link_url_fbw = [i for i in shop_by_landing_detail_data_link_url if "biz_type=fbw" in i]
    #     # 断言 组件type data数据type， link都包含 biz_type=fbw
    #     assert shop_by_landing_detail['object']['type'] == 'bakery' and len(
    #         set(shop_by_landing_detail_data_type)) == 1 and shop_by_landing_detail_data_type[0] == 'vendor' and len(
    #         link_url_fbw) == len(
    #         shop_by_landing_detail_data_link_url), f'fbw Shop by restaurant data_type: {shop_by_landing_detail_data_type}'

    @weeeTest.mark.list('Regression', 'Transaction', 'product', 'freshdeli_landing')
    def test_fbw_freshdeli_landing_page_all_products(self, ec_mkpl_header):
        """
        fbw freshdeli landing page all products
        """
        header, zipcode, sales_org_id, date = ec_mkpl_header['addr_header'], ec_mkpl_header['zipcode'], ec_mkpl_header[
            'sales_org_id'], ec_mkpl_header['date']
        fbw_landing_page_cms = QueryPageData().query_page_data(headers=header, page_key='freshdeli',
                                                               page_type='8', lang='en',
                                                               sales_org_id=sales_org_id, zipcode=zipcode)
        components = fbw_landing_page_cms['object']['layout']['sections'][0]['components']
        # getPage返回 new arrival component组件 list[0]
        all_products_component = \
            [component for component in components if component['component_instance_key'] == 'cm_content_feed_v2'][0]

        # 组件title
        title = all_products_component['properties']['title']
        # 断言组件title 和 more link
        assert title == 'All products'
        # cms page 返回 data source配置
        all_products_component_ds = all_products_component['datasource'][0]
        all_products_component_link = \
            fbw_landing_page_cms['object']['datasource']['{}'.format(all_products_component_ds)][
                'now']
        # 请求cms详情
        self.get(headers=header, url=all_products_component_link,
                 params={'dataobject_key': all_products_component_ds,
                         'page_num': '1'})
        all_products_detail = self.response
        detail_biz_type = jmespath(all_products_detail, 'object.contents[*].data.product.biz_type')
        assert len(set(detail_biz_type)) == 1 and detail_biz_type[
            0] == 'fbw', f'fbw feeds detail_biz_type[0]{detail_biz_type[0]}'

    @weeeTest.mark.list('Regression', 'Transaction', 'product', 'all_products_tab')
    def test_fbw_freshdeli_landing_page_all_products_tab(self, ec_mkpl_header):
        """
        FBW-餐馆卤味落地页所有菜品模块切换子分类tab流程验证
        """
        freshdaily = HomeMkplCarousel().fbw_freshdaily_feed(headers=ec_mkpl_header['addr_header'],
                                                            filter_sub_category="restaurant",
                                                            recommend_session=str(uuid.uuid4())
                                                            )
        assert freshdaily["result"] is True, f'freshdaily落地页数据异常{freshdaily}'
        assert len(freshdaily["object"]["contents"]) > 0, f'FBW-餐馆卤味落地页所有菜品模块数据异常{freshdaily}'
        contents = freshdaily["object"]["contents"]
        tabs = freshdaily["object"]["tabs"]
        for item in tabs:
            search_keyword = HomeMkplCarousel().fbw_freshdaily_feed(headers=ec_mkpl_header['addr_header'],
                                                                    recommend_session=str(uuid.uuid4()),
                                                                    filter_sub_category="restaurant",
                                                                    key=item["key"]
                                                                    )
            assert search_keyword["result"] is True, f'FBW-餐馆卤味落地页所有菜品模块数据异常{search_keyword}'
            assert len(
                search_keyword["object"]["contents"]) > 0, f'FBW-餐馆卤味落地页所有菜品模块数据异常{search_keyword}'
            tabs_new = search_keyword["object"]["tabs"]
            # 找到 key 为 freshbakery02 的 tab
            selected_tab = next((tab for tab in tabs_new if tab["key"] == item["key"]), None)
            # 断言 selected_tab 不为空且它的 selected 属性为 True
            assert selected_tab is not None and selected_tab[
                "selected"], f'Tab with key {item["title"]} should be selected.'

    @weeeTest.mark.list('Regression', 'Transaction', 'product', 'all_products_tab')
    def test_fbw_freshdeli_landing_page_all_products_check(self, ec_mkpl_header):
        """
        FBW-餐馆卤味落地页所有菜品模块商品基础校验
        """
        freshdaily = HomeMkplCarousel().fbw_freshdaily_feed(headers=ec_mkpl_header['addr_header'],
                                                            filter_sub_category="restaurant",
                                                            recommend_session=str(uuid.uuid4())
                                                            )
        assert freshdaily["result"] is True, f'freshdaily落地页数据异常{freshdaily}'
        assert len(freshdaily["object"]["contents"]) > 0, f'FBW-餐馆卤味落地页所有菜品模块数据异常{freshdaily}'
        contents = freshdaily["object"]["contents"]
        for index, item in enumerate(contents):
            CommonCheck().check_product_info(headers=ec_mkpl_header['addr_header'], product=item["data"]["product"],
                                             category_type="freshgourmet")
            if index == 5:
                break

    @weeeTest.mark.list('Regression', 'Transaction', 'product', 'landing_page_shop_list')
    def test_fbw_freshdeli_landing_page_shop_list(self, ec_mkpl_header):
        """
        FBW-餐馆卤味落地页all products 推荐商店流程验证
        """
        shop_list = HomeMkplCarousel().fbw_freshdaily_shop_list(headers=ec_mkpl_header['addr_header'],
                                                                category_num="18"
                                                                )
        assert shop_list["result"] is True
        assert len(shop_list["object"]["data"]) > 0, f'FBW-餐馆卤味落地页数据异常{shop_list}'
        assert shop_list["object"]["type"] == "bakery"
        for item in shop_list["object"]["data"]:
            assert item["id"] is not None, f'FBW-餐馆卤味落地页数据异常{shop_list}'
            assert item["image_url"] is not None, f'FBW-餐馆卤味落地页数据异常{shop_list}'
            assert item["link_url"] is not None, f'FBW-餐馆卤味落地页数据异常{shop_list}'
            assert "/mkpl/vendor/" + str(item["id"]) + "?biz_type=fbw" in item[
                "link_url"], f'FBW-餐馆卤味落地页数据异常{shop_list}'
            if item.get("tags"):
                item.get("tags")[0].get("title"), f"tags.title为空，item={item}"
                item.get("tags")[0].get("color"), f"tags.color为空，item={item}"
                item.get("tags")[0].get("background_color"), f"tags.background_color为空，item={item}"
            # assert item["tags"] is not None, f'FBW-餐馆卤味落地页数据异常{shop_list}'
            assert item["title"] is not None, f'FBW-餐馆卤味落地页数据异常{shop_list}'
            assert item["type"] == "vendor", f'FBW-餐馆卤味落地页数据异常{shop_list}'


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
