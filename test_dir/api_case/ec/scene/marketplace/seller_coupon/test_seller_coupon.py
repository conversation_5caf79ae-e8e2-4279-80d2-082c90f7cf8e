import re

import pytest
import weeeTest
from weeeTest import jmespath
from test_dir.api.ec.ec_item.search_vender_rest.search_vender import SearchVender
from test_dir.api.ec.ec_marketplace.coupon_apis.plan_id_coupon import CouponsSellers
from test_dir.api.ec.ec_marketplace.global_puls_interfaces.get_global_welcome_coupons import GetGlobalWelcomeCoupons
from test_dir.api.ec.ec_marketplace.sellerinfo.seller_homepage import VendorHomePage
from test_dir.api.ec.ec_so.coupon.apply_coupon import ApplyCoupon
from test_dir.api.ec.ec_so.coupon.query_available_coupons import QueryAvailableCoupons
from test_dir.api.ec.ec_so.preorder.query_preorder import QueryPreOrder
from test_dir.api.ec.ec_so.seller.marketplacea_seller_cart import SellerCart
from test_dir.api_case.ec.common.remove_all_products_in_cart import RemoveAllProductsInCart

deduce_coupon_pattern = r'^\$\d+(\.\d{1,2})? off$'


@pytest.fixture
def fixture_all_seller_coupon(ec_mkpl_header):
    """
    筛选出welcome coupon中满减coupon
    :return:
    """
    header, zipcode, sales_org_id, date = ec_mkpl_header['addr_header'], ec_mkpl_header['zipcode'], ec_mkpl_header[
        'sales_org_id'], ec_mkpl_header['date']
    welcome_coupon = GetGlobalWelcomeCoupons().get_global_welcome_coupons(headers=header,
                                                                          zipcode=int(zipcode),
                                                                          source_key=None,
                                                                          tab_key='all')

    coupons = welcome_coupon['object']['coupons']
    return coupons


@pytest.fixture
def fixture_deduce_coupon(fixture_all_seller_coupon):
    """
    筛选出welcome coupon中满减coupon
    :return:
    """
    coupons = fixture_all_seller_coupon
    # coupon文案正则匹配过滤满减coupon
    deduce_coupons = [coupon for coupon in coupons if
                      re.match(deduce_coupon_pattern, coupon['promote_title'])]
    # 没有满减coupon时 返回True 用于 skipif
    # return deduce_coupons if len(deduce_coupons) > 0 else True
    return deduce_coupons


@pytest.fixture
def deduce_coupon_with_threshold(fixture_deduce_coupon):
    # 筛选有门槛的满减coupon
    threshold_coupon = [coupon for coupon in fixture_deduce_coupon if 'over $' in coupon['subtitle']]
    # 没有 门槛满减coupon时
    # return threshold_coupon if len(threshold_coupon) > 0 else True
    return threshold_coupon


@pytest.fixture
def fixture_unclaimed_deduce_coupons(fixture_deduce_coupon):
    """
    满减coupon中未领取的
    :param fixture_deduce_coupon:
    :return:
    """
    unclaimed_deduce_coupons = [coupon for coupon in fixture_deduce_coupon if
                                re.match(deduce_coupon_pattern, coupon['promote_title'])
                                and coupon['id'] is None]
    # 没有满减coupon时 返回True 用于 skipif
    # return unclaimed_deduce_coupons if len(unclaimed_deduce_coupons) > 0 else True
    return unclaimed_deduce_coupons


@pytest.fixture
def fixture_percent_coupon(fixture_all_seller_coupon):
    """
    筛选出welcome coupon中百分比coupon
    """
    coupons = fixture_all_seller_coupon
    # 过滤百分比coupon
    percent_coupons = [coupon for coupon in coupons if 'over $' in coupon['subtitle']]
    return percent_coupons


@pytest.fixture
def threshold_percent_coupon(fixture_percent_coupon):
    """
    筛选出welcome coupon中百分比coupon
    """
    coupons = fixture_percent_coupon
    # 过滤百分比coupon
    percent_coupons = [coupon for coupon in coupons if '% off' in coupon['promote_title']]
    return percent_coupons


# @weeeTest.mark.skipif(fixture_deduce_coupon, reason='seller deduce coupon does not exist')
class TestSellerDeduceCouponScene(weeeTest.TestCase):
    @weeeTest.mark.list('product', 'Regression', 'tb1', 'Transaction')
    def test_marketplace_deduce_coupon_consistent(self, fixture_deduce_coupon, ec_mkpl_header):
        """
        welcome coupon满减优惠券领取后和seller页一致
        """
        header = ec_mkpl_header['addr_header']
        # 断言领取未使用的coupon在seller coupon promotion中下发
        fixture_unclaimed_deduce_coupons = [coupon for coupon in fixture_deduce_coupon if
                                            re.match(deduce_coupon_pattern, coupon['promote_title'])
                                            and coupon['id'] is None]
        if len(fixture_unclaimed_deduce_coupons) != 0:
            # 未领取第一个coupon的对应信息
            seller_id = fixture_unclaimed_deduce_coupons[0]['seller_id']
            coupon_plan_id = fixture_unclaimed_deduce_coupons[0]['plan_id']
            coupon_amount_str = fixture_unclaimed_deduce_coupons[0]['promote_title'].split(' ')[0].lstrip('$')
            coupon_amount = eval(coupon_amount_str)
            coupon_amount = int(coupon_amount) if coupon_amount == int(coupon_amount) else coupon_amount
            # 字符串中截取coupon使用门槛 First order at this store=>0/ First order over $XX at this store >XX
            coupon_order_amount_limit_str = fixture_unclaimed_deduce_coupons[0]['subtitle'].split('$')[1].split(' ')[
                0] if 'order over $' in fixture_unclaimed_deduce_coupons[0]['subtitle'] else '0'
            coupon_order_amount_limit = eval(coupon_order_amount_limit_str)
            # coupon amount 兼容int float 小数
            coupon_order_amount_limit = int(coupon_order_amount_limit) if coupon_order_amount_limit == int(
                coupon_order_amount_limit) else coupon_order_amount_limit
            # 领取
            new_claimed = CouponsSellers().coupons_sellers(headers=header, vender_id=seller_id,
                                                           plan_id=coupon_plan_id)
            coupon_id = new_claimed['object']['coupon_id']
            seller_top_info = VendorHomePage().vendor_page_header(headers=header, vendor_id=seller_id)

            coupon_promotion_ids = jmespath(seller_top_info, 'object.coupon_promotions[*].id')
            assert coupon_id in coupon_promotion_ids, \
                f'领取的coupon:{coupon_id},在seller页面的promotion：{coupon_promotion_ids}没有下发'
            coupon_promotion_index = coupon_promotion_ids.index(coupon_id)
            claimed_coupon_promotion = jmespath(seller_top_info,
                                                'object.coupon_promotions[{}]'.format(coupon_promotion_index))
            # 断言seller页面coupon信息和welcome coupon一致
            assert claimed_coupon_promotion['promote_title'] == '${} off on first order'.format(
                coupon_amount), \
                f'在seller页面的promotion文案为：{claimed_coupon_promotion["promote_title"]},领取coupon金额{coupon_amount}'
            # coupon 有使用门槛 subtitle返回
            if coupon_order_amount_limit > 0:
                assert claimed_coupon_promotion['subtitle'] == 'Order over ${}'.format(coupon_order_amount_limit), \
                    f'在seller页面的promotion subtitle文案为：{claimed_coupon_promotion["subtitle"]},领取coupon门槛：{coupon_order_amount_limit}'
        else:
            print("no seller deduce coupon")

    @weeeTest.mark.list('skip_Regression', 'tb1', 'Transaction')
    # 案例测试的原因，测试商家活动和coupon都已修改
    def test_seller_deduce_coupon_below_threshold(self, deduce_coupon_with_threshold, ec_mkpl_header):
        """
        seller 满减coupon加购未达到门槛提示
        """
        header, date = ec_mkpl_header['addr_header'], ec_mkpl_header['date']
        # 使用满减coupon的第一个作为测试coupon,未领取则领取
        if len(deduce_coupon_with_threshold) != 0:
            deduce_coupon = deduce_coupon_with_threshold[0]
            coupon_id = deduce_coupon['id'] if deduce_coupon['id'] else CouponsSellers().coupons_sellers(
                headers=header, vender_id=deduce_coupon['seller_id'], plan_id=deduce_coupon['plan_id'])[
                'object']['coupon_id']
            seller_id, coupon_promote_title = deduce_coupon['seller_id'], deduce_coupon['promote_title']
            # coupon promtion 文案中截取$10 off
            coupon_amount = coupon_promote_title.split(' ')[0].split('$')[1]
            coupon_amount = eval(coupon_amount)
            coupon_amount = int(coupon_amount) if coupon_amount == int(
                coupon_amount) else coupon_amount
            # subtitle 截取 coupon门槛
            coupon_order_amount_limit = deduce_coupon['subtitle'].split('$')[1].split(' ')[0]
            coupon_order_amount_limit = eval(coupon_order_amount_limit)
            coupon_order_amount_limit = int(coupon_order_amount_limit) if coupon_order_amount_limit == int(
                coupon_order_amount_limit) else coupon_order_amount_limit
            seller_products = SearchVender().vender_all_tab_products(headers=header, vender_id=seller_id,
                                                                     date=date, sign='autotest', offset=0, limit=100)[
                'object']['products']
            # 最小加购金额<优惠券门槛的的商品
            # 排除 新建商品 可能未生成tag 影响coupon 无法使用文案
            available_products = [i for i in seller_products if
                                  i['price'] * i[
                                      'min_order_quantity'] <= coupon_order_amount_limit and "new" not in jmespath(i,
                                                                                                                   'label_list[*].label_key')]
            if len(available_products) == 0:
                pytest.skip("no product's price is lower than coupon threshold")
            else:
                product = available_products[0]

            # 清空购物车
            RemoveAllProductsInCart().clear_grocery_cart(headers=header)
            # mkpl 加购 金额低于coupon门槛
            cart = SellerCart().seller_cart_action(headers=header, product_id=product['id'],
                                                   qty=product['sales_min_order_quantity'],
                                                   seller_id=seller_id,
                                                   delivery_date=date, refer_type="seller")
            # porderV5
            QueryPreOrder().query_preorder_v5(headers=header, cart_domain="grocery")
            cart_coupon_check = QueryAvailableCoupons().coupons_list_v2(headers=header,
                                                                        cart_domain="grocery")
            coupons_invalid = cart_coupon_check['object']['coupons_invalid']
            index = [k for k, v in enumerate(coupons_invalid) if v['id'] == coupon_id][0]
            assert f"The code can't be applied (Required order min: ${coupon_order_amount_limit}" in \
                   coupons_invalid[index][
                       'invalid_message'], f'invalid_message{coupons_invalid[index]["invalid_message"]}'
            # coupon apply

            #  用例断言结束清除购物车
            RemoveAllProductsInCart().clear_grocery_cart(headers=header)
        else:
            print("no deduce coupon")

    @weeeTest.mark.list('Regression-restore', 'tb1', 'Transaction')
    def test_seller_deduce_coupon_threshold(self, deduce_coupon_with_threshold, ec_mkpl_header):
        """
        seller 满减coupon加购达到门槛
        """
        # todo 此用例由于业务原因，开发正在修复，待修复后再加入regression 20241120
        header, date = ec_mkpl_header['addr_header'], ec_mkpl_header['date']
        if len(deduce_coupon_with_threshold) != 0:
            # 使用满减coupon的第一个作为测试coupon,未领取则领取
            deduce_coupon = deduce_coupon_with_threshold[0]
            coupon_id = deduce_coupon['id'] if deduce_coupon['id'] else CouponsSellers().coupons_sellers(
                headers=header, vender_id=deduce_coupon['seller_id'], plan_id=deduce_coupon['plan_id'])[
                'object']['coupon_id']
            seller_id, coupon_promote_title = deduce_coupon['seller_id'], deduce_coupon['promote_title']

            # coupon promtion 文案中截取$10 off
            coupon_amount = coupon_promote_title.split(' ')[0].split('$')[1]
            coupon_amount = eval(coupon_amount)
            coupon_amount = int(coupon_amount) if coupon_amount == int(
                coupon_amount) else coupon_amount
            # subtitle 截取 coupon门槛
            coupon_order_amount_limit = deduce_coupon['subtitle'].split('$')[1].split(' ')[0]
            coupon_order_amount_limit = eval(coupon_order_amount_limit)
            coupon_order_amount_limit = int(coupon_order_amount_limit) if coupon_order_amount_limit == int(
                coupon_order_amount_limit) else coupon_order_amount_limit
            seller_products = SearchVender().vender_all_tab_products(headers=header, vender_id=seller_id,
                                                                     date=date, sign='autotest', offset=0, limit=100)[
                'object']['products']
            # 最大加购金额>优惠券门槛的的商品
            available_products = [i for i in seller_products if
                                  i['price'] * min(i['max_order_quantity'],
                                                   i[
                                                       'remaining_count']) >= coupon_order_amount_limit and "new" not in jmespath(
                                      i, 'label_list[*].label_key')]
            if len(available_products) == 0:
                pytest.skip("no product's atc amount meets coupon threshold")
            # product = available_products[0]
            # 清空购物车，加购
            RemoveAllProductsInCart().clear_grocery_cart(headers=header)
            # mkpl 加购 到优惠券门槛
            sub_total_price = 0
            for product in available_products:
                if sub_total_price < coupon_order_amount_limit:
                    cart = SellerCart().seller_cart_action(headers=header, product_id=product['id'],
                                                           qty=min(product['max_order_quantity'],
                                                                   product['remaining_count']),
                                                           seller_id=seller_id,
                                                           delivery_date=date, refer_type="seller")
                    sub_total_price = cart['object']['seller_float_cart_list'][0]['fee_info']['sub_total_price']
                    sub_total_price = float(sub_total_price)
            # porderV5
            QueryPreOrder().query_preorder_v5(headers=header, cart_domain="grocery")
            # checkout coupon
            cart_coupon_check = QueryAvailableCoupons().coupons_list_v2(headers=header,
                                                                        cart_domain="grocery")
            # checkout valid coupon
            coupons_valid_ids = jmespath(cart_coupon_check, 'object.coupons_valid[*].id')
            # 断言 seller coupon在可以用coupon list
            assert coupon_id in coupons_valid_ids, f'seller coupon_id :{coupon_id},returned valid coupons :{coupons_valid_ids}'
            coupons_valid = cart_coupon_check['object']['coupons_valid']
            # 对应Coupon id的 Coupon list index
            index = [k for k, v in enumerate(coupons_valid) if v['id'] == coupon_id][0]

            coupon_code = coupons_valid[index]['key']
            # 断言 满使用门槛时 coupon apply message
            apply_res = ApplyCoupon().coupons_apply_v2(headers=header, cart_domain="grocery",
                                                       coupon_code=coupon_code)
            # cart apply 成功 返回对应的 Coupon code
            assert apply_res['object']['coupon_info']['code'] == coupon_code
            # 清除购物车
            RemoveAllProductsInCart().clear_grocery_cart(headers=header)
        else:
            print("no deduce coupon")


class TestSellerPercentCouponScene(weeeTest.TestCase):

    # @weeeTest.mark.list('product', 'Regression', 'tb1', 'Transaction')
    def test_seller_percent_coupon_below_threshold(self, ec_mkpl_header, threshold_percent_coupon):
        """
        seller 满折coupon加购未达到门槛提示
        """
        header, date = ec_mkpl_header['addr_header'], ec_mkpl_header['date']
        # coupon的第一个作为测试coupon,未领取则领取
        if len(threshold_percent_coupon) != 0:
            percent_coupon = threshold_percent_coupon[0]
            # 没有Coupon id 则领取
            coupon_id = percent_coupon['id'] if percent_coupon['id'] else CouponsSellers().coupons_sellers(
                headers=header, vender_id=percent_coupon['seller_id'], plan_id=percent_coupon['plan_id'])[
                'object']['coupon_id']
            seller_id, coupon_promote_title = percent_coupon['seller_id'], percent_coupon['promote_title']
            # subtitle 截取 coupon门槛
            coupon_order_amount_limit = percent_coupon['subtitle'].split('$')[1].split(' ')[0]
            coupon_order_amount_limit = eval(coupon_order_amount_limit)
            coupon_order_amount_limit = int(coupon_order_amount_limit) if coupon_order_amount_limit == int(
                coupon_order_amount_limit) else coupon_order_amount_limit
            seller_products = SearchVender().vender_all_tab_products(headers=header, vender_id=seller_id,
                                                                     date=date, sign='autotest', offset=0, limit=100)[
                'object']['products']
            # 最小加购金额<优惠券门槛的的商品
            available_products = [i for i in seller_products if
                                  i['price'] * i[
                                      'min_order_quantity'] <= coupon_order_amount_limit and "new" not in jmespath(i,
                                                                                                                   'label_list[*].label_key')]
            if len(available_products) == 0:
                pytest.skip("no product's price is lower than coupon threshold")
            else:
                product = available_products[0]

            # 清空购物车
            RemoveAllProductsInCart().clear_grocery_cart(headers=header)
            # mkpl 加购 金额低于coupon门槛
            cart = SellerCart().seller_cart_action(headers=header, product_id=product['id'],
                                                   qty=product['sales_min_order_quantity'],
                                                   seller_id=seller_id,
                                                   delivery_date=date, refer_type="seller")
            # porderV5
            QueryPreOrder().query_preorder_v5(headers=header, cart_domain="grocery")
            cart_coupon_check = QueryAvailableCoupons().coupons_list_v2(headers=header,
                                                                        cart_domain="grocery")
            coupons_invalid = cart_coupon_check['object']['coupons_invalid']
            index = [k for k, v in enumerate(coupons_invalid) if v['id'] == coupon_id][0]
            # 断言 不满使用门槛时 coupon apply message
            assert coupons_invalid[index][
                       'invalid_message'] == f"The code can't be applied (Required order min: ${coupon_order_amount_limit}, Trade-in items are not eligible)" \
                                             f"", f'invalid_message{coupons_invalid[index]["invalid_message"]}'
            #  用例断言结束清除购物车
            RemoveAllProductsInCart().clear_grocery_cart(headers=header)
        else:
            print("no percent seller coupon")
