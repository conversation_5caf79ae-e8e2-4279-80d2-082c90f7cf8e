# !/usr/bin/python3
# -*- coding: utf-8 -*-
import pytest
import weeeTest
from weeeTest import weeeConfig, jmespath

from test_dir.api.ec.ec_item.recommend.get_preference_products import GetPreferenceProducts
from test_dir.api.ec.ec_marketplace.global_puls_interfaces.list_all_available_seller_ids import \
    ListAllAvailableSellerIds
from test_dir.api.ec.ec_so.seller.marketplacea_seller_cart import Seller<PERSON>art
from test_dir.api.ec.ec_so.seller.query_mkpl_float_cart import MkplFloatCart
from test_dir.api_case.ec.common.remove_all_products_in_cart import RemoveAllProductsInCart
from test_dir.api_case.ec.scene.marketplace import mkpl_util as mkpl
from test_dir.api.ec.ec_so.preorder.query_preorder import QueryPreOrder
from test_dir.api.ec.ec_mkt.activity.activity_trade_in import ActivityTradeIn


class TestMarketpalceCartScene(weeeTest.TestCase):

    @pytest.fixture(scope='class', autouse=True)
    def setup(self, ec_mkpl_header):
        _date = mkpl.Common().porder_date(headers=ec_mkpl_header.get("addr_header"))
        res = ListAllAvailableSellerIds().list_all_available_seller_ids(
            headers=ec_mkpl_header.get("addr_header"),
            zipcode=ec_mkpl_header.get("zipcode")
        )
        seller_id = jmespath(res, 'object')[0]
        # seller all tab返回结果
        all_products = mkpl.SellerPage().all_product_tab(ec_mkpl_header.get("addr_header"), seller_id, _date)
        yield all_products, seller_id, _date
        pass

    @weeeTest.mark.list('singlecase', 'Transaction', 'product')
    def test_mkpl_float_cart_items(self, ec_mkpl_header, setup):
        """
        商家页悬浮购物车,展开数据返回展示 已加购商品列表
        """
        # 清空购物车
        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(ec_mkpl_header.get("addr_header"))
        # 加购
        product_id = jmespath(setup[0], "object.products")[0]["id"]
        qty = jmespath(setup[0], "object.products")[0]["min_order_quantity"]
        cart = SellerCart().seller_cart_action(
            headers=ec_mkpl_header.get("addr_header"), product_id=product_id, qty=qty,
            seller_id=setup[1],
            delivery_date=setup[2], refer_type="seller"
        )
        cart_content = MkplFloatCart().mkpl_float_cart_v2(headers=ec_mkpl_header.get("addr_header"), vendor_id=setup[1],
                                                          show_product_detail=True)
        cart_items = jmespath(cart_content, "object.seller_float_cart_list[0].items[*].product_id")
        # 断言 购物车有已加购的商品 和ETA
        assert int(product_id) in cart_items and jmespath(cart_content,
                                                          "object.seller_float_cart_list[0].shipping_info.shipping_shipment_date")

    @weeeTest.mark.list('Regression', 'Transaction', 'product')
    def test_mkpl_cart_recommend(self, ec_mkpl_header, setup):
        """
        商家页悬浮购物车展示猜你喜欢商品。最多展示10个
        """
        cart_recommend = GetPreferenceProducts().get_preference_products_seller_cart(
            headers=ec_mkpl_header.get("addr_header"),
            seller_id=setup[1]
        )
        # seller 购物车推荐商品
        cart_recommend_products = jmespath(cart_recommend, "object.modules[0].product_list[*].id")
        # seller 所有商品
        seller_all_products = jmespath(setup[0], "object.products[*].id")
        # 断言 推荐商品不超过10个 属于当前商家
        assert len(cart_recommend_products) <= 10 and set(cart_recommend_products).issubset(set(seller_all_products))

    @weeeTest.mark.list('Regression', 'Transaction', 'product')
    def test_mkpl_recommend_atc(self, ec_mkpl_header, setup):
        """
        加购猜你喜欢商品,加购成功，推荐商品在购物车列表展示
        """
        cart_recommend = GetPreferenceProducts().get_preference_products_seller_cart(headers=ec_mkpl_header.get("addr_header"),
                                                                                     seller_id=setup[1])
        # seller 购物车推荐商品 取第一个商品 id
        product_id = jmespath(cart_recommend, "object.modules[0].product_list[*].id")[0]
        qty = jmespath(cart_recommend, "object.modules[0].product_list[*].min_order_quantity")[0]
        # 加购
        cart = SellerCart().seller_cart_action(
            headers=ec_mkpl_header.get("addr_header"),
            product_id=product_id, qty=qty,
            seller_id=setup[1],
            delivery_date=setup[2],
            refer_type="seller"
        )
        cart_content = MkplFloatCart().mkpl_float_cart_v2(
            headers=ec_mkpl_header.get("addr_header"),
            vendor_id=setup[1],
            show_product_detail=True
        )
        # 获取购物车商品列表
        cart_items = jmespath(cart_content, "object.seller_float_cart_list[0].items[*].product_id")
        # 断言 购物车有已加购的商品
        assert int(product_id) in cart_items



    @weeeTest.mark.list('Regression', 'Transaction', 'product', 'single')
    def test_mkpl_cart_add_on(self, ec_mkpl_header, setup):
        """
        Global+ 凑单页用例
        @param ec_mkpl_header:
        @param setup:
        @return:
        """

        # 清空购物车
        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(ec_mkpl_header.get("addr_header"))
        # 加购
        product_id = jmespath(setup[0], "object.products")[0]["id"]
        qty = jmespath(setup[0], "object.products")[0]["min_order_quantity"]
        cart = SellerCart().seller_cart_action(headers=ec_mkpl_header.get("addr_header"),
                                               product_id=product_id, qty=qty, seller_id=setup[1],
                                               delivery_date=setup[2], refer_type="seller")

        #查询大购物车v5
        global_cart = QueryPreOrder().query_preorder_v5(headers=ec_mkpl_header.get("addr_header"), cart_domain="grocery")
        cart_sections = jmespath(global_cart, "object.sections")

        for sections in cart_sections:
            #判断如果是seller类型并且seller id是当前加购的商家：
            if sections['type'] == "seller" and sections['vendor_info']['vendor_id'] == setup[1]:
                activity_info = sections['activity_info']
                for details in activity_info:
                    if details['type'] == "free_shipping":
                        diff_amount = details['diff_amount']
                        if diff_amount <= 0:
                            assert details['url'] is None, f'{global_cart}，检查seller: {setup[1]}Global+大购物车数据'
                            #如果达到seller免运费门槛，此时大购物车凑单入口展示绿色背景
                            assert details['background_color'] == "#D7FAE1", f'{global_cart}，检查seller: {setup[1]} Global+大购物车数据'
                        else:
                            # 不满足seller运费门槛时-大购物车展示距免运门槛的差额信息：
                            assert details[
                                       'background_color'] is None, f'{global_cart}，检查seller: {setup[1]} Global+大购物车数据'
                            assert str(details['diff_amount']) in str(
                                details['offer_content']), f'{global_cart}，检查seller: {setup[1]} Global+大购物车数据'
                            assert details['url'] is not None, f'{global_cart}检查seller: {setup[1]}Global+大购物车数据'
                            assert str(setup[1]) in str(
                                details['url']), f'{global_cart}检查seller: {setup[1]}Global+大购物车数据'

                            # 商家凑单页面的商品数据验证-精选tab
                            global_add_on_product_list = ActivityTradeIn().item_activity_shop_more_global_products(
                                headers=ec_mkpl_header.get("addr_header"), tab='featured',
                                shop_more_type='free_shipping', diff_amount=diff_amount, type='seller',
                                refer_value=setup[1]
                            )
                            price_groups = jmespath(global_add_on_product_list, 'object.priceGroups')
                            product_lists = jmespath(global_add_on_product_list, 'object.products')
                            product_total_count = jmespath(global_add_on_product_list, 'object.total_count')

                            if product_lists is None:
                                #当前商家凑单页暂无add on 商品
                                assert product_total_count == 0, f'请检查seller id: {setup[1]} add on数据,接口返回：{global_add_on_product_list}'
                                assert price_groups is None, f'请检查seller id: {setup[1]} add on数据,接口返回：{global_add_on_product_list}'
                                assert product_lists is None, f'请检查seller id: {setup[1]} add on数据,接口返回：{global_add_on_product_list}'
                            else:
                                assert product_total_count is not None and isinstance(product_total_count, int), f'请检查sellerid: {setup[1]} add on数据,接口返回：{global_add_on_product_list}'
                                assert len(price_groups) > 0, f'请检查seller id: {setup[1]} add on数据,接口返回：{global_add_on_product_list}'
                                assert len(product_lists) > 0, f'请检查seller id: {setup[1]} add on数据,接口返回：{global_add_on_product_list}'

                                #断言检查顶部价格tab的数据：
                                for groups in price_groups:
                                    assert groups['group_key'] is not None, f'请检查seller id: {setup[1]} add on数据,接口返回：{global_add_on_product_list}'
                                    assert groups['group_name'] is not None, f'请检查seller id: {setup[1]} add on数据,接口返回：{global_add_on_product_list}'

                                #断言检查tab下面的商品数据：
                                for products in product_lists:
                                    assert products['is_mkpl'] is True, f'请检查seller id: {setup[1]} add on数据,接口返回：{global_add_on_product_list}'
                                    assert products['price'] is not None, f'请检查seller id: {setup[1]} add on数据,接口返回：{global_add_on_product_list}'
                                    assert products['base_price'] is not None, f'请检查seller id: {setup[1]} add on数据,接口返回：{global_add_on_product_list}'
                                    assert products['biz_type'] == "seller", f'请检查seller id: {setup[1]} add on数据,接口返回：{global_add_on_product_list}'
                                    assert products['vender_id'] == setup[1], f'请检查seller id: {setup[1]} add on数据,接口返回：{global_add_on_product_list}'



if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    weeeTest.main()
