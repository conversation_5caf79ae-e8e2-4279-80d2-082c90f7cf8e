import weeeTest

from test_dir.api.ec.ec_marketplace.global_puls_interfaces.global_origin_list import Globaloriginlist
from test_dir.api.ec.ec_marketplace.global_puls_interfaces.global_store_all import GlobalStore


class TestGlobalOriginlist(weeeTest.TestCase):

    # 获取Global的地区信息
    @weeeTest.mark.list('Transaction', 'product', 'test_global_origin_list')
    def test_global_origin_list(self, ec_login_header):
        """ # 获取Global的地区信息"""
        origin_list = Globaloriginlist().global_origin_list(headers=ec_login_header)
        assert origin_list["result"] is True, f'global全部商店数据异常{origin_list}'
        assert len(origin_list.get("object")) > 0, f'global全部商店数据异常{origin_list}'
        for item in origin_list.get("object"):
            self.global_origin_list_assert(origin_list=item)

    @weeeTest.data.file(file_name='ec_marketplace_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'test_global_store')
    def test_global_store_all(self, *args, ec_login_header):
        """  # 获取Global所有商家信息"""
        store = GlobalStore().global_store_all(headers=ec_login_header,
                                               origin_ids=args[0]["origin_list"]["recommend"],
                                               dataobject_key=args[0]["dataobject_key_global"]["0"])
        assert store["result"] is True, f'全部商店数据异常{store}'
        assert len(store.get("object").get("data")) > 0, f'全部商店数据异常{store}'
        for item in store.get("object").get("data"):
            self.global_origin_assert(origin_data=item)

    @weeeTest.data.file(file_name='ec_marketplace_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'test_global_store')
    def test_global_store_japan(self, *args, ec_login_header):
        """  # 获取Global-japan商家信息 """
        store = GlobalStore().global_store_all(headers=ec_login_header,
                                               origin_ids=args[0]["origin_list"]["japan"],
                                               dataobject_key=args[0]["dataobject_key_global"]["10"])
        assert store["result"] is True, f'全部商店数据异常{store}'
        assert len(store.get("object").get("data")) > 0, f'全部商店数据异常{store}'
        for item in store.get("object").get("data"):
            self.global_origin_assert(origin_data=item)

    @weeeTest.data.file(file_name='ec_marketplace_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'test_global_store_korea')
    def test_global_store_korea(self, *args, ec_login_header):
        """  # 获取Global-korea商家信息"""
        store = GlobalStore().global_store_all(headers=ec_login_header,
                                               origin_ids=args[0]["origin_list"]["korea"],
                                               dataobject_key=args[0]["dataobject_key_global"]["11"])
        assert store["result"] is True, f'全部商店数据异常{store}'
        assert len(store.get("object").get("data")) > 0, f'全部商店数据异常{store}'
        for item in store.get("object").get("data"):
            self.global_origin_assert(origin_data=item)

    @weeeTest.data.file(file_name='ec_marketplace_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'test_global_store')
    def test_global_store_usa(self, *args, ec_login_header):
        """  # 获取Global-usa商家信息"""
        store = GlobalStore().global_store_all(headers=ec_login_header,
                                               origin_ids=args[0]["origin_list"]["usa"],
                                               dataobject_key=args[0]["dataobject_key_global"]["5"])
        # product_id = self.response["object"]["data"][0]["products"][0]["id"]
        assert store["result"] is True, f'全部商店数据异常{store}'
        assert len(store.get("object").get("data")) > 0, f'全部商店数据异常{store}'
        for item in store.get("object").get("data"):
            self.global_origin_assert(origin_data=item)

    @weeeTest.data.file(file_name='ec_marketplace_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'test_global_store')
    def test_global_store_others(self, *args, ec_login_header):
        """  # 获取Global-others商家信息"""
        store = GlobalStore().global_store_all(headers=ec_login_header,
                                               origin_ids=args[0]["origin_list"]["others"],
                                               dataobject_key=args[0]["dataobject_key_global"]["-2"])
        assert store["result"] is True, f'全部商店数据异常{store}'
        assert len(store.get("object").get("data")) > 0, f'全部商店数据异常{store}'
        for item in store.get("object").get("data"):
            self.global_origin_assert(origin_data=item)

    def global_origin_list_assert(self, origin_list):
        assert origin_list.get("origin") is not None, f'global全部商店数据异常{origin_list}'
        assert origin_list.get("origin_id") is not None, f'global全部商店数据异常{origin_list}'
        assert origin_list.get("page_key") is not None, f'global全部商店数据异常{origin_list}'

    def global_origin_assert(self, origin_data):
        assert origin_data.get("products") is not None, f'全部商店数据异常{origin_data}'
        # 如果商品返回大于等于10个会有more link
        if len(origin_data.get("products")) >= 10:
            assert origin_data.get("more_link") is not None, f'全部商店数据异常{origin_data}'
        # if origin_data.get("overall_rating") > 3:
        #     assert origin_data.get("overall_rating"), f'全部商店数据异常{origin_data}'
        for item1 in origin_data.get("products"):
            assert item1.get("biz_type") == "seller", f'全部商店数据异常{origin_data}'
        seller_id = origin_data.get("seller_id")
        assert seller_id is not None, f'全部商店数据异常{origin_data}'
        seller_tags = origin_data.get("seller_tags")
        if len(seller_tags) > 0:
            for item2 in seller_tags:
                assert item2.get("background_color") is not None, f'全部商店数据异常{origin_data}'
                assert item2.get("font_color") is not None, f'全部商店数据异常{origin_data}'
                assert item2.get("tag") is not None, f'全部商店数据异常{origin_data}'
                assert item2.get("type") is not None, f'全部商店数据异常{origin_data}'

        assert origin_data.get("seller_url") is not None, f'全部商店数据异常{origin_data}'
        assert origin_data.get("shipping_reminder_content") is not None, f'全部商店数据异常{origin_data}'
        assert origin_data.get("title") is not None, f'全部商店数据异常{origin_data}'
        # assert "/mkpl/vendor/" + str(seller_id) in origin_data.get("more_link"), f'全部商店数据异常{origin_data}'
        assert "/mkpl/vendor/" + str(seller_id) in origin_data.get("seller_url"), f'全部商店数据异常{origin_data}'
