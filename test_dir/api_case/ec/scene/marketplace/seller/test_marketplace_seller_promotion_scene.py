# !/usr/bin/python3
# -*- coding: utf-8 -*-
import re

import pytest
import weeeTest
from weeeTest import weeeConfig, jmespath

from test_dir.api.ec.ec_item.product.pdp_detail import PdpDetail
from test_dir.api.ec.ec_marketplace.sellerinfo.seller_homepage import VendorHomePage
from test_dir.api.ec.ec_so.preorder.query_preorder import QueryPreOrder
from test_dir.api.ec.ec_so.seller.marketplacea_seller_cart import SellerCart
from test_dir.api.ec.ec_so.seller.query_mkpl_float_cart import MkplFloatCart
from test_dir.api_case.ec.common.remove_all_products_in_cart import RemoveAllProductsInCart
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder
from test_dir.api.ec.ec_so.preorder.query_preorder import QueryPreOrder
from test_dir.api_case.ec.scene.marketplace import mkpl_util as mkpl
from test_dir.api.ec.ec_promotion.promotion.query_vendor_promotions import QueryVendorPromotions
from test_dir.api.ec.ec_promotion.promotion.query_promotion_info import QueryPromotionInfo
from test_dir.api.ec.ec_item.search_v3.v3_search import V3Search


class TestMarketplaceSellerPromotion(weeeTest.TestCase):

    @pytest.fixture(scope='class', autouse=True)
    def setup(self, ec_mkpl_header):
        """
        前置条件
         # tb1 测试seller：
        #  满30-5   8097     <EMAIL>/915031
        todo：pytest fixture
        :return:
        """
        # 参数对象全局，其他case中直接使用
        global zipcode, lang, sales_org_id, _date, seller

        # 调用方法生成 mkpl_common_headers zipcode sales_org_id date
        common = mkpl.Common().user_common_params(header=ec_mkpl_header.get("addr_header"))
        zipcode, sales_org_id = common['zipcode'], common[
            'sales_org_id']
        _date = mkpl.Common().porder_date(headers=ec_mkpl_header.get("addr_header"))
        # waterfall seller id
        seller = mkpl.SellerPage()

    @weeeTest.mark.list('skip_product', 'Transaction')
    # 案例测试的原因，测试商家活动和coupon都已修改
    def test_seller_promo_info(self, ec_mkpl_header):
        """
        验证商家已设置门槛活动	商家信息下面展示活动信息：买$xxx，立减$xx
        """
        # 线上测试商家7497
        seller_id = 7497
        # production执行 切换测试zipcode
        SetUserPorder().set_user_zipcode(headers=ec_mkpl_header.get("addr_header"), zipcode=99991)
        # seller page 描述
        desc = VendorHomePage().vendor_page_header(headers=ec_mkpl_header.get("addr_header"), vendor_id=seller_id)
        # 筛出 promotion tag 文案
        seller_tags = jmespath(desc, 'object.seller_tags')
        for tag in seller_tags:
            if tag["type"] == "promo":
                promo_title = tag['tag']
        # 断言正则匹配 "Spend $xx+, get $xx off"
        pattern = r'Spend \$(\d+|\d+\.\d{1,2})\+, get \$(\d+|\d+\.\d{1,2}) off'
        assert re.match(pattern, promo_title), f'seller promo tag {promo_title} format not match the pattern {pattern}'
        # zipcode环境恢复
        SetUserPorder().set_user_zipcode(headers=ec_mkpl_header.get("addr_header"), zipcode=zipcode)

    @weeeTest.mark.list('skip_product', 'Transaction')
    # 案例测试的原因，测试商家活动和coupon都已修改
    def test_product_promo_label(self, ec_mkpl_header):
        """
        验证商家已设置门槛活动	mkpl商品列表卡片底部展示活动信息：买$xxx，立减$xx
        """
        # 线上测试商家7497
        seller_id = 7497
        # production执行 切换测试zipcode
        SetUserPorder().set_user_zipcode(headers=ec_mkpl_header.get("addr_header"), zipcode=99991)
        # seller all product 商品label
        all_products = seller.all_product_tab(ec_mkpl_header.get("addr_header"), seller_id, _date)
        activity_tag = jmespath(all_products, 'object.products[0].activity_tag_list')[0]
        # 断言正则匹配 "Spend $xx+, get $xx off"
        pattern = r'Spend \$(\d+|\d+\.\d{1,2})\+, get \$(\d+|\d+\.\d{1,2}) off'
        assert re.match(pattern,
                        activity_tag), f'seller product tag {activity_tag} format not match the pattern {pattern}'
        # zipcode环境恢复
        SetUserPorder().set_user_zipcode(headers=ec_mkpl_header.get("addr_header"), zipcode=zipcode)

    @weeeTest.mark.list('skip_product', 'Transaction', 'test')
    # 案例测试的原因，测试商家活动和coupon都已修改
    def test_seller_cart_promo_progress(self, ec_mkpl_header):
        """
        验证商家已设置门槛活动	加购不满门槛购物车上方展示：再买$xx 可享xx off
        """
        # 清空购物车

        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(ec_mkpl_header.get("addr_header"))
        # 加购
        seller_id = 7497
        product_id = 2102269
        qty = 1
        SetUserPorder().set_user_zipcode(headers=ec_mkpl_header.get("addr_header"), zipcode=99991)
        cart = SellerCart().seller_cart_action(headers=ec_mkpl_header.get("addr_header"), product_id=product_id, qty=qty,
                                               seller_id=seller_id,
                                               delivery_date=_date, refer_type="seller")
        # 购物车信息
        cart_content = MkplFloatCart().mkpl_float_cart_v2(headers=ec_mkpl_header.get("addr_header"), vendor_id=seller_id,
                                                          show_product_detail=0)
        # 购物车活动信息
        cart_promotion = jmespath(cart_content, "object.seller_float_cart_list[0].promotion_details[0].title.tag_text")
        print('>>>>>>>>cart_promotion', cart_promotion)
        # Additional $xx.xx, get $x.xx off
        pattern = r'Additional \$(\d+|\d+\.\d{1,2}), get \$(\d+|\d+\.\d{1,2}) off'
        assert re.match(pattern,
                        cart_promotion), f'seller cart promotion progress {cart_promotion} format not match the pattern {pattern}'

        seller_info = VendorHomePage().vendor_page_header(headers=ec_mkpl_header.get("addr_header"), vendor_id=seller_id)
        seller_promotion_info = jmespath(seller_info, 'object.seller_promotion_info.promotions')
        if seller_promotion_info is None:
            print("该商家暂无活动信息")
        else:
            for promotion in seller_promotion_info:
                if promotion['type'] == "promo_reduce":
                    ps_id = promotion['ps_id']
                    # 点击购买更多-进入活动凑单页面
                    promotion_add_on = QueryPromotionInfo().query_promotion_info(headers=ec_mkpl_header.get("addr_header"),
                                                                             promo_id=ps_id, promo_type="promo")
                    promo_title = jmespath(promotion_add_on, 'object.promote_title')
                    promo_status = jmespath(promotion_add_on, 'object.status')
                    promo_type = jmespath(promotion_add_on, 'object.type')
                    assert promo_title is not None and isinstance(promo_title, str), f'{seller_promotion_info}，检查seller{seller_id}: 大购物车活动数据'
                    assert promo_status == 30 and isinstance(promo_status, int), f'{seller_promotion_info}，检查seller{seller_id}: 大购物车活动数据'
                    assert str(promo_type) == str(promotion['type']), f'{seller_promotion_info}，检查seller{seller_id}: 大购物车活动数据'

                    # 活动商品列表
                    promotion_product = V3Search().ec_search_keyword_promo(headers=ec_mkpl_header.get("addr_header"), tag_id=ps_id)
                    product_list = jmespath(promotion_product, 'object.products')
                    assert len(product_list) > 0, f'{promotion_product}，检查seller{seller_id}: 大购物车活动数据'
                    for sku in product_list:
                        assert sku['activity_tag_list'] is not None, f'{promotion_product}，检查seller{seller_id}: 大购物车活动数据'
                        assert sku['biz_type'] == "seller", f'{promotion_product}，检查seller{seller_id}: 大购物车活动数据'
                        assert sku['product_tag_list'] is not None, f'{promotion_product}，检查seller{seller_id}: 大购物车活动数据'
                        assert sku['vender_info_view'] is not None, f'{promotion_product}，检查seller{seller_id}: 大购物车活动数据'

        # 查询大购物车v5
        global_cart = QueryPreOrder().query_preorder_v5(headers=ec_mkpl_header.get("addr_header"),
                                                        cart_domain="grocery")
        cart_sections = jmespath(global_cart, "object.sections")

        for sections in cart_sections:
            # 判断如果是seller类型并且seller id是当前加购的商家：
            if sections['type'] == "seller" and sections['vendor_info']['vendor_id'] == seller_id:
                activity_info = sections['activity_info']
                for details in activity_info:
                    if details['original_type'] == "promo_reduce":
                        assert details['cart_content'] is not None, f'{global_cart}，检查seller{seller_id}: Global+大购物车数据'
                        assert details['content'] is not None, f'{global_cart}，检查seller{seller_id}: Global+大购物车数据'
                        assert details['desc'] is not None, f'{global_cart}，检查seller{seller_id}: Global+大购物车数据'
                        assert details['limit_content'] is not None, f'{global_cart}，检查seller{seller_id}: Global+大购物车数据'
                        assert details['offer_content'] is not None, f'{global_cart}，检查seller{seller_id}: Global+大购物车数据'
                        assert details['rule_desc'] is not None, f'{global_cart}，检查seller{seller_id}: Global+大购物车数据'
                        assert details['url'] is not None, f'{global_cart}，检查seller{seller_id}: Global+大购物车数据'

        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(ec_mkpl_header.get("addr_header"))
        SetUserPorder().set_user_zipcode(headers=ec_mkpl_header.get("addr_header"), zipcode=zipcode)

    @weeeTest.mark.list('skip_product', 'Transaction')
    # 案例测试的原因，测试商家活动和coupon都已修改
    def test_seller_cart_promo_can_apply(self, ec_mkpl_header):
        """
        验证商家已设置门槛活动	加购满门槛购物车上方展示：可享xx off
        """
        # 清空购物车

        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(ec_mkpl_header.get("addr_header"))
        # 加购
        seller_id = 7497
        product_id = 2102269
        qty = 2
        SetUserPorder().set_user_zipcode(headers=ec_mkpl_header.get("addr_header"), zipcode=99991)
        cart = SellerCart().seller_cart_action(headers=ec_mkpl_header.get("addr_header"), product_id=product_id, qty=qty,
                                               seller_id=seller_id,
                                               delivery_date=_date, refer_type="seller")
        # seller购物车
        cart_content = MkplFloatCart().mkpl_float_cart_v2(headers=ec_mkpl_header.get("addr_header"), vendor_id=seller_id,
                                                          show_product_detail=0)
        # 购物车promotion信息
        cart_promotion = jmespath(cart_content, "object.seller_float_cart_list[0].promotion_details[0].title.tag_text")
        print('>>>>>>>>cart_promotion', cart_promotion)
        # $xx.xx off will be applied at check out!
        pattern = r'\$(\d+|\d+\.\d{1,2}) off will be applied at check out!'
        assert re.match(pattern,
                        cart_promotion), f'seller cart promotion applied: {cart_promotion} format not match the pattern {pattern}'
        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(ec_mkpl_header.get("addr_header"))
        SetUserPorder().set_user_zipcode(headers=ec_mkpl_header.get("addr_header"), zipcode=zipcode)

    @weeeTest.mark.list('singlecase', 'Transaction', 'test')
    def test_seller_homepage_gift_promotion(self, ec_mkpl_header):
        """
        测试seller页面 满赠活动信息banner
        @return:
        """
        seller_info = VendorHomePage().vendor_page_header(headers=ec_mkpl_header.get("addr_header"), vendor_id=8299)
        origin_seller_promotion = QueryVendorPromotions().query_vendor_promotions(headers=ec_mkpl_header.get("addr_header"),
                                                                                  vendor_id=8299)
        seller_tags = jmespath(seller_info, 'object.seller_tags')
        seller_promotion_rich_info = jmespath(seller_info, 'object.promotion_rich_info')
        seller_promotion_banners = jmespath(seller_info, 'object.promotion_banners')
        seller_promotion_info = jmespath(origin_seller_promotion, 'object.promotions')
        if seller_promotion_info[0]['type'] == 'promo_gift':
            origin_gift_product_info = jmespath(origin_seller_promotion, 'object.promotions[0].detail_list')
        else:
            print(f"{origin_seller_promotion}，当前promotion不是满赠活动")

        if len(seller_promotion_info) > 0:
            for items in seller_promotion_info:
                if items['type'] == 'promo_gift':
                    assert len(items['detail_list']) > 0, f"请检查商家活动信息返回数据{seller_info}"
                else:
                    print(f"{items['ps_id']}当前活动不是满赠活动，请检查")
                    assert len(items['detail_list']) == 0, f"请检查商家活动信息返回数据{seller_info}"
                assert items['ps_id'] == origin_seller_promotion['object']['promotions'][0]['ps_id'] and isinstance(
                    items['ps_id'], int), f"请检查商家活动信息返回数据{seller_info}"
                assert items['start_time'] == origin_seller_promotion['object']['promotions'][0][
                    'start_time'] and isinstance(items['start_time'], int), f"请检查商家活动信息返回数据{seller_info}"
                assert items['end_time'] == origin_seller_promotion['object']['promotions'][0][
                    'end_time'] and isinstance(items['start_time'], int), f"请检查商家活动信息返回数据{seller_info}"
                assert items['end_time'] > items['start_time'], f"请检查商家活动信息返回数据{seller_info}"
                assert items['type'] == origin_seller_promotion['object']['promotions'][0]['type'] and isinstance(
                    items['type'], str), f"请检查商家活动信息返回数据{seller_info}"
                assert items['use_url'] == origin_seller_promotion['object']['promotions'][0]['use_url'] and isinstance(
                    items['use_url'], str), f"请检查商家活动信息返回数据{seller_info}"
                assert items['promote_title'] is not None and isinstance(items['promote_title'],
                                                                         str), f"请检查商家活动信息返回数据{seller_info}"
                assert items['min_subtotal_amount'] is not None and isinstance(items['min_subtotal_amount'],
                                                                               int), f"请检查商家活动信息返回数据{seller_info}"

                assert items['detail_list'][0]['product_id'] is not None and isinstance(
                    items['detail_list'][0]['product_id'], int), f"请检查商家活动信息返回数据{seller_info}"
                # 断言seller tags包含promo tag,并且接口正确返回字体颜色及label的背景色，供前端展示
                assert len(seller_tags) > 0, f"请检查商家活动信息返回数据{seller_info}"
                assert any(tags["type"] == "promo" for tags in seller_tags), f"请检查商家活动信息返回数据{seller_info}"
                assert any(
                    tags["font_color"] == "#FFFFFF" for tags in seller_tags), f"请检查商家活动信息返回数据{seller_info}"
                assert any(tags["background_color"] == "#E42D12" for tags in
                           seller_tags), f"请检查商家活动信息返回数据{seller_info}"

            # seller promotion rich info断言基础信息
            for info in seller_promotion_rich_info:
                assert info['title'] is not None and isinstance(info['title'],
                                                                str), f"请检查商家活动信息返回数据{seller_info}"
                assert info['rich_title'] is not None and isinstance(info['rich_title'],
                                                                     str), f"请检查商家活动信息返回数据{seller_info}"
                assert info['use_url'] is not None and isinstance(info['use_url'],
                                                                  str), f"请检查商家活动信息返回数据{seller_info}"

            for banners in seller_promotion_banners:
                assert banners['id'] == origin_seller_promotion['object']['promotions'][0]['ps_id'] and isinstance(
                    banners['id'], int), f"请检查商家活动信息返回数据{seller_info}"
                assert banners['start_time'] == origin_seller_promotion['object']['promotions'][0][
                    'start_time'] and isinstance(banners['start_time'], int), f"请检查商家活动信息返回数据{seller_info}"
                assert banners['end_time'] == origin_seller_promotion['object']['promotions'][0][
                    'end_time'] and isinstance(banners['end_time'], int), f"请检查商家活动信息返回数据{seller_info}"
                assert banners['start_time'] == items['start_time'], f"请检查商家活动信息返回数据{seller_info}"
                assert banners['end_time'] == items['end_time'], f"请检查商家活动信息返回数据{seller_info}"
                assert banners['type'] is not None and isinstance(banners['type'],
                                                                  str), f"请检查商家活动信息返回数据{seller_info}"
                if banners['type'] == "promotion_gift":
                    assert len(banners['products']) > 0, f"请检查商家活动信息返回数据{seller_info}"
                    assert banners['products'][0]['product_id'] == origin_gift_product_info[0][
                        'product_id'], f"请检查商家活动信息返回数据{seller_info}"
                    assert banners['products'][0]['image_url'] is not None and isinstance(
                        banners['products'][0]['image_url'], str), f"请检查商家活动信息返回数据{seller_info}"
                else:
                    print(f"{banners['ps_id']}当前活动不是满赠活动，请检查")
                    assert len(banners['products']) == 0, f"请检查商家活动信息返回数据{seller_info}"

        else:
            print(f"{seller_info}该seller暂无活动信息")
            assert len(seller_promotion_info) == 0, f"请检查商家活动信息返回数据{seller_info}"

        # 加购当前seller的商品，float cart 满赠活动信息下发---待补充


class TestMarketplaceSellerBuyAGetB(weeeTest.TestCase):
    @pytest.fixture(scope='class', autouse=True)
    def setup(self, ec_mkpl_header):
        # 参数对象全局，其他case中直接使用
        global zipcode, lang, sales_org_id, _date
        # 调用方法生成 mkpl_common_headers zipcode sales_org_id date
        common = mkpl.Common().user_common_params(header=ec_mkpl_header.get('addr_header'))
        zipcode, sales_org_id = common['zipcode'], common['sales_org_id']
        _date = mkpl.Common().porder_date(headers=ec_mkpl_header.get('addr_header'))
        SetUserPorder().set_user_zipcode(headers=ec_mkpl_header.get('addr_header'), zipcode=99991)
        yield
        SetUserPorder().set_user_zipcode(headers=ec_mkpl_header.get('addr_header'), zipcode=zipcode)



    @weeeTest.mark.list('skip_product', 'Transaction')
    def test_pdp_buy_a_get_b(self, ec_mkpl_header):
        # 活动停止，用例暂停
        """
        在PDP buy A get B活动
        """
        product_id = 2101517
        detail = PdpDetail().pdp_detail(headers=ec_mkpl_header.get('addr_header'), product_id=product_id, sales_org_id=None,
                                        zipcode=99991, category=None)
        gift_info = jmespath(detail, 'object.product.giftInfo')
        # 断言 tag 文案
        bar_tag_pattern = r"Buy \d+ get free .+"
        assert re.match(bar_tag_pattern,
                        gift_info['bar_tag']), f"bar_tag:{gift_info['bar_tag']} does not match the required format"
        # 断言get B item中 gift_product_id gift_count price存在,name不为空 链接结尾商品id
        for gift in gift_info['gift_list']:
            assert 'gift_product_id' in gift and gift['gift_product_id'], "gift_product_id is missing or null"
            assert 'gift_count' in gift and gift['gift_count'], "gift_count is missing or null"
            assert 'price' in gift and gift['price'], "price is missing or null"
            assert 'name' in gift and gift['name'].strip(), "name is empty or null"
            link_url = gift['link_url']
            assert link_url.endswith(str(gift['gift_product_id'])), "link_url does not end with gift_product_id"

    @weeeTest.mark.list('product-restore', 'Transaction')
    def test_cart_buy_a_get_b(self, ec_mkpl_header):
        """
        在商家购物车中 buy A get B活动商品 赠品
        """
        # 清空购物车

        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(ec_mkpl_header.get('addr_header'))
        seller_id = 7497
        product_id = 2101517
        # 切换99991 测试zipcode
        SetUserPorder().set_user_zipcode(headers=ec_mkpl_header.get('addr_header'), zipcode=99991)
        # 加购 买A赠B主商品
        for qty in [1, 2]:
            cart_action = SellerCart().seller_cart_action(headers=ec_mkpl_header.get('addr_header'), product_id=product_id, qty=qty,
                                                          seller_id=seller_id,
                                                          delivery_date=_date, refer_type="seller")
            global_cart = QueryPreOrder().query_preorder_v5(headers=ec_mkpl_header.get('addr_header'), cart_domain="grocery")
            cart_item = jmespath(global_cart, 'object.sections[0].activity_info[0].items')[0]
            # 断言买A赠B购物车主商品
            assert cart_item['biz_type'] == "seller", f'seller cart item biz_type error :{cart_item}'
            assert 'weeecdn.com' in cart_item['img'], f'seller cart item img {cart_item["img"]} not contain weeecdn.com'
            assert cart_item['product_price'] > 0, f'seller cart item price error :{cart_item["product_price"]}'
            assert cart_item['product_id'] == product_id, f'seller cart item  product_id {cart_item["product_id"]}'
            assert cart_item['biz_type'] == "seller", f'seller cart item biz_type error :{cart_item["biz_type"]}'
            assert cart_item[
                       'seller_id'] == seller_id, f'seller cart item seller_id error :{cart_item["seller_id"]},atc seller id {seller_id}'
            # 断言买A赠B购物车赠品

            buy_a_get_item = cart_item[0]['activity_info'][0]['items'][0]
            assert buy_a_get_item[
                       'biz_type'] == "seller", f'seller buy_a_get_item item biz_type error :{buy_a_get_item}'
            assert buy_a_get_item['is_gift'], f'seller buy_a_get_item item is_gift error :{buy_a_get_item}'
            assert buy_a_get_item[
                       'price_type'] == 'gift', f'seller buy_a_get_item  price_type error :{buy_a_get_item["price_type"]}'
            assert buy_a_get_item['price'] == 0, f'seller buy_a_get_item  price_type error :{buy_a_get_item["price"]}'
            # 断言 加购数量和赠送数量
            assert buy_a_get_item['quantity'] == cart_item[
                'quantity'], f"buy a get b item qty {cart_item['quantity']} not equals to gift item qty {cart_item['quantity']}"
        # 环境恢复 zipcode
        SetUserPorder().set_user_zipcode(headers=ec_mkpl_header.get('addr_header'), zipcode=zipcode)

    @weeeTest.mark.list('skip_product', 'Transaction')
    def test_checkout_buy_a_get_b(self, ec_mkpl_header):
        # 活动停止，用例暂停
        """
        商家活动 buy A get B活动商品结算
        """
        # 清空购物车

        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(ec_mkpl_header.get('addr_header'))
        seller_id = 7497
        product_id = 2101517
        # 切换99991 测试zipcode
        SetUserPorder().set_user_zipcode(headers=ec_mkpl_header.get('addr_header'), zipcode=99991)
        cart_action = SellerCart().seller_cart_action(headers=ec_mkpl_header.get('addr_header'), product_id=product_id, qty=1,
                                                      seller_id=seller_id,
                                                      delivery_date=_date, refer_type="seller")
        # porderV5 预结算(线上不下单)
        pre = QueryPreOrder().query_preorder_v5(headers=ec_mkpl_header.get('addr_header'), cart_domain="grocery")
        pre_items = jmespath(pre, 'object.sections[0].activity_info[0].items[*].product_id')
        assert product_id in pre_items, f'seller promo item {product_id} not in pre items {pre_items}'
        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(ec_mkpl_header.get('addr_header'))
        # 环境恢复 zipcode
        SetUserPorder().set_user_zipcode(headers=ec_mkpl_header.get('addr_header'), zipcode=zipcode)


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    weeeTest.main()
