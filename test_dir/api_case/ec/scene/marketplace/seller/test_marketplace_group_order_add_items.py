import pytest
import weeeTest
from weeeTest import weeeConfig, jmespath
from test_dir.api.ec.ec_so.mkpl_group_buy.create_group_shopping_cart import CreateGroupOrderCart
from test_dir.api.ec.ec_so.mkpl_group_buy.update_group_shopping_cart import UpdateGroupShoppingCart
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_so.order.checkout import Checkout
from test_dir.api.ec.ec_so.order.prepare_checkout import PrepareCheckout
from test_dir.api.ec.ec_so.address.query_user_address_list import QueryUserAddressList
from test_dir.api_case.ec.common.remove_all_products_in_cart import RemoveAllProductsInCart
from test_dir.api.ec.ec_marketplace.global_puls_interfaces.list_all_available_seller_ids import ListAllAvailableSellerIds
from test_dir.api_case.ec.scene.marketplace import mkpl_util as mkpl
from test_dir.api.ec.ec_so.mkpl_group_buy.query_group_shopping_float_cart import QueryGroupShoppingFloatCart
from test_dir.api.ec.ec_so.mkpl_group_buy.query_vendor_group_buy_information import QueryVendorGroupBuyInfo
from test_dir.api.ec.ec_so.mkpl_group_buy.query_groupbuy_seller_items import QueryGroupbuySellerItems
from test_dir.api.ec.ec_customer.account_rest.account_rest import AccountRest
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder
from test_dir.api.ec.ec_so.address.apply_user_address_information import ApplyUserAddressInformation
from test_dir.api.ec.ec_so.order_query.list_my_order import ListMyOrder
from test_dir.api.ec.ec_so.order.cancel_order import CancelOrder


class TestGroupOrder(weeeTest.TestCase):
    @pytest.fixture(scope='class')
    def setup(self, ec_charlie_header, ec_mkpl_guest_header):
        global lang, sales_org_id, date, first_address_id, seller_id, all_products, group_order_info, group_order_key, host_user_id, host_user_name, guest_user_id, guest_user_name
        # 切换到98007地区,获取host用户的porder
        SetUserPorder().set_user_new_porder(headers=ec_charlie_header, zipcode="98007", lang="en")
        # 获取用户的porder
        charlie_test_porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_charlie_header)['object']

        # 切换到98007地区,获取guest用户的porder
        SetUserPorder().set_user_new_porder(headers=ec_mkpl_guest_header, zipcode="98007", lang="en")
        # 获取auto用户的porder
        guest_test_porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_mkpl_guest_header)['object']

        date = charlie_test_porder['deal_date']
        zipcode = int(charlie_test_porder['zipcode'])
        host_user_address_list = QueryUserAddressList().address_list(headers=ec_charlie_header)
        first_address_id = jmespath(host_user_address_list, 'object[0].address_id')

        # 获取商家列表，并取第一个seller id作为拼单流程发起id
        res = ListAllAvailableSellerIds().list_all_available_seller_ids(headers=ec_charlie_header,
                                                                        zipcode=ec_charlie_header.get('zipcode'))
        seller_id = jmespath(res, 'object')[0]
        all_products = mkpl.SellerPage().all_product_tab(ec_charlie_header, seller_id, date)

        # 先创建拼单，获取拼单key作为全局参数
        group_order_info = CreateGroupOrderCart().create_group_order(headers=ec_charlie_header, vendor_id=seller_id)
        group_order_key = jmespath(group_order_info, 'object.key')

        # 当前case先mock 两个用户参加拼单场景 获取charlie chen + auto 账号的基本信息
        host_user_basic_info = AccountRest().me_page_h5_bff_account_portal(headers=ec_charlie_header)
        host_user_id = jmespath(host_user_basic_info, 'object.sections[0][0].data.user_id')
        host_user_name = jmespath(host_user_basic_info, 'object.sections[0][0].data.alias')

        guest_user_basic_info = AccountRest().me_page_h5_bff_account_portal(headers=ec_mkpl_guest_header)
        guest_user_id = jmespath(guest_user_basic_info, 'object.sections[0][0].data.user_id')
        guest_user_name = jmespath(guest_user_basic_info, 'object.sections[0][0].data.alias')


    @weeeTest.mark.list('singlecase', 'Transaction', 'product-restore')
    def test_host_user_add_group_cart_items(self, ec_charlie_header, ec_mkpl_guest_header):
        """
        Host用户加购+Guest用户参与拼团 加购结算流程
        """
        # todo 此用例由于业务原因，开发正在修复，待修复后再加入regression ********
        RemoveAllProductsInCart().clear_grocery_cart(headers=ec_charlie_header)
        product_id = jmespath(all_products, "object.products")[0]["id"]
        qty = jmespath(all_products, "object.products")[0]["min_order_quantity"]
        #Host用户加购商品
        group_cart_add_items = UpdateGroupShoppingCart().update_group_shopping_cart(headers=ec_charlie_header,
                                                                                    key=group_order_key,
                                                                                    product_id=product_id,
                                                                                    quantity=qty,
                                                                                    nick_name=host_user_name,
                                                                                    user_id=host_user_id,
                                                                                    source=f'mweb_group_order_list_host-item_list-{seller_id}')


        #拼单加购接口基础断言：
        assert group_cart_add_items['object']['cart_info'][
                   'key'] == group_order_key, f'拼单key值异常，{group_order_info}'
        assert group_cart_add_items['object']['cart_info']['user_id'] == str(
            host_user_id), f'拼单host信息错误，{group_order_info}'
        assert group_cart_add_items['object']['cart_info']['is_creator'] == True, f'拼单host信息错误，{group_order_info}'
        assert group_cart_add_items['object']['cart_info']['vendor_info'][
                   'vendor_id'] == seller_id, f'拼单商家信息错误，{group_order_info}'
        assert group_cart_add_items['object']['cart_info']['vendor_info'][
                   'vender_logo_url'] is not None and "weeecdn" in \
               group_cart_add_items['object']['cart_info']['vendor_info'][
                   'vender_logo_url'], f'拼单商家信息错误，{group_order_info}'
        assert group_cart_add_items['object']['cart_info']['shipping_info'][
                   'delivery_mode'] == "shipping", f'拼单商家信息错误，{group_order_info}'
        assert group_cart_add_items['object']['cart_info']['shipping_info'][
                   'estimated_time'] is not None, f'拼单商家信息错误，{group_order_info}'
        assert group_cart_add_items['object']['cart_info']['shipping_info'][
                   'shipping_shipment_date'] is not None, f'拼单商家信息错误，{group_order_info}'
        assert group_cart_add_items['object']['cart_info']['shipping_info'][
                   'original_shipping_free_fee'] is not None, f'拼单商家信息错误，{group_order_info}'
        assert group_cart_add_items['object']['cart_info']['shipping_info'][
                   'shipping_free_fee'] is not None, f'拼单商家信息错误，{group_order_info}'
        assert group_cart_add_items['object']['cart_info']['shipping_info'][
                   'orignal_shipping_fee'] is not None, f'拼单商家信息错误，{group_order_info}'
        assert group_cart_add_items['object']['cart_info']['shipping_info'][
                   'shipping_fee'] is not None, f'拼单商家信息错误，{group_order_info}'
        assert group_cart_add_items['object']['cart_info']['shipping_info'][
                   'shipping_type_desc'] is not None, f'拼单商家信息错误，{group_order_info}'
        assert group_cart_add_items['object']['cart_info']['shipping_info'][
                   'shipping_desc'] is not None, f'拼单商家信息错误，{group_order_info}'

        #fee info 断言 加购金额，运费等信息  寻找列表中满足 'is_creator' 条件的元素
        host_user_items = group_cart_add_items['object']['cart_info']['items']
        host_items_subtotal = 0

        for items in host_user_items:

            if items['is_creator'] == True:
                sub_total = items['sub_total']
                for product in items['items']:
                    host_items_subtotal += product.get('total_price', 0.0)
                    # 断言host用户拼单的总商品金额 = 所有加购商品的单价 * 对应加购的数量
                    assert round(product['total_price'], 2) == round(product['price'] * product['quantity'], 2)
                    assert sub_total == host_items_subtotal, f'拼单host信息错误，{group_order_info}'

       # auto用户加入拼单：
        group_cart_add_items = UpdateGroupShoppingCart().update_group_shopping_cart(
                    headers=ec_mkpl_guest_header,
                    key=group_order_key,
                    product_id=product_id,
                    quantity=qty,
                    nick_name=guest_user_name,
                    user_id=guest_user_id,
                    source=f'mweb_group_order_list_guest-item_list-{seller_id}')

        group_order_shipping_fee_info = QueryGroupbuySellerItems().test_groupbuy_seller_items(headers=ec_charlie_header, key=group_order_key)
        group_order_shipping_fee = float(jmespath(group_order_shipping_fee_info, 'object.fee_info.shipping_fee'))
        group_order_total_price = float(jmespath(group_order_shipping_fee_info, 'object.fee_info.total_price_with_activity_and_coupon'))
        group_order_shipping_diff_price = float(jmespath(group_order_shipping_fee_info, 'object.shop_more_info.shipping_fee_diff_price'))
        group_order_original_shipping_fee = float(jmespath(group_order_shipping_fee_info, 'object.shipping_info.shipping_free_fee'))
        group_order_shipping_more_info = jmespath(group_order_shipping_fee_info, 'object.shop_more_info')
        group_order_original_shipping_fee_extra = float(group_order_original_shipping_fee - group_order_total_price)


        if group_order_shipping_fee > 0.00:
          assert round(group_order_shipping_diff_price, 2) == round(group_order_original_shipping_fee_extra, 2), f'拼单购物车信息错误，{group_order_info}'

        else:
            assert group_order_shipping_more_info is None, f'拼单购物车信息错误，{group_order_info}'


        #host用户修改guest参与者拼单购物车的商品数量：
        group_cart_add_items_update = UpdateGroupShoppingCart().update_group_shopping_cart(headers=ec_charlie_header,
                                                                                        key=group_order_key,
                                                                                        product_id=product_id,
                                                                                        quantity=qty + 1,
                                                                                        nick_name=host_user_name,
                                                                                        user_id=guest_user_id,
                                                                                        source=f'mweb_group_order_list_host-item_list-{seller_id}')

        group_order_info_update = QueryGroupShoppingFloatCart().query_group_shopping_float(headers=ec_charlie_header, key=group_order_key)
        group_order_users_info = QueryVendorGroupBuyInfo().vendor_groupbuy_info(headers=ec_charlie_header, vendor_id=seller_id)
        group_cart_add_items_list = jmespath(group_cart_add_items_update, 'object.cart_info.items')

        final_count_users = jmespath(group_order_info_update, 'object.count_users')
        final_count_items = jmespath(group_order_info_update, 'object.count_items')

        group_order_exist_count_users = jmespath(group_order_users_info, 'object.count_users')
        group_order_exist_count_items = jmespath(group_order_users_info, 'object.count_items')

        #断言如果不是拼单创建者用户加购，接口不会下发subtotal信息
        for A in host_user_items:
            if A['is_creator'] == False:
                assert A['sub_total'] is None, f'拼单购物车信息错误，{group_order_info}'
        #最终断言 大购物车 & 商家主页- 同步当前拼单加购人数以及加购商品件数信息
        assert final_count_users == len(group_cart_add_items_list), f'拼单购物车信息错误，{group_order_info}'
        assert final_count_items == group_cart_add_items_update['object']['cart_info']['quantity'], f'拼单购物车信息错误，{group_order_info}'
        assert group_order_exist_count_users == final_count_users, f'拼单购物车信息错误，{group_order_info}'
        assert group_order_exist_count_items == final_count_items, f'拼单购物车信息错误，{group_order_info}'

        #Host用户结算Global+拼单订单，step1 -拼单页面点击结算按钮，进入结算页面 添加相关case断言
        host_group_order_checkout_pre_info = PrepareCheckout().prepare_checkout_v2_marketplace_group_order(headers=ec_charlie_header, cart_domain="group_buy")
        checkout_group_order_shipping_fee = jmespath(host_group_order_checkout_pre_info, 'object.fee_info.shipping_fee')
        checkout_group_order_products = jmespath(host_group_order_checkout_pre_info, 'object.order_reviews[0].group_buy_user_products')
        checkout_group_order_rewards_info = jmespath(host_group_order_checkout_pre_info, 'object.order_reviews[0].point_info.order_reward_points_desc_v2')

        assert float(checkout_group_order_shipping_fee) == float(group_order_shipping_fee), f'请检查拼单购物车{group_order_info}以及结算页面数据{host_group_order_checkout_pre_info}'
        #拼单费用信息以及订单小结
        assert host_group_order_checkout_pre_info['object']['fee_info']['sub_total_price'] is not None and isinstance(host_group_order_checkout_pre_info['object']['fee_info']['sub_total_price'], str), f'请检查拼单购物车{group_order_info}以及结算页面数据{host_group_order_checkout_pre_info}'
        assert host_group_order_checkout_pre_info['object']['fee_info']['total_price_with_activity'] is not None and isinstance(host_group_order_checkout_pre_info['object']['fee_info']['total_price_with_activity'], str), f'请检查拼单购物车{group_order_info}以及结算页面数据{host_group_order_checkout_pre_info}'
        assert host_group_order_checkout_pre_info['object']['fee_info']['total_price_with_coupon'] is not None and isinstance(host_group_order_checkout_pre_info['object']['fee_info']['total_price_with_coupon'], str), f'请检查拼单购物车{group_order_info}以及结算页面数据{host_group_order_checkout_pre_info}'
        assert host_group_order_checkout_pre_info['object']['fee_info']['total_price_with_activity_and_coupon'] is not None and isinstance(host_group_order_checkout_pre_info['object']['fee_info']['total_price_with_activity_and_coupon'], str), f'请检查拼单购物车{group_order_info}以及结算页面数据{host_group_order_checkout_pre_info}'
        assert host_group_order_checkout_pre_info['object']['order_reviews'][0]['type'] == "seller", f'请检查拼单购物车{group_order_info}以及结算页面数据{host_group_order_checkout_pre_info}'
        assert host_group_order_checkout_pre_info['object']['order_reviews'][0]['biz_type'] == "S-normal-0", f'请检查拼单购物车{group_order_info}以及结算页面数据{host_group_order_checkout_pre_info}'
        assert host_group_order_checkout_pre_info['object']['order_reviews'][0]['vendor_info']['vendor_id'] == seller_id, f'请检查拼单购物车{group_order_info}以及结算页面数据{host_group_order_checkout_pre_info}'
        #拼单积分信息断言
        if len(checkout_group_order_rewards_info) > 0:
            for rewards in checkout_group_order_rewards_info:
                assert rewards['reward_points'] is not None and isinstance(rewards['reward_points'], int), f'请检查拼单购物车{group_order_info}以及结算页面数据{host_group_order_checkout_pre_info}'
                assert rewards['reward_type_desc'] is not None and isinstance(rewards['reward_type_desc'], str), f'请检查拼单购物车{group_order_info}以及结算页面数据{host_group_order_checkout_pre_info}'
                assert rewards['reward_type_desc'] is not None and str(rewards['reward_points']) in rewards['reward_type_points'], f'请检查拼单购物车{group_order_info}以及结算页面数据{host_group_order_checkout_pre_info}'
        #拼单 order_reviews 信息断言
        assert host_group_order_checkout_pre_info['object']['order_reviews'][0]['sub_order_type'] == "group_buy", f'请检查拼单购物车{group_order_info}以及结算页面数据{host_group_order_checkout_pre_info}'
        assert host_group_order_checkout_pre_info['object']['order_reviews'][0]['deal_id'] == -11111, f'请检查拼单购物车{group_order_info}以及结算页面数据{host_group_order_checkout_pre_info}'
        assert host_group_order_checkout_pre_info['object']['order_reviews'][0]['shipping_info']['delivery_mode'] == "shipping", f'请检查拼单购物车{group_order_info}以及结算页面数据{host_group_order_checkout_pre_info}'
        assert host_group_order_checkout_pre_info['object']['order_reviews'][0]['shipping_info']['shipping_shipment_date'] is not None and isinstance(host_group_order_checkout_pre_info['object']['order_reviews'][0]['shipping_info']['shipping_shipment_date'], str), f'请检查拼单购物车{group_order_info}以及结算页面数据{host_group_order_checkout_pre_info}'
        assert host_group_order_checkout_pre_info['object']['order_reviews'][0]['shipping_info']['shipping_type_desc'] is not None and isinstance(host_group_order_checkout_pre_info['object']['order_reviews'][0]['shipping_info']['shipping_type_desc'], str), f'请检查拼单购物车{group_order_info}以及结算页面数据{host_group_order_checkout_pre_info}'
        assert host_group_order_checkout_pre_info['object']['order_reviews'][0]['vendor_info']['vendor_id'] == seller_id, f'请检查拼单购物车{group_order_info}以及结算页面数据{host_group_order_checkout_pre_info}'
        assert host_group_order_checkout_pre_info['object']['order_reviews'][0]['vendor_info']['vendor_name'] is not None and isinstance(host_group_order_checkout_pre_info['object']['order_reviews'][0]['vendor_info']['vendor_name'], str), f'请检查拼单购物车{group_order_info}以及结算页面数据{host_group_order_checkout_pre_info}'
        assert host_group_order_checkout_pre_info['object']['user']['global_user_id'] == host_user_id, f'请检查拼单购物车{group_order_info}以及结算页面数据{host_group_order_checkout_pre_info}'

        #断言 如果是拼团订单，结算页面商品明细里面一定会包含发起者 & 参团者的用户id + 用户名 并且与发起者的user id匹配时，is_owner 布尔值必定是true
        for groupuser in checkout_group_order_products:
            if groupuser['is_creator'] == True:
                assert groupuser['user_id'] == str(host_user_id), f'请检查拼单购物车{group_order_info}以及结算页面数据{host_group_order_checkout_pre_info}'
                assert groupuser['nick_name'] == host_user_name, f'请检查拼单购物车{group_order_info}以及结算页面数据{host_group_order_checkout_pre_info}'
                assert groupuser['is_owner'] == True, f'请检查拼单购物车{group_order_info}以及结算页面数据{host_group_order_checkout_pre_info}'

            else:
                    assert groupuser['user_id'] == str(guest_user_id), f'请检查拼单购物车{group_order_info}以及结算页面数据{host_group_order_checkout_pre_info}'
                    assert groupuser['nick_name'] == guest_user_name, f'请检查拼单购物车{group_order_info}以及结算页面数据{host_group_order_checkout_pre_info}'
                    assert groupuser['is_owner'] == False, f'请检查拼单购物车{group_order_info}以及结算页面数据{host_group_order_checkout_pre_info}'

        #step2 发起拼单订单结算：
        group_order_checkout_amount = float(jmespath(host_group_order_checkout_pre_info, "object.fee_info.final_amount"))
        group_order_checkout_pre_id = jmespath(host_group_order_checkout_pre_info, "object.checkout_pre_id")
        apply_host_user_address = ApplyUserAddressInformation().address_apply_v2(headers=ec_charlie_header, address_id=first_address_id)
        global_group_order_placed = Checkout().checkout_v3(headers=ec_charlie_header, cart_domain="group_buy", checkoutAmount=group_order_checkout_amount, checkout_pre_id=group_order_checkout_pre_id)
        global_group_order_id = jmespath(global_group_order_placed, 'object.order_ids')[0]
        global_group_order_final_amount = jmespath(global_group_order_placed, 'object.final_amount')
        global_group_order_success_url = jmespath(global_group_order_placed, 'object.success_url')
        global_group_order_cancel_url = jmespath(global_group_order_placed, 'object.cancel_url')

        #断言拼单订单 id可正常生成以及结算金额正确
        assert global_group_order_id is not None and isinstance(global_group_order_id, int), f'请检查拼单订单结算{global_group_order_placed}'
        assert round(global_group_order_final_amount, 2) == round(group_order_checkout_amount, 2), f'请检查拼单订单结算{global_group_order_placed}'
        assert str(global_group_order_id) in global_group_order_success_url, f'请检查拼单订单结算{global_group_order_placed}'
        assert str(global_group_order_id) in global_group_order_cancel_url, f'请检查拼单订单结算{global_group_order_placed}'

        #host生成订单后，guest 两端订单状态更新为已确认 断言：
        guest_user_group_order_update = QueryGroupShoppingFloatCart().query_group_shopping_float(headers=ec_mkpl_guest_header, key=group_order_key)
        guest_user_group_order_status = jmespath(guest_user_group_order_update, 'object.status')
        host_user_group_order_status = jmespath(guest_user_group_order_update, 'object.owner_cart_status')

        assert guest_user_group_order_status == "F", f'请检查拼单购物车{guest_user_group_order_update}'
        assert host_user_group_order_status == "submitted", f'请检查拼单购物车{guest_user_group_order_update}'

        #host用户 进入订单列表页断言：
        all_orders = ListMyOrder().list_my_order_v2(headers=ec_charlie_header, filter_status="all")
        order_lis = jmespath(all_orders, "object.myOrders[*].id")
        assert global_group_order_id in order_lis, f'请检查{guest_user_group_order_update} 以及订单列表{all_orders}'
        for orders in all_orders['object']['myOrders']:
            if orders['id'] == global_group_order_id:
                assert orders['status'] == "C", f'请检查{guest_user_group_order_update} 以及订单列表{all_orders}'
                assert orders['sub_order_type'] == "group_buy", f'请检查{guest_user_group_order_update} 以及订单列表{all_orders}'

        #host用户取消拼单订单 基础断言：
        CancelOrder().cancel_unpaid_order(headers=ec_charlie_header, order_id=str(global_group_order_id))
        host_user_order_lists = ListMyOrder().list_my_order_v1(headers=ec_charlie_header, filter_status="4")
        cannclled_order_lists = jmespath(host_user_order_lists, "object.myOrders[*].id")
        assert global_group_order_id in cannclled_order_lists, f'请检查{host_user_order_lists}订单列表'

        #最终切回原zipcode
        SetUserPorder().set_user_zipcode(headers=ec_charlie_header, zipcode="98007")
        SetUserPorder().set_user_zipcode(headers=ec_mkpl_guest_header, zipcode="98011")


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    weeeTest.main()
