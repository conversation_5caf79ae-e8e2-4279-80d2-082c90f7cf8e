import pytest
import weeeTest
import copy
from weeeTest import weeeConfig, jmespath
from test_dir.api.ec.ec_so.mkpl_group_buy.create_group_shopping_cart import CreateGroupOrderCart
from test_dir.api.ec.ec_so.mkpl_group_buy.check_group_shopping_cart import MkplGroupOrderQuery
from test_dir.api.ec.ec_so.mkpl_group_buy.query_vendor_group_buy_information import QueryVendorGroupBuyInfo
from test_dir.api.ec.ec_marketplace.global_puls_interfaces.list_all_available_seller_ids import \
    ListAllAvailableSellerIds
from test_dir.api_case.ec.scene.marketplace import mkpl_util as mkpl
from test_dir.api.ec.ec_customer.account_rest.account_rest import AccountRest
from test_dir.api.ec.ec_so.seller.marketplacea_seller_cart import SellerCart
from test_dir.api.ec.ec_so.seller.query_mkpl_float_cart_v2 import MkplFloatCartV2
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder


class TestCreateGroupOrder(weeeTest.TestCase):

    @pytest.fixture(scope='class', autouse=True)
    def setup(self, ec_mkpl_header):
        _date = mkpl.Common().porder_date(headers=ec_mkpl_header.get("addr_header"))
        res = ListAllAvailableSellerIds().list_all_available_seller_ids(
            headers=ec_mkpl_header.get("addr_header"),
            zipcode=ec_mkpl_header.get("zipcode")
        )
        seller_id = jmespath(res, 'object')[0]
        seller = mkpl.SellerPage()
        # seller all tab返回结果
        all_products = mkpl.SellerPage().all_product_tab(ec_mkpl_header.get("addr_header"), seller_id, _date)
        product_id = jmespath(all_products, "object.products")[0]["id"]
        qty = jmespath(all_products, "object.products")[0]["min_order_quantity"]
        global_original_cart_items = SellerCart().seller_cart_action(headers=ec_mkpl_header.get("addr_header"),
                                                                     product_id=product_id, qty=qty,
                                                                     seller_id=seller_id,
                                                                     delivery_date=_date, refer_type="seller")

        # 加购global+商品后，展开悬浮购物车获取数据
        global_seller_float_cart = MkplFloatCartV2().mkpl_seller_cart_v2(headers=ec_mkpl_header.get("addr_header"),
                                                                         seller_id=seller_id)

        seller_float_cart_list = jmespath(global_seller_float_cart, 'object.seller_float_cart_list')

        yield {
            "all_products": all_products,
            "seller_id": seller_id,
            "_date": _date,
            "seller": seller,
            "global_original_cart_items": global_original_cart_items,
            "global_seller_float_cart": global_seller_float_cart,
            "seller_float_cart_list": seller_float_cart_list
        }
        pass


    @weeeTest.mark.list('Regression', 'Transaction', 'product')
    def test_host_create_group_order(self, ec_mkpl_header, setup):
        """
        Host用户创建拼单
        """
        if len(setup.get("seller_float_cart_list")) > 0:
            current_added_items_qty = jmespath(setup.get("global_seller_float_cart"), 'object.seller_float_cart_list[0].quantity')
        else:
            print("当前用户该seller购物车无任何商品或者加购商品失败", f'{setup.get("global_seller_float_cart")}')

        # 获取拼单发起人基础信息：
        group_order_host_basic_info = AccountRest().me_page_h5_bff_account_portal(headers=ec_mkpl_header.get("addr_header"))
        group_order_host_user_id = jmespath(group_order_host_basic_info, 'object.sections[0][0].data.user_id')
        group_order_host_name = jmespath(group_order_host_basic_info, 'object.sections[0][0].data.alias')

        # Host用户发起Global+ 拼单
        group_order_info = CreateGroupOrderCart().create_group_order(headers=ec_mkpl_header.get("addr_header"),
                                                                     vendor_id=setup.get("seller_id"))


        #如果当前Global+购物车商品不为空，则拼单购物车自动同步当前host用户已加购的商品及数量 且默认当前拼单购物车参与人数为host
        #assert group_order_info["object"]["count_users"] == 1, f'Global+ 拼单商品参与人数错误 {group_order_info}'
        #assert group_order_info["object"]["count_items"] == current_added_items_qty, f'Global+ 拼单商品数据同步错误或者商品库存不足{group_order_info}'
        #拼单创建后基础断言校验：
        assert group_order_info['object']['key'].startswith("gb_"), f'Global+ 拼单key值拼接错误{group_order_info}'
        assert group_order_info['object']["is_creator"] == True, f'Global+ 拼单host属性错误{group_order_info}'
        assert group_order_info['object']["start_time"] is not None, f'Global+ 拼单开始时间缺失{group_order_info}'
        assert group_order_info['object']["user_id"] is not None and group_order_info['object']["user_id"] == str(
            group_order_host_user_id), f'Global+ 拼单host用户id不匹配{group_order_info}'
        assert group_order_info['object']["user_name"] is not None and group_order_info['object'][
            "user_name"] == group_order_host_name, f'Global+ 拼单host用户id不匹配{group_order_info}'
        assert group_order_info["object"]["vendor_id"] == setup.get("seller_id"), f'Global+ 拼单seller信息不匹配{group_order_info}'
        # 如果订单未被取消，默认拼单是生效状态
        assert group_order_info["object"]["status"] == "A", f'Global+ 拼单状态错误{group_order_info}'
        assert group_order_info["object"]["delivery_mode"] == "shipping", f'Global+ 拼单配送模式错误{group_order_info}'

        assert group_order_info["object"][
                   "owner_cart_status"] == "adding", f'Global+ 拼单加购状态错误{group_order_info}'
        assert group_order_info["object"]["group_buy_url_host"] is not None and group_order_info['object']['key'] in \
               group_order_info["object"]["group_buy_url_host"], f'Global+ 拼单链接错误{group_order_info}'
        assert group_order_info["object"]["group_buy_tip"] is not None and "5%" in group_order_info["object"][
            "group_buy_tip"], f'Global+ 拼单返利文案异常{group_order_info}'

        """
        创建拼单后-校验seller主页以及大购物车页面 接口正常下发Global+拼单banner信息
        """
        group_order_exist_info = MkplGroupOrderQuery().mkpl_group_buy(headers=ec_mkpl_header.get("addr_header"))
        # 确认当前拼单存在：
        assert group_order_exist_info['object']['key'] is not None and group_order_exist_info['object']['key'] == \
               group_order_info['object']['key'], f'Global+ 拼单当前状态异常{group_order_exist_info}'
        assert group_order_exist_info['object']['vendor_id'] is not None and group_order_exist_info['object'][
            'vendor_id'] == setup.get("seller_id"), f'Global+ 拼单当前状态异常{group_order_exist_info}'

        # Seller主页 & 大购物车 拼单banner信息
        group_order_banner_info = QueryVendorGroupBuyInfo().vendor_groupbuy_info(headers=ec_mkpl_header.get("addr_header"),
                                                                                 vendor_id=setup.get("seller_id"))
        assert group_order_banner_info['object']['key'] == group_order_info['object'][
            'key'], f'Global+ 拼单key直不一致{group_order_banner_info}'
        assert group_order_banner_info['object'][
                   "is_creator"] == True, f'Global+ 拼单host属性错误{group_order_banner_info}'
        assert group_order_banner_info['object'][
                   "start_time"] is not None, f'Global+ 拼单开始时间缺失{group_order_banner_info}'
        assert group_order_banner_info['object']["user_id"] is not None and group_order_banner_info['object'][
            "user_id"] == str(group_order_host_user_id), f'Global+ 拼单host用户id不匹配{group_order_banner_info}'
        assert group_order_banner_info['object']["user_name"] is not None and group_order_banner_info['object'][
            "user_name"] == group_order_host_name, f'Global+ 拼单host用户id不匹配{group_order_banner_info}'
        assert group_order_banner_info["object"][
                   "vendor_id"] == setup.get("seller_id"), f'Global+ 拼单seller信息不匹配{group_order_banner_info}'
        # 如果订单未被取消，默认拼单是生效状态
        assert group_order_banner_info["object"]["status"] == "A", f'Global+ 拼单状态错误{group_order_banner_info}'
        assert group_order_banner_info["object"][
                   "delivery_mode"] == "shipping", f'Global+ 拼单配送模式错误{group_order_banner_info}'
        assert group_order_banner_info["object"]["count_users"] is not None and isinstance(group_order_banner_info["object"]["count_users"],int), f'Global+ 拼单参与人数数据错误{group_order_banner_info}'
        assert group_order_banner_info["object"]["count_items"] is not None and isinstance(
            group_order_banner_info["object"]["count_items"],
            int), f'Global+ 拼单参与人数数据错误{group_order_banner_info}'
        assert group_order_banner_info["object"][
                   "owner_cart_status"] == "adding", f'Global+ 拼单加购状态错误{group_order_banner_info}'
        assert group_order_banner_info["object"]["group_buy_url_host"] is not None and \
               group_order_banner_info['object']['key'] in group_order_info["object"][
                   "group_buy_url_host"], f'Global+ 拼单链接错误{group_order_banner_info}'
        assert group_order_banner_info["object"]["group_buy_tip"] is not None and "5%" in \
               group_order_banner_info["object"]["group_buy_tip"], f'Global+ 拼单返利文案异常{group_order_banner_info}'


        # 最终切回原zipcode
        SetUserPorder().set_user_zipcode(headers=ec_mkpl_header.get("addr_header"), zipcode="98007")


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    weeeTest.main()
