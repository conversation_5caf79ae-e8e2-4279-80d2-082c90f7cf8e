# !/usr/bin/python3
# -*- coding: utf-8 -*-
import json
import re

import pytest
import weeeTest
from weeeTest import weeeConfig, jmespath

from test_dir.api.ec.ec_marketplace.global_puls_interfaces.list_all_available_seller_ids import \
    ListAllAvailableSellerIds
from test_dir.api.ec.ec_marketplace.search.mkpl_search import MkplSearch
from test_dir.api_case.ec.scene.marketplace import mkpl_util as mkpl


class TestMarketpalceAllProductCategoryScene(weeeTest.TestCase):
    @pytest.fixture(scope='class', autouse=True)
    def setup(self, ec_mkpl_header):
        _date = mkpl.Common().porder_date(headers=ec_mkpl_header.get("addr_header"))
        res = ListAllAvailableSellerIds().list_all_available_seller_ids(
            headers=ec_mkpl_header.get("addr_header"),
            zipcode=ec_mkpl_header.get("zipcode")
        )
        seller_id = jmespath(res, 'object')[0]
        seller = mkpl.SellerPage()
        # seller all tab返回结果
        all_products = mkpl.SellerPage().all_product_tab(ec_mkpl_header.get("addr_header"), seller_id, _date)
        yield all_products, seller_id, _date, seller
        pass

    @weeeTest.mark.list('Regression', 'Transaction', 'product')
    def test_store_page_all_products_sold_available(self, ec_mkpl_header, setup):
        """
        页面不展示售罄的商品
        """
        all_product_status = jmespath(setup[0], 'object.products[*].sold_status')
        result = list(set(all_product_status))
        # 判断去重后只有一个，并且和选中的一致：返回的所有商品的分类和选中的一致
        assert len(result) == 1 and all_product_status[0] == 'available'

    @weeeTest.mark.list('Regression', 'Transaction', 'product')
    def test_store_page_all_products_selected_catalogue(self, ec_mkpl_header, setup):
        """ marketplace商家页 All products交互:分类过滤 [104978-1]"""
        # seller all products catalogue


        vendor_product_catalogue = setup[3].get_vendor_product_catalogue(setup[0])
        selected_catalogue = vendor_product_catalogue[1]
        print('>>>>>>>', selected_catalogue)
        # 分类过滤参数序列化转str
        filter_param = {"catalogue_num": selected_catalogue}
        j_str = json.dumps(filter_param)
        filter_str = str(j_str)
        # 选中分类过滤结果
        filter_result = setup[3].all_product_tab(ec_mkpl_header.get("addr_header"), setup[1], setup[2], filters=filter_str)
        # 结果中 所有product的category
        filter_products_category = jmespath(filter_result, 'object.products[*].category')
        # category列表去重
        result_category = list(set(filter_products_category))
        # 判断去重后只有一个，并且和选中的一致：返回的所有商品的分类和选中的一致
        assert len(result_category) == 1 and result_category[0] == selected_catalogue

    @weeeTest.mark.list('product', 'Regression')
    def test_store_page_all_products_selected_sort(self, ec_mkpl_header, setup):
        """ marketplace商家页 All products交互:filter过滤>按照sort[price_desc]筛选 [104978-2]"""
        sort_keys = setup[3].get_vendor_product_sorts(setup[0])
        sort_result = None
        if 'price_desc' in sort_keys:
            # filter结果
            sort_result = setup[3].all_product_tab(headers=ec_mkpl_header.get("addr_header"), seller_id=setup[1], date=setup[2],
                                                 sort='price_desc')

        # 结果中 所有product的category
        products_price_lst = jmespath(sort_result, 'object.products[*].price')
        # 断言 返回商品价格为倒序
        assert products_price_lst == sorted(products_price_lst, reverse=True)

    @weeeTest.mark.list('product', 'Regression')
    def test_store_page_all_products_selected_filter_price(self, ec_mkpl_header, setup):
        """ marketplace商家页 All products交互:filter过滤>按照最后一个价格区间筛选 [104978-3]"""
        # seller 筛选价格区间的最后一个

        # 价格筛选项默认filters property_key 为 6
        price_property_key = '6'
        filters = jmespath(setup[0], 'object.filters')
        for obj in filters:
            if obj["property_key"] == price_property_key:
                # 取最后一个价格区间筛选
                price_limit_key = obj["property_values"][-1]['value_key']
                price_limit_name = obj["property_values"][-1]['value_name']
        # 最后一个价格区间数字str
        price_limit_num = ''.join(re.findall(r'\d', price_limit_name))
        price_limit_num = int(price_limit_num)
        # 分类过滤参数序列化转str
        filter_param = {price_property_key: price_limit_key}
        j_str = json.dumps(filter_param)
        filter_str = str(j_str)
        # 选中价格区间过滤结果
        filter_result = setup[3].all_product_tab(ec_mkpl_header.get("addr_header"), setup[1], setup[2], filters=filter_str)
        # 返回商品价格
        products_price_lst = jmespath(filter_result, 'object.products[*].price')
        # 返回商品价格的最小值
        min_result_price = min(products_price_lst)
        # 断言结果商品的最小价格>= 筛选价格
        assert min_result_price >= price_limit_num

    @weeeTest.mark.list('product', 'Regression', )
    def test_seller_search_keyword(self, ec_mkpl_header, setup):
        """
        商家关键词搜索 title包含关键词得商品展示出来
        """

        # 取第一个商品的第一个词作为关键词 sign暂写死
        keyword = setup[0]["object"]["products"][0]["name"].split(" ")[0]
        # print(">>>>>>keyword", keyword)
        result = MkplSearch().seller_search_keyword(ec_mkpl_header.get("addr_header"), setup[1], setup[2], lang='en', zipcode=ec_mkpl_header.get('zipcode'),
                                                    filter_key_word=keyword, sign='signautotest')
        # 搜索结果商品title
        result_title_lst = jmespath(result, "object.products[*].name")
        # 包含搜索词的商品名称
        contain_result = [i for i in result_title_lst if i.find(keyword) >= 0]
        print(">>>>>>contain_result", contain_result)
        # 断言匹配结果不为空
        assert contain_result, f"contain_result={contain_result}, result_title_lst={result_title_lst}"


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    weeeTest.main()
