"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_pc_combine_order.py
@Description    :  
@CreateTime     :  2023/11/4 14:32
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/11/4 14:32
"""
import weeeTest
from weeeTest import jmespath
import json
from test_dir.api.ec.ec_item.search_v3.search_by_catalogue_content import SearchByCatalogueContent
from test_dir.api.ec.ec_so.address.apply_user_address_information import ApplyUserAddressInformation
from test_dir.api.ec.ec_so.address.query_user_address_list import QueryUserAddressList
from test_dir.api.ec.ec_so.order.checkout import Checkout
from test_dir.api.ec.ec_so.order.prepare_checkout import PrepareCheckout
from test_dir.api.ec.ec_so.preorder.items_select import ItemsSelect
from test_dir.api.ec.ec_so.preorder.payment_category import PaymentCategory
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder


class TestPcCombineOrder(weeeTest.TestCase):
    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    # @weeeTest.mark.list('Regression',  'tb1')
    def test_pc_combine_order(self, *args, ec_zhuli_header):
        """订单-下pc-combine一起结算订单"""
        # 20250303运行错误，需要修复
        # 结算暂时不迁到线上
        # headers = Header().login_header("<EMAIL>", "1234qwer")
        # 获取用户的porder
        SetUserPorder().set_user_porder(headers=ec_zhuli_header)
        # 获取用户的preorder
        print("获取用户的preorder", QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_zhuli_header))
        assert self.response["result"] is True
        porder = self.response["object"]
        sales_org_id = porder["sales_org_id"]
        deal_date = porder["delivery_pickup_date"]
        delivery_date = porder["delivery_pickup_date"]
        zipcode = porder["zipcode"]
        preorder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_zhuli_header)
        # print("pre", preorder["object"]["delivery_date"])
        # 通过分类接口获取生鲜商品
        normal = SearchByCatalogueContent().search_by_catalogue_content(headers=ec_zhuli_header, zipcode=zipcode,
                                                                        date=deal_date, filter_sub_category="care",
                                                                        filters=json.dumps({"delivery": True}))

        itmes = jmespath(normal, "object.contents")
        print(itmes)
        product_list = []
        for item in itmes:
            # 通过分类获取type不是carousel并且商品状态不是change_other_day的商品
            if item["type"] != "carousel" and item["data"]["sold_status"] != "change_other_day":
                product_list.append(item["data"]["id"])
        print("product_list", product_list)
        if not product_list:
            print("没有可售的生鲜商品")
            UpdatePreOrderLine().porder_items_v3(headers=ec_zhuli_header, product_id=args[0]["cart"]["product_id"],
                                                 quantity=args[0]["cart"]["quantity"])
        else:
            # 加购水果分类返回的第一个有效商品
            UpdatePreOrderLine().porder_items_v3(headers=ec_zhuli_header, product_id=product_list[0],
                                                 quantity=args[0]["cart"]["quantity"])
        # try:
        #     normal["object"]["updateItems"]
        # except Exception as e:
        #     log.info("生鲜加购失败: " + str(e))


        # 通过分类接口获取特价精选的pantry商品
        pantry = SearchByCatalogueContent().search_by_catalogue_content(headers=ec_zhuli_header, zipcode=zipcode,
                                                                        date=deal_date, filter_sub_category=
                                                                        "care",
                                                                        filters=json.dumps({"pantry": True}))
        itmes = jmespath(pantry, "object.contents")
        product_list = []
        for item in itmes:
            # 通过分类获取type不是carousel并且商品状态不是change_other_day的商品
            if item["type"] == "product" and item["data"]["sold_status"] != "change_other_day" or item["data"][
                "sold_status"] != "unavailable":
                product_list.append(item["data"]["id"])
        print("product_list", product_list)
        # 加购pantry商品如果通过分类获取不到pantry商品就固定加购配置表里的商品
        if pantry["object"]["total_count"] == 0:
            UpdatePreOrderLine().porder_items_v3(headers=ec_zhuli_header,
                                                 product_id=args[0]["cart"]["pantry_productid"],
                                                 quantity=args[0]["cart"]["quantity"])
        else:
            # 加购pantry分类的第一个商品
            # pantry_id = pantry["object"]["contents"][1]["data"]["id"]
            # 通过json配置写死酒商品加购到购物车
            UpdatePreOrderLine().porder_items_v3(headers=ec_zhuli_header, product_id=product_list[0],
                                                 quantity=args[0]["cart"]["quantity"])

        # 通过分类接口获取酒分类的商品
        alcohol = SearchByCatalogueContent().search_by_catalogue_content(headers=ec_zhuli_header, zipcode=zipcode,
                                                                         date=deal_date, filter_sub_category=
                                                                         "alcohol", sort="price_asc")["object"]
        # 如果通过分类获取不到酒商品就固定加购配置表里的商品
        if alcohol["contents"] is None:
            UpdatePreOrderLine().porder_items_v3(headers=ec_zhuli_header,
                                                 product_id=args[0]["cart"]["alcohol_productid"],
                                                 quantity=args[0]["cart"]["quantity"])
        else:
            # 加购酒分类的第一个商品
            alcohol_productid = alcohol["contents"][0]["data"]["id"]
            # 通过json配置写死酒商品加购到购物车
            UpdatePreOrderLine().porder_items_v3(headers=ec_zhuli_header, product_id=alcohol_productid,
                                                 quantity=args[0]["cart"]["quantity"])
        # 获取购物车商品
        select = ItemsSelect().items_select(headers=ec_zhuli_header, cart_domain=args[0]["cart"]["cart_domain"],
                                            selected_product_keys=[])
        # product_id = select["object"]["sections"][0]["items"][0]["product_id"]
        # 循环遍历jason文件多个下标的商品id
        product_lst = []
        for cart in jmespath(select, 'object.sections'):
            # print('cart:', cart['cart_id'])
            for item in cart['items']:
                # print("product list append", item['product_id'])
                product_lst.append(item['product_id'])
        print('product_lst', product_lst)
        # 勾选购物车
        ItemsSelect().items_select(headers=ec_zhuli_header, cart_domain=args[0]["cart"]["cart_domain"],
                                   selected_product_keys=product_lst)
        # 获取地址列表数据
        address_list = QueryUserAddressList().address_list(headers=ec_zhuli_header)['object']
        result = [(x["id"]) for x in address_list]
        # print("ddd",result)
        address_id = result[0]
        # ApplyUserAddressInformation().address_apply_v2(headers=zhuli_header, address_id=address_id)
        # 应用地址信息
        ApplyUserAddressInformation().address_apply_v2(headers=ec_zhuli_header, address_id=address_id)
        pre = PrepareCheckout().prepare_checkout_v2(headers=ec_zhuli_header, cart_domain=args[0]["cart"]["cart_domain"])
        checkoutamount = pre["object"]["fee_info"]["final_amount"]
        checkout_pre_id = pre["object"]["checkout_pre_id"]
        # 获取第一个profile_id值
        # profile_id = PaymentCategory().braintree_profiles(headers=zhuli_header)["object"][0]['profile_id']
        # 切换积分支付
        PaymentCategory().payment_category(headers=ec_zhuli_header,
                                           payment_category=args[0]["order"]["payment_category"], points=True)
        # 结算
        print("PC combine订单号",
              Checkout().checkout_v3(headers=ec_zhuli_header, cart_domain=args[0]["cart"]["cart_domain"],
                                     checkout_pre_id=checkout_pre_id,
                                     checkoutAmount=checkoutamount)[
                  "object"]["order_ids"])


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
