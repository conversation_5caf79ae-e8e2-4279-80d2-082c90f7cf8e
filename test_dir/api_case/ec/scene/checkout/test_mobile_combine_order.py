"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_pantry_order.py
@Description    :  
@CreateTime     :  2023/9/13 13:04
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/9/13 13:04
"""
import json

import weeeTest
from weeeTest import jmespath

from test_dir.api.ec.ec_item.search_v3.search_by_catalogue_content import SearchByCatalogueContent
from test_dir.api.ec.ec_so.address.apply_user_address_information import ApplyUserAddressInformation
from test_dir.api.ec.ec_so.order.checkout import Checkout
from test_dir.api.ec.ec_so.order.prepare_checkout import PrepareCheckout
from test_dir.api.ec.ec_so.order.upshell import Upshell
from test_dir.api.ec.ec_so.preorder.payment_category import PaymentCategory
from test_dir.api.ec.ec_so.preorder.query_preorder import QueryPreOrder
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder


class TestMobileCombineOrder(weeeTest.TestCase):
    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.list('mobile_combine', 'Regression-restore',  'tb1')
    def test_mobile_combine_order(self, *args, ec_zhuli_header):
        """订单-一起结算生鲜+pantry订单"""
        SetUserPorder().set_user_new_porder(headers=ec_zhuli_header, zipcode="98011", lang="en")
        # 获取用户的preorder
        QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_zhuli_header)
        assert self.response["result"] is True
        porder = self.response["object"]
        sales_org_id = porder["sales_org_id"]
        deal_date = porder["delivery_pickup_date"]
        delivery_date = porder["delivery_pickup_date"]
        zipcode = porder["zipcode"]
        # 查询购物车v51
        QueryPreOrder().query_preorder_v5(headers=ec_zhuli_header, cart_domain=args[0]["cart"]["cart_domain"])
        # 通过分类接口获取生鲜商品
        normal = SearchByCatalogueContent().search_by_catalogue_content(headers=ec_zhuli_header, zipcode=zipcode,
                                                                        date=deal_date, filter_sub_category="care",
                                                                        filters=json.dumps({"delivery": True}))

        itmes = jmespath(normal, "object.contents")
        print(itmes)
        product_list = []
        for item in itmes:
            # 通过分类获取type不是carousel并且商品状态不是change_other_day的商品
            if item["type"] != "carousel" and item["data"]["sold_status"] != "change_other_day":
                product_list.append(item["data"]["id"])
        print("product_list", product_list)
        if not product_list:
            print("没有可售的生鲜商品")
            UpdatePreOrderLine().porder_items_v3(headers=ec_zhuli_header, product_id=args[0]["cart"]["product_id"],
                                                 quantity=args[0]["cart"]["quantity"], date=deal_date)
        else:
            # 加购水果分类返回的第一个有效商品
            UpdatePreOrderLine().porder_items_v3(headers=ec_zhuli_header, product_id=product_list[0],
                                                 quantity=args[0]["cart"]["quantity"], date=deal_date)
        # 通过分类接口获取特价精选的pantry商品
        pantry = SearchByCatalogueContent().search_by_catalogue_content(headers=ec_zhuli_header, zipcode=zipcode,
                                                                        date=deal_date, filter_sub_category=
                                                                        "care",
                                                                        filters=json.dumps({"pantry": True}))
        itmes = jmespath(pantry, "object.contents")
        product_list = []
        for item in itmes:
            # 通过分类获取type不是carousel并且商品状态不是change_other_day的商品
            if item["type"] == "product" and item["data"]["sold_status"] != "change_other_day" or item["data"][
                "sold_status"] != "unavailable":
                product_list.append(item["data"]["id"])
        print("product_list", product_list)
        # 加购pantry商品如果通过分类获取不到pantry商品就固定加购配置表里的商品
        if pantry["object"]["total_count"] == 0:
            UpdatePreOrderLine().porder_items_v3(headers=ec_zhuli_header, product_id=args[0]["cart"]["pantry_productid"],
                                                 quantity=args[0]["cart"]["quantity"])
        else:
            # 加购pantry分类的第一个商品
            # pantry_id = pantry["object"]["contents"][1]["data"]["id"]
            # 通过json配置写死酒商品加购到购物车
            UpdatePreOrderLine().porder_items_v3(headers=ec_zhuli_header, product_id=product_list[0],
                                                 quantity=args[0]["cart"]["quantity"])


        # 通过分类接口获取酒分类的商品
        alcohol = SearchByCatalogueContent().search_by_catalogue_content(headers=ec_zhuli_header, zipcode=zipcode,
                                                                         date=deal_date, filter_sub_category=
                                                                         "alcohol", sort="price_asc")["object"]
        # 如果通过分类获取不到酒商品就固定加购配置表里的商品
        if alcohol["contents"] is None:
            UpdatePreOrderLine().porder_items_v3(headers=ec_zhuli_header, product_id=args[0]["cart"]["alcohol_productid"],
                                                 quantity=args[0]["cart"]["quantity"])
        else:
            # 加购酒分类的第一个商品
            alcohol_productid = alcohol["contents"][0]["data"]["id"]
            # 通过json配置写死酒商品加购到购物车
            UpdatePreOrderLine().porder_items_v3(headers=ec_zhuli_header, product_id=alcohol_productid,
                                                 quantity=args[0]["cart"]["quantity"])
        # 通过分类接口获取mkpl分类的商品
        # mkpl = SearchByCatalogueContent().search_by_catalogue_content(headers=zhuli_header, zipcode=zipcode,
        #                                                               date=deal_date, filter_sub_category=
        #                                                               args[0]["category"]["filter_sub_category"][2],
        #                                                               sort="recommend",
        #                                                               filters=json.dumps(
        #                                                                   args[0]["category"]["filters_global"])
        #                                                               )

        # 如果通过分类获取不到mkpl商品就固定加购配置表里的商品
        # if mkpl:
        #     UpdatePreOrderLine().porder_items_v3(headers=zhuli_header, product_id=args[0]["cart"]["mkpl_product_id"],
        #                                          quantity=args[0]["cart"]["quantity"])
        # else:
        #     # 加购mkpl分类的第一个商品
        #     mkpl_productid = mkpl["object"]["contents"][0]["data"]["id"]
        #     # 通过json配置写死酒商品加购到购物车
        #     UpdatePreOrderLine().porder_items_v3(headers=zhuli_header, product_id=mkpl_productid,
        #                                          quantity=args[0]["cart"]["quantity"])
        # 获取upsell的数据
        upsell = Upshell().upsell_v2(headers=ec_zhuli_header, cart_domain=args[0]["cart"]["cart_domain"])
        print("upsell", upsell["object"]["upsell_list"])
        if upsell["object"]["upsell_list"] != []:
            upsell_product = upsell["object"]["upsell_list"][0]["items"][0]["id"]
            # 加购upsell第一个组件的第一个商品
            UpdatePreOrderLine().porder_items_cart_v2(headers=ec_zhuli_header, product_id=upsell_product,
                                                      quantity=args[0]["cart"]["quantity"])
        else:
            ApplyUserAddressInformation().address_apply_v2(headers=ec_zhuli_header, address_id=6198)
            pre = PrepareCheckout().prepare_checkout_v2(headers=ec_zhuli_header,
                                                        cart_domain=args[0]["cart"]["cart_domain"],
                                                        index=args[0]["order"]["index"], tip=args[0]["order"]["tip"])
            checkoutamount = pre["object"]["fee_info"]["final_amount"]
            checkout_pre_id = pre["object"]["checkout_pre_id"]
            # """获取第一个profile_id值"""["object"][0]['profile_id']
            # profile_id = PaymentCategory().braintree_profiles(headers=headers)
            # print(profile_id)
            # 使用积分支付
            PaymentCategory().payment_category(headers=ec_zhuli_header,
                                               payment_category=args[0]["order"]["payment_category"],
                                               points=True)
            # 结算
            print("一起结算订单号",
                  Checkout().checkout_v3(headers=ec_zhuli_header, cart_domain=args[0]["cart"]["cart_domain"],
                                         checkout_pre_id=checkout_pre_id,
                                         checkoutAmount=checkoutamount)[
                      "object"]["order_ids"])

            # if order_list["message_id"] == 'SO90113':
            #     print("当前地址不能接收邮寄包裹。如需继续请选择")
            # else:
            # 获取首页的top message数据
            # print("获取首页合单提醒文案",
            #       MessageHomeHeader().message_home_header(headers=headers, version="v2", lang="zh",
            #                                               date=preorder["deal_date"])["object"][0]["message"]["short_message"])


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
