"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_grocery_to_cart.py
@Description    :  
@CreateTime     :  2023/11/20 16:52
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/11/20 16:52
"""
import pytest
import weeeTest

from test_dir.api.ec.ec_item.search_v3.search_by_catalogue_content import SearchByCatalogueContent
from test_dir.api.ec.ec_so.order.cancel_order import CancelOrder
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine
from test_dir.api_case.ec.common.comm_check_products_with_cart import CommCheckProductsWithCart
from test_dir.api_case.ec.common.common_payment import CommonPayment
from test_dir.api_case.ec.common.remove_all_products_in_cart import RemoveAllProductsInCart


class TestCheckOutAllMethod(weeeTest.TestCase):
    @weeeTest.mark.list('Regression', 'Transaction', 'product')
    @pytest.mark.parametrize("payment_category", ["P", "V", "M", "I", "K", "H"])
    def test_grocery_check_out_all_payment_method(self, ec_zhuli_header, payment_category):
        """
        结算页-使用不同支付方式结算验证
        """
        # D 信用卡 strip，老APP使用
        # B 表示braintree信用卡，Q也是，目前废弃了
        # P 表示 PayPal
        # V 表示 venmo
        # M 表示ETB
        # I 表示 微信，F、C 也是微信，目前废弃了
        # K 表示支付宝, A 及E 目前不用跑了
        # H 表示cash app
        # L 表示apple pay，T 也是目前废弃了

        # 获取用户的preorder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_zhuli_header)["object"]
        deal_date = porder["delivery_pickup_date"]
        zipcode = porder["zipcode"]
        # 清空购物车
        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=ec_zhuli_header)
        # 查询生鲜商品
        normal = SearchByCatalogueContent().search_by_catalogue_content(
            headers=ec_zhuli_header,
            zipcode=zipcode,
            date=deal_date,
            filter_sub_category="fruits"
        )
        assert normal["object"]["contents"], f"当前环境下没有生鲜商品,filter_sub_category=fruits，请检查，为{normal['object']}"
        available_grocery_products = [[item['data']['id'], item['data']['sales_min_order_quantity']] for item in
                                      normal["object"]["contents"] if
                                      item['type'] != 'carousel' and item["data"]["sold_status"] == "available"]

        # 加购生鲜商品
        for index, item in enumerate(available_grocery_products):
            porder_items = UpdatePreOrderLine().porder_items_v3(
                headers=ec_zhuli_header,
                product_id=item[0],
                quantity=item[1]
            )
            assert porder_items["result"] is True and len(
                porder_items["object"]["updateItems"]) > 0, f"product id is {item[0]}, response is {porder_items}"
            CommCheckProductsWithCart().check_product_exists_in_cart(
                headers=ec_zhuli_header,
                cart_domain="grocery",
                product_id=item[0]
            )

            if index == 10:
                break

        # # 加购Upsell商品
        # CommonBuy.buy_upsell_product(ec_login_header)

        # 使用参数化支付方式，产生一个待支付的订单
        order_info = CommonPayment.pay_with_all_method(headers=ec_zhuli_header,
                                                       payment_category=payment_category, is_point=False)
        order_ids = order_info["order_ids"]
        assert order_ids, f"使用paypal支付，未产生订单号，接口返回结果为：{order_ids}"
        # 取消待支付订单
        cancel_order = CancelOrder().cancel_unpaid_order_new(headers=ec_zhuli_header, order_ids=order_ids)
        assert cancel_order['object'] == 'success'

