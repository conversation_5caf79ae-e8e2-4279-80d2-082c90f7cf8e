# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_item.py
@Description    :
@CreateTime     :  2023/5/16 16:53
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 16:53
"""
import json

import pytest
import weeeTest

from test_dir.api.ec.ec_item.search_v3.search_by_catalogue_content import SearchByCatalogueContent
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api_case.ec.common.comm_check_products_with_cart import CommCheckProductsWithCart
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction
from test_dir.api_case.ec.common.common_check import CommonCheck
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder


class TestExploreGrocerySpecialCategory(weeeTest.TestCase):
    @pytest.fixture(scope='class', autouse=True)
    def setup(self, ec_login_header):
        # 切换测试销售组织地区
        SetUserPorder().set_user_zipcode(headers=ec_login_header, zipcode="98011")
        yield
        # 切回98011组织地区
        SetUserPorder().set_user_zipcode(headers=ec_login_header, zipcode="98011")

    # @weeeTest.mark.list('explore_trending_category', 'Regression', 'Smoke', 'Transaction')
    # def test_explore_trending_category(self, ec_login_header):
    #     """ 分类页-人气热卖验证流程"""
    #     porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
    #     # 1.获取trending分类数据
    #     trending_content = self.get_explore_special_category(
    #         filter_sub_category="trending",
    #         date=porder["delivery_pickup_date"],
    #         zipcode=porder["zipcode"],
    #         headers=ec_login_header
    #     )
    #
    #     products = trending_content["object"]["contents"]
    #
    #     # 2.product属性断言
    #     for index, product in enumerate(products):
    #         if product["type"] == "product":
    #             CommonCheck().check_product_info(product=product["data"], category_type="trending", source="explorepage",
    #                                              filters="others", headers=ec_login_header)
    #
    #     # 点击商品卡片,跳转
    #     CommCheckFunction().comm_check_pdp_link(products[-1]['data']["id"], products[-1]['data']['view_link'], headers=ec_login_header)
    #
    #     # 3.加购特殊分类下的生鲜商品并断言验证
    #     CommCheckProductsWithCart().products_data_add_to_cart(ec_login_header, products,
    #                                                           porder["delivery_pickup_date"],
    #                                                           "mweb_home-cm_item_trending-null")
    #
    # @weeeTest.mark.list('explore_sale_category', 'Regression', 'Smoke', 'Transaction')
    # def test_explore_sale_category(self, ec_login_header):
    #     """ 分类页-特价精选验证流程"""
    #     # 获取用户的porder
    #     porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
    #
    #     # 1.获取sales分类数据
    #     sale_content = self.get_explore_special_category(
    #         filter_sub_category="sale",
    #         date=porder["delivery_pickup_date"],
    #         zipcode=porder["zipcode"],
    #         headers=ec_login_header
    #     )
    #
    #     products = sale_content["object"]["contents"]
    #
    #     # 2.product属性断言
    #     # CommonCheck().check_products_data_info(category_type="sale", source="explorepage",
    #     #                                        products=sale_content["object"]["contents"])
    #     for index, product in enumerate(products):
    #         if product["type"] == "product":
    #             CommonCheck().check_product_info(product=product["data"], category_type="sale", source="explorepage",
    #                                              filters="others", headers=ec_login_header)
    #     # 点击商品卡片,跳转
    #     CommCheckFunction().comm_check_pdp_link(products[-1]['data']["id"], products[-1]['data']['view_link'], headers=ec_login_header)
    #
    #     # 3. 加购sales分类产品并验证
    #     CommCheckProductsWithCart().products_data_add_to_cart(ec_login_header, products,
    #                                                           porder["delivery_pickup_date"],
    #                                                           "mweb_home-cm_item_sale-null")
    #
    # @weeeTest.mark.list('explore_new_category', 'Smoke', 'Regression', 'Transaction')
    # def test_explore_new_category(self, ec_login_header):
    #     """ 分类页-新品上架验证流程"""
    #     # 获取用户的porder
    #     porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
    #     # 1. 获取new分类数据
    #     new_content = self.get_explore_special_category(
    #         filter_sub_category="new",
    #         date=porder["delivery_pickup_date"],
    #         zipcode=porder["zipcode"], headers=ec_login_header
    #     )
    #     products = new_content["object"].get("contents")
    #
    #     # 检查商品基础信息
    #     # CommonCheck().check_products_data_info(category_type="new", source="explorepage",
    #     #                                        products=new_content["object"]["contents"])
    #     for index, product in enumerate(products):
    #         if product["type"] == "product":
    #             CommonCheck().check_product_info(product=product["data"], category_type="new", source="explorepage",
    #                                              filters="others", headers=ec_login_header)
    #
    #     # 点击商品卡片,跳转
    #     CommCheckFunction().comm_check_pdp_link(products[-1]['data']["id"], products[-1]['data']['view_link'], headers=ec_login_header)
    #
    #     # 3. 加购new分类商品并验证
    #     CommCheckProductsWithCart().products_data_add_to_cart(ec_login_header, products,
    #                                                           porder["delivery_pickup_date"],
    #                                                           "mweb_home-cm_item_new-null")
    #


    @weeeTest.mark.list('explore_special_category', 'Regression', 'Smoke', 'Transaction')
    @pytest.mark.parametrize("category", ["trending","sale","new"])
    def test_explore_special_category(self, ec_login_header, category):
        """ 分类页-特殊分类(人气热卖/特价精选/新品上架)参数化验证流程"""
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]

        # 1.获取特殊分类数据
        special_content = self.get_explore_special_category(
            filter_sub_category=category,
            date=porder["delivery_pickup_date"],
            zipcode=porder["zipcode"],
            headers=ec_login_header
        )
        assert special_content.get("object", {}).get(
                   "total_count", 0) >= 5, f"这个{category}分类返回异常为：{special_content.get('object', {})}"
        assert special_content.get("object", {}).get("search_catalogue_num") == category,f"这个{category}分类返回异常为：{special_content.get('object', {})}"
        products = special_content.get("object", {}).get("contents", [])

        # 2.product属性断言
        for index, product in enumerate(products):
            if product.get("type") == "product":
                CommonCheck().check_product_info(product=product.get("data", {}), category_type=category, source="explorepage",
                                             filters="others", headers=ec_login_header)

        # 点击商品卡片,跳转
        if products and len(products) > 0:
            CommCheckFunction().comm_check_pdp_link(products[-1].get('data', {}).get("id"), products[-1].get('data', {}).get('view_link'), headers=ec_login_header)

        # 3.加购特殊分类下的商品并断言验证
        CommCheckProductsWithCart().products_data_add_to_cart(ec_login_header, products,
                                                          porder["delivery_pickup_date"],
                                                          f"mweb_home-cm_item_{category}-null")

    @weeeTest.mark.list('explore_new_category', 'Smoke', 'Regression', 'Transaction')
    @pytest.mark.parametrize("category", ["trending","sale","new"])
    def test_explore_new_category_filter_categories(self, ec_login_header, category):
        """ 分类页-新品上架filter子分类验证流程"""
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 1. 获取分类数据
        category_content = self.get_explore_special_category(
            filter_sub_category=category,
            date=porder["delivery_pickup_date"],
            zipcode=porder["zipcode"], headers=ec_login_header
        )
        categories = category_content.get("object", {}).get("categories", [])
        assert categories, f"这个{category}分类数据异常"

        for item in categories:
            category_content = self.get_explore_special_category(
                headers=ec_login_header,date=porder["delivery_pickup_date"],
                zipcode=porder["zipcode"],
                filter_sub_category=category,
                filters=json.dumps(
                    {"catalogue_num": item.get("catalogue_num")}
                )
            )
            assert category_content.get("object", {}).get("total_count", 0) > 0, f"这个{category}的子分类{item.get('catalogue_num')}分类数据异常"

    @weeeTest.mark.list('B2B', 'Regression', 'Smoke', 'Transaction')
    @pytest.mark.parametrize("category", ["trending","new"])
    def test_b2b_explore_special_category(self, ec_login_header, category):
        """ 分类页-特殊分类(人气热卖/特价精选/新品上架)参数化验证流程"""
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]

        # 1.获取特殊分类数据
        special_content = self.get_explore_special_category(
            filter_sub_category=category,
            date=porder["delivery_pickup_date"],
            zipcode=porder["zipcode"],
            headers=ec_login_header
        )
        assert special_content.get("object", {}).get(
                   "total_count", 0) >= 5, f"这个{category}分类返回异常为：{special_content.get('object', {})}"
        assert special_content.get("object", {}).get("search_catalogue_num") == category,f"这个{category}分类返回异常为：{special_content.get('object', {})}"
        products = special_content.get("object", {}).get("contents", [])

        # 2.product属性断言
        for index, product in enumerate(products):
            if product.get("type") == "product":
                CommonCheck().check_product_info(product=product.get("data", {}), category_type=category, source="explorepage",
                                             filters="others", headers=ec_login_header)

        # 点击商品卡片,跳转
        if products and len(products) > 0:
            CommCheckFunction().comm_check_pdp_link(products[-1].get('data', {}).get("id"), products[-1].get('data', {}).get('view_link'), headers=ec_login_header)

        # 3.加购特殊分类下的商品并断言验证
        CommCheckProductsWithCart().products_data_add_to_cart(ec_login_header, products,
                                                          porder["delivery_pickup_date"],
                                                          f"mweb_home-cm_item_{category}-null")

    @weeeTest.mark.list('B2B', 'Smoke', 'Regression', 'Transaction')
    @pytest.mark.parametrize("category", ["trending","new"])
    def test_b2b_explore_new_category_filter_categories(self, ec_login_header, category):
        """ 分类页-新品上架filter子分类验证流程"""
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 1. 获取分类数据
        category_content = self.get_explore_special_category(
            filter_sub_category=category,
            date=porder["delivery_pickup_date"],
            zipcode=porder["zipcode"], headers=ec_login_header
        )
        categories = category_content.get("object", {}).get("categories", [])
        assert categories, f"这个{category}分类数据异常"

        for item in categories:
            category_content = self.get_explore_special_category(
                headers=ec_login_header,date=porder["delivery_pickup_date"],
                zipcode=porder["zipcode"],
                filter_sub_category=category,
                filters=json.dumps(
                    {"catalogue_num": item.get("catalogue_num")}
                )
            )
            assert category_content.get("object", {}).get("total_count", 0) > 0, f"这个{category}的子分类{item.get('catalogue_num')}分类数据异常"

    def get_explore_special_category(self, headers,date, zipcode, filter_sub_category, filters=None):

        explore_special_category = SearchByCatalogueContent().search_by_catalogue_content(
            headers=headers,date=date, zipcode=zipcode,
            filter_sub_category=filter_sub_category,
            filters=filters
        )
        return explore_special_category

    # def trending_product_assertion(self, products: list | Any):
    #     for product in products:
    #         assert "weeecdn" in product['data']['img']
    #         assert product['data']['name'] is not None,f"商品id：{product['id']}，商品名是：{product['data']['name']}"
    #         assert product['data']['price'] is not None, f"商品id：{product['id']}，商品价格是：{product['data']['price']}"
    #         # assert product['data']['sold_status'] == "available"
    #         assert str(product['data']["id"]) in product['data']['slug'], "这个链接返回的不是这个产品的链接，请确认~"
    #         # 断言如果是mkpl商品，商家信息不为空
    #         if product['data']['biz_type'] == 'seller':
    #             assert product['data']['vender_id'] is not None
    #             assert product['data']['vender_info_view'] != {}
    #     # 点击商品卡片,跳转
    #     CommCheckFunction().comm_check_pdp_link(products[0]['data']["id"], products[0]['data']['view_link'])

    # def sale_product_assertion(self, products: list | Any):
    #     for product in products:
    #
    #         if not product['data']["sponsored_text"]:
    #             assert product['data']['img'] is not None
    #             assert product['data'][
    #                        'price'] is not None, f"商品id：{product['id']}，商品价格是：{product['data']['price']}"
    #             assert product['data']['last_week_sold_count'] is not None
    #             assert product['data']['sold_count'] is not None
    #             # 如果这个商品没有折扣，且不是广告商品，那么标签返回 每日低价
    #             if product['data']['base_price'] is None and product['data']["sponsored_text"] is None:
    #                 if len(product['data']['label_list']) > 0:
    #                     assert product['data']['label_list'][0]['label_key'] == 'EDLP_label' or product['data']['label_list'][0]['label_key'] == 'off'
    #             # 如果这个商品有折扣，且不是广告商品，那么标签返回 off
    #             if product['data']['base_price'] is not None and product['data']["sponsored_text"] is None:
    #                 assert product['data']['base_price'] > product['data'][
    #                     'price'], f"商品id：{product['data']['id']}，商品base_price是：{product['data']['base_price']}"
    #                 assert product['data']['label_list'][0]['label_key'] == 'off'
    #             # 断言如果是mkpl商品，商家信息不为空
    #             if product['data']['biz_type'] == 'seller':
    #                 assert product['data']['vender_id'] is not None
    #                 assert product['data']['vender_info_view'] != {}
    #     # 点击商品卡片,跳转
    #     CommCheckFunction().comm_check_pdp_link(products[0]['data']["id"], products[0]['data']['view_link'])

    # def new_product_assertion(self, products: list | Any):
    #     for product in products:
    #         # 如果这个商品不是广告商品，那么标签返回 new
    #         if product['data']["sponsored_text"] is None:
    #             assert any(item['label_key'] == 'new' for item in
    #                        product["data"]["label_list"]), f"'new' not found under any 'label_key'"
    #         # 断言如果是mkpl商品，商家信息不为空
    #         if product['data']['biz_type'] == 'seller':
    #             assert product['data']['vender_id'] is not None
    #             assert product['data']['vender_info_view'] != {}
    #     # 点击商品卡片,跳转
    #     CommCheckFunction().comm_check_pdp_link(products[0]['data']["id"], products[0]['data']['view_link'])
    #
