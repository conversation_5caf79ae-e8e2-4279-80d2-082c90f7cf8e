# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
from typing import Any

import weeeTest

from test_dir.api.ec.ec_item.category.api_catalogues import ApiCatalogues
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction


class TestHomeGroceryNormalCategory(weeeTest.TestCase):

    @weeeTest.mark.list('Regression', 'Smoke', 'Transaction')
    def test_home_grocery_normal_category(self, ec_login_header):
        """ 分类-首页特普通类验证流程"""
        # 1.获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 2.获取首页分类,不包括特殊分类
        catalogue_home = ApiCatalogues().catalogue_home(headers=ec_login_header,
                                                        date=porder["delivery_pickup_date"],
                                                        zipcode=porder["zipcode"])
        category_lists = catalogue_home["object"]["category_list"]
        assert len(category_lists) > 0, f"首页分类{category_lists}返回没有数据，请确认"
        assert catalogue_home["object"]["see_all_img_url"] is not None
        # 3.首页分类断言
        self.home_catalogue_assertion(category_lists=category_lists, headers=ec_login_header)
    @weeeTest.mark.list('B2B','Regression', 'Smoke', 'Transaction')
    def test_b2b_home_grocery_normal_category(self, ec_login_header):
        """ 分类-首页特普通类验证流程"""
        # 1.获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 2.获取首页分类,不包括特殊分类
        catalogue_home = ApiCatalogues().catalogue_home(headers=ec_login_header,
                                                        date=porder["delivery_pickup_date"],
                                                        zipcode=porder["zipcode"])
        category_lists = catalogue_home["object"]["category_list"]
        assert len(category_lists) > 0, f"首页分类{category_lists}返回没有数据，请确认"
        assert catalogue_home["object"]["see_all_img_url"] is not None
        # 3.首页分类断言
        self.home_catalogue_assertion(category_lists=category_lists, headers=ec_login_header)

    def home_catalogue_assertion(self, category_lists: list | Any, headers):
        assert any(item["num"] == "global" for item in category_lists), f'"global+" not found under homepage'
        for category_list in category_lists:
            if category_list["type"] == 4:
                # 表示不是普通分类,通常是节日活动分类
                # 0114去掉断言，global的category_label页面上测试就是为None
                # assert category_list['category_label']['label_key'] is not None
                assert category_list['name'] is not None, f"首页分类{category_lists}返回没有数据，请确认"
                assert category_list['img_url'] is not None, f"首页分类{category_lists}返回没有数据，请确认"
                assert category_list['label'] is not None, f"首页分类{category_lists}返回没有数据，请确认"
                assert category_list['url'] is not None, f"首页分类{category_lists}返回没有数据，请确认"
                # 点击这个分类
                view_link = category_list['url']
                # 点击跳转
                CommCheckFunction().comm_check_link(view_link, headers=headers)

            elif category_list["type"] == 0:
                # 表示是普通分类

                if category_list['num'] == "global":
                    # 点击这个分类
                    view_link = category_list['url']
                    # todo 暂时去掉
                    # assert view_link.endswith("/mkpl/global"), f"url is not /mkpl/global"
                    # 点击跳转
                    CommCheckFunction().comm_check_link(view_link, headers=headers)

                else:
                    # 点击这个分类
                    view_link = category_list['url']
                    assert view_link.endswith("/category/" + category_list['num']),f"url is not /category/{category_list['num']}"
                    # 点击跳转
                    CommCheckFunction().comm_check_link(view_link, headers=headers)

if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
