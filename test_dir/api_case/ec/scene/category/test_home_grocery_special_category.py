# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_item.py
@Description    :
@CreateTime     :  2023/5/16 16:53
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 16:53
"""

import weeeTest

from test_dir.api.ec.ec_item.search_v3.search_by_catalogue import SearchByCatalogue
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api_case.ec.common.comm_check_products_with_cart import CommCheckProductsWithCart
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction
from test_dir.api_case.ec.common.common_check import CommonCheck


class TestHomeGrocerySpecialCategory(weeeTest.TestCase):
    @weeeTest.mark.list('home_trending_category', 'Regression', 'Smoke', 'Transaction')
    def test_home_trending_category(self, ec_login_header):
        """ 首页-人气热卖验证流程"""
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 1.获取trending分类数据
        trending_content = self.get_home_special_category_content("trending", "ds_item_trending", ec_login_header)
        products = trending_content["object"]["products"]

        # 2.product属性断言
        for index, product in enumerate(products):
            CommonCheck().check_product_info(product=product, headers=ec_login_header, category_type="trending", source="homepage", filters="others")


        # CommonCheck().check_products_info(category_type="trending", source="homepage",
        #                                   products=trending_content["object"]["products"])
        # 点击商品卡片,跳转
        CommCheckFunction().comm_check_pdp_link(products[0]["id"], products[0]['view_link'], headers=ec_login_header)

        # 3.加购特殊分类下的生鲜商品并断言验证
        available_products = [item for item in products if item['sold_status'] == 'available']
        unavailable_products = [item for item in products if item['sold_status'] != 'available']
        CommCheckProductsWithCart().products_add_to_cart(ec_login_header, available_products,
                                                         porder["delivery_pickup_date"],
                                                        "mweb_home-cm_item_trending-null")

    @weeeTest.mark.list('home_sale_category', 'Regression', 'Smoke', 'Transaction')
    def test_home_sale_category(self, empty_cart, ec_login_header):
        """ 首页-特价精选验证流程"""
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]

        # 1.获取sales分类数据
        sale_content = self.get_home_special_category_content("sale", "ds_item_sale", ec_login_header)
        products = sale_content["object"]["products"]
        # 2. 产品属性断言
        # self.sale_product_assertion(products)
        # 检查商品基础信息
        for index, product in enumerate(products):
            CommonCheck().check_product_info(product=product, category_type="sale", source="homepage", filters="others", headers=ec_login_header)

        # 点击商品卡片,跳转
        CommCheckFunction().comm_check_pdp_link(products[0]["id"], products[0]['view_link'], headers=ec_login_header)
        # 3. 加购sales分类产品并验证
        available_products = [item for item in products if item['sold_status'] == 'available']
        CommCheckProductsWithCart().products_add_to_cart(ec_login_header, available_products,
                                                         porder["delivery_pickup_date"], "mweb_home-cm_item_sale-null")

    @weeeTest.mark.list('home_new_category', 'Smoke', 'Regression', 'Transaction')
    def test_home_new_category(self, empty_cart, ec_login_header):
        """ 首页-新品上架验证流程"""
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 1. 获取new分类数据
        print("porder===>", porder)
        new_content = self.get_home_special_category_content("new", "ds_item_new", ec_login_header)
        products = new_content["object"]["products"]

        # 2. 产品属性断言
        # self.new_product_assertion(products)
        # 检查商品基础信息
        for index, product in enumerate(products):
            CommonCheck().check_product_info(product=product, category_type="new", source="homepage", filters="others", headers=ec_login_header)
        #
        # CommonCheck().check_products_info(category_type="new", source="homepage",
        #                                   products=new_content["object"]["products"])
        # 点击商品卡片,跳转
        CommCheckFunction().comm_check_pdp_link(products[0]["id"], products[0]['view_link'], headers=ec_login_header)

        # 3. 加购new分类商品并验证
        available_products = [item for item in products if item['sold_status'] == 'available']
        CommCheckProductsWithCart().products_add_to_cart(ec_login_header, available_products,
                                                         porder["delivery_pickup_date"], "mweb_home-cm_item_new-null")

    @weeeTest.mark.list('B2B', 'Regression', 'Smoke', 'Transaction')
    def test_b2b_home_trending_category(self, ec_login_header):
        """ 首页-人气热卖验证流程"""
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 1.获取trending分类数据
        trending_content = self.get_home_special_category_content("trending", "ds_item_trending", ec_login_header)
        products = trending_content["object"]["products"]

        # 2.product属性断言
        for index, product in enumerate(products):
            CommonCheck().check_product_info(product=product, headers=ec_login_header, category_type="trending", source="homepage", filters="others")


        # CommonCheck().check_products_info(category_type="trending", source="homepage",
        #                                   products=trending_content["object"]["products"])
        # 点击商品卡片,跳转
        CommCheckFunction().comm_check_pdp_link(products[0]["id"], products[0]['view_link'], headers=ec_login_header)

        # 3.加购特殊分类下的生鲜商品并断言验证
        available_products = [item for item in products if item['sold_status'] == 'available']
        unavailable_products = [item for item in products if item['sold_status'] != 'available']
        CommCheckProductsWithCart().products_add_to_cart(ec_login_header, available_products,
                                                         porder["delivery_pickup_date"],
                                                        "mweb_home-cm_item_trending-null")

    @weeeTest.mark.list('B2B', 'Smoke', 'Regression', 'Transaction')
    def test_b2b_home_new_category(self, empty_cart, ec_login_header):
        """ 首页-新品上架验证流程"""
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 1. 获取new分类数据
        print("porder===>", porder)
        new_content = self.get_home_special_category_content("new", "ds_item_new", ec_login_header)
        products = new_content["object"]["products"]

        # 2. 产品属性断言
        # self.new_product_assertion(products)
        # 检查商品基础信息
        for index, product in enumerate(products):
            CommonCheck().check_product_info(product=product, category_type="new", source="homepage", filters="others", headers=ec_login_header)
        #
        # CommonCheck().check_products_info(category_type="new", source="homepage",
        #                                   products=new_content["object"]["products"])
        # 点击商品卡片,跳转
        CommCheckFunction().comm_check_pdp_link(products[0]["id"], products[0]['view_link'], headers=ec_login_header)

        # 3. 加购new分类商品并验证
        available_products = [item for item in products if item['sold_status'] == 'available']
        CommCheckProductsWithCart().products_add_to_cart(ec_login_header, available_products,
                                                         porder["delivery_pickup_date"], "mweb_home-cm_item_new-null")

    def get_home_special_category_content(self, filter_sub_category, dataobject_key, header):
        home_special_category = SearchByCatalogue().search_by_catalogue(headers=header,
                                                                        filter_sub_category=filter_sub_category,
                                                                        dataobject_key=dataobject_key)
        assert home_special_category["object"][
                   "total_count"] > 5, f"这个{filter_sub_category}分类返回数据较少，请确认, trending_category.object为{home_special_category['object']}"
        assert home_special_category["object"][
                   "search_catalogue_num"] == filter_sub_category, f"trending_category.object.search_catalogue_num为{home_special_category['object']['search_catalogue_num']}"
        return home_special_category

        # 拉四次数据
        # for 循环
        # 打出log
