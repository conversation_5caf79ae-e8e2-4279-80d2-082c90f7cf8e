# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import json
import time
from typing import Any

import pytest
import weeeTest
from weeeTest import log

from test_data.ec.simple.common import Header
from test_dir.api.ec.ec_item.search_v3.search_by_catalogue_content import SearchByCatalogueContent
from test_dir.api.ec.ec_item.category.api_catalogues import ApiCatalogues
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api_case.ec.common.comm_check_products_with_cart import CommCheckProductsWithCart
from test_dir.api_case.ec.common.common_check import CommonCheck
from test_dir.api_case.ec.common.get_root_dir import get_project_dir
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder


class TestExploreNormalCategory(weeeTest.TestCase):
    @pytest.fixture(scope='class', autouse=True)
    def setup(self, ec_login_header):
        # 切换测试销售组织地区
        SetUserPorder().set_user_zipcode(headers=ec_login_header, zipcode="98011")
        yield
        # 切回98011组织地区
        SetUserPorder().set_user_zipcode(headers=ec_login_header, zipcode="98011")



    @staticmethod
    def get_explore_normal_category(headers, date, zipcode):
        # 获取所有一级普通分类,不包括特殊分类
        catalogue_home = ApiCatalogues().catalogue_home(headers=headers, date=date,
                                                        zipcode=zipcode)
        category_lists = catalogue_home["object"]["category_list"]
        assert len(category_lists) > 0, f"首页分类{category_lists}返回没有数据，请确认"
        assert catalogue_home["object"]["see_all_img_url"] is not None
        # 筛选出满足条件的分类-普通分类
        normal_category = [
            category_list for category_list in category_lists
            if category_list.get('type') == 0 and category_list.get('num') != "global"
        ]
        if zipcode == "98011":
            assert any(item.get('num') == "alcohol" for item in category_lists)

        # print(normal_category)
        return normal_category

    with open(get_project_dir() + "/test_data/autotest_token.json", "r", encoding='utf-8') as f:
        headers = json.load(f)
        log.debug(f"test_explorer_normal_category_headers: {headers}")
    _porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=headers)
    log.debug(f"test_explorer_normal_category_porder: {_porder}")
    porder = _porder.get("object")
    category_lists = get_explore_normal_category(
        headers=headers,
        date=porder["delivery_pickup_date"],
        zipcode=porder["zipcode"]
    )

    @weeeTest.mark.list('B2B', 'Regression', 'Smoke', 'Transaction')
    @pytest.mark.parametrize("category", category_lists)
    def test_explorer_normal_category_new(self, category, ec_login_header):
        """ 分类-普通分类基础验证流程"""
        _porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        normal_category = SearchByCatalogueContent().search_by_catalogue_content(
            headers=ec_login_header,
            date=_porder["delivery_pickup_date"],
            filter_sub_category=category["num"])

        if normal_category.get('object').get('total_count') > 0 and normal_category.get('object').get('contents') is None:
            time.sleep(10)
            normal_category = SearchByCatalogueContent().search_by_catalogue_content(
                headers=ec_login_header,
                date=_porder["delivery_pickup_date"],
                filter_sub_category=category.get("num"))

        self.sec_categories_assertion(
            category=category["num"],
            categories=normal_category["object"]["categories"]
        )
        products = normal_category["object"]["contents"]

        # 检查商品基础信息
        for index, product in enumerate(products):
            if product["type"] == "product":
                CommonCheck().check_product_info(
                    ec_login_header,
                    product["data"],
                    category_type=category.get("num"),
                    source="category",
                    filters="others"
                )
            if index == 20:
                break

                # 3.加购特殊分类下的生鲜商品并断言验证
        CommCheckProductsWithCart().products_data_add_to_cart(
            headers=ec_login_header,
            products=normal_category["object"]["contents"],
            porder_deal_date=_porder["delivery_pickup_date"],
            product_source="mweb_category-" + category.get("num") + "-all"
        )

    @weeeTest.mark.list('Regression', 'Smoke', 'Transaction')
    @pytest.mark.parametrize("category", category_lists)
    def test_b2b_explorer_normal_category_new(self, category, ec_login_header):
        """ 分类-普通分类基础验证流程"""
        _porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        normal_category = SearchByCatalogueContent().search_by_catalogue_content(
            headers=ec_login_header,
            date=_porder["delivery_pickup_date"],
            filter_sub_category=category["num"])

        if normal_category.get('object').get('total_count') > 0 and normal_category.get('object').get('contents') is None:
            time.sleep(10)
            normal_category = SearchByCatalogueContent().search_by_catalogue_content(
                headers=ec_login_header,
                date=_porder["delivery_pickup_date"],
                filter_sub_category=category.get("num"))

        self.sec_categories_assertion(
            category=category["num"],
            categories=normal_category["object"]["categories"]
        )
        products = normal_category["object"]["contents"]

        # 检查商品基础信息
        for index, product in enumerate(products):
            if product["type"] == "product":
                CommonCheck().check_product_info(
                    ec_login_header,
                    product["data"],
                    category_type=category.get("num"),
                    source="category",
                    filters="others"
                )
            if index == 20:
                break

                # 3.加购特殊分类下的生鲜商品并断言验证
        CommCheckProductsWithCart().products_data_add_to_cart(
            headers=ec_login_header,
            products=normal_category["object"]["contents"],
            porder_deal_date=_porder["delivery_pickup_date"],
            product_source="mweb_category-" + category.get("num") + "-all"
        )

    @weeeTest.mark.list('Regression-skip', 'Smoke-skip', 'Transaction')
    def test_explorer_normal_category(self, ec_login_header):
        """ 分类-普通分类基础验证流程"""
        # 已被test_explorer_normal_category_new替代，是同一用例
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        sales_org_id = porder["sales_org_id"]
        deal_date = porder["delivery_pickup_date"]
        delivery_date = porder["delivery_pickup_date"]
        zipcode = porder["zipcode"]
        # 筛选出满足条件的分类-普通分类
        category_lists = self.get_explore_normal_category(headers=ec_login_header,
                                                          date=porder["delivery_pickup_date"],
                                                          zipcode=porder["zipcode"])

        # 访问普通分类
        for category in category_lists:
            # 访问普通分类并返回
            normal_category = SearchByCatalogueContent().search_by_catalogue_content(
                headers=ec_login_header,
                date=delivery_date,
                filter_sub_category=category.get("num"))
            products = normal_category["object"]["contents"]
            # print(products)
            # self.categories_res_assertion(category=category["num"],
            #                               normal_category=normal_category["object"])

            # 断言返回的分类二级分类信息
            self.sec_categories_assertion(category=category.get("num"),
                                          categories=normal_category["object"]["categories"])
            # 断言返回的分类商品及信息
            # CommonCheck().check_products_data_info(category_type=category.get("num"), source="category",
            #                                        products=normal_category["object"]["contents"])
            # 检查商品基础信息
            for index, product in enumerate(products):
                if product["type"] == "product":
                    CommonCheck().check_product_info(ec_login_header, product["data"], category_type=category.get("num"),
                                                     source="category", filters="others")
                if index == 20:
                    break

            # 3.加购特殊分类下的生鲜商品并断言验证
            CommCheckProductsWithCart().products_data_add_to_cart(headers=ec_login_header,
                                                                  products=normal_category["object"]["contents"],
                                                                  porder_deal_date=porder["delivery_pickup_date"],
                                                                  product_source="mweb_category-" + category.get(
                                                                      "num") + "-all")

            # # 点击商品卡片,跳转
            # CommCheckFunction().comm_check_pdp_link(product_id=normal_category["object"]["contents"][-1]['data']["id"],
            #                                         view_link=normal_category["object"]["contents"][-1]['data'][
            #                                             'view_link'])

    @weeeTest.mark.list('explorer_alcohol_category', 'Regression', 'Smoke', 'Transaction')
    def test_explorer_alcohol_category(self, ec_login_header):
        """ 分类-酒分类基础验证流程"""
        # 切换到98011地区,获取用户的porder
        porder = SetUserPorder().set_user_zipcode(headers=ec_login_header, zipcode="98011")
        # 筛选出满足条件的分类-普通分类
        self.get_explore_normal_category(headers=ec_login_header,
                                         date=porder["delivery_pickup_date"],
                                         zipcode=porder["zipcode"])

        # 访问酒分类并返回
        normal_category = SearchByCatalogueContent().search_by_catalogue_content(headers=ec_login_header,
                                                                                 date=porder["delivery_pickup_date"],
                                                                                 filter_sub_category="alcohol")
        # 酒分类返回断言
        self.categories_res_assertion(category="alcohol", normal_category=normal_category["object"])

    @weeeTest.mark.list('Regression', 'Transaction')
    @pytest.mark.parametrize("category", category_lists)
    def test_explorer_normal_category_sort(self, category, ec_login_header):
        """ 分类-普通分类下单选各sort筛选验证流程"""
        # 访问普通分类
        # 访问普通分类并返回
        _porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]

        normal_category = SearchByCatalogueContent().search_by_catalogue_content(
            headers=ec_login_header,
            date=_porder["delivery_pickup_date"],
            filter_sub_category=category.get("num"))

        if normal_category.get('object').get('total_count') > 0 and normal_category.get('object').get('contents') is None:
            time.sleep(10)
            normal_category = SearchByCatalogueContent().search_by_catalogue_content(
                headers=ec_login_header,
                date=_porder["delivery_pickup_date"],
                filter_sub_category=category.get("num"))

        assert normal_category["object"]["total_count"] > 0, f"分类{normal_category}返回没有数据，请确认"
        assert len(normal_category["object"]["contents"]) > 0, f"分类{normal_category}返回没有数据，请确认"
        search_catalogue_num = normal_category["object"]["search_catalogue_num"]
        assert search_catalogue_num == category.get(
            "num"), f'返回分类不对{search_catalogue_num}，应该是{category.get("num")}，请确认'
        assert normal_category["object"]["show_categories"] is True
        categories = normal_category["object"]["categories"]
        # 获取分类下的filters 与sorts
        filters = normal_category["object"]["filters"]
        sorts = normal_category["object"]["sorts"]
        products = normal_category["object"]["contents"]

        # 单选各sort筛选验证流程
        # 最高的价格最高的时候大于20
        # 进行分类下的filters 与sorts 过滤
        self.category_sorts_product_assertion(_porder["zipcode"], _porder["delivery_pickup_date"],
                                              category.get("num"), normal_category["object"]["sorts"],
                                              headers=ec_login_header)

    @weeeTest.mark.list('Regression',  'Transaction')
    @pytest.mark.parametrize("category", category_lists)
    def test_explorer_normal_category_filters(self, category, ec_login_header):
        """ 分类-普通分类下单选各filters筛选验证流程"""

        # 访问普通分类
        # 访问普通分类并返回
        _porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]

        normal_category = SearchByCatalogueContent().search_by_catalogue_content(
            headers=ec_login_header,
            date=_porder["delivery_pickup_date"],
            filter_sub_category=category.get("num"),
            filters=json.dumps(
                {"delivery_type": "delivery_type_local"}
            )
        )

        if normal_category.get('object').get('total_count') > 0 and normal_category.get('object').get('contents') is None:
            time.sleep(10)
            normal_category = SearchByCatalogueContent().search_by_catalogue_content(
                headers=ec_login_header,
                date=_porder["delivery_pickup_date"],
                filter_sub_category=category.get("num"),
                filters=json.dumps(
                    {"delivery_type": "delivery_type_local"}
                )
            )

        assert normal_category["object"]["total_count"] > 0, f"分类{category}:返回没有数据{normal_category}，请确认"
        assert len(
            normal_category["object"]["contents"]) > 0, f"分类{category}:返回没有数据{normal_category}，请确认"
        search_catalogue_num = normal_category["object"]["search_catalogue_num"]
        assert search_catalogue_num == category.get(
            "num"), f'返回子分类不对{search_catalogue_num}，应该是{category.get("num")}，请确认'
        assert normal_category["object"]["show_categories"] is True
        categories = normal_category["object"]["categories"]
        # 获取分类下的filters 与sorts
        filters = normal_category["object"]["filters"]
        sorts = normal_category["object"]["sorts"]
        products = normal_category["object"]["contents"]

        # 单选各sort筛选验证流程
        # 最高的价格最高的时候大于20
        # 进行分类下的filters 与sorts 过滤
        self.category_filters_product_assertion(_porder["zipcode"], _porder["delivery_pickup_date"],
                                                category.get("num"), normal_category["object"]["filters"],
                                                headers=ec_login_header)

    @weeeTest.mark.list('B2B','Regression', 'Transaction')
    @pytest.mark.parametrize("category", category_lists)
    def test_b2b_explorer_normal_category_sort(self, category, ec_login_header):
        """ 分类-普通分类下单选各sort筛选验证流程"""
        # 访问普通分类
        # 访问普通分类并返回
        _porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]

        normal_category = SearchByCatalogueContent().search_by_catalogue_content(
            headers=ec_login_header,
            date=_porder["delivery_pickup_date"],
            filter_sub_category=category.get("num"))

        if normal_category.get('object').get('total_count') > 0 and normal_category.get('object').get('contents') is None:
            time.sleep(10)
            normal_category = SearchByCatalogueContent().search_by_catalogue_content(
                headers=ec_login_header,
                date=_porder["delivery_pickup_date"],
                filter_sub_category=category.get("num"))

        assert normal_category["object"]["total_count"] > 0, f"分类{normal_category}返回没有数据，请确认"
        assert len(normal_category["object"]["contents"]) > 0, f"分类{normal_category}返回没有数据，请确认"
        search_catalogue_num = normal_category["object"]["search_catalogue_num"]
        assert search_catalogue_num == category.get(
            "num"), f'返回分类不对{search_catalogue_num}，应该是{category.get("num")}，请确认'
        assert normal_category["object"]["show_categories"] is True
        categories = normal_category["object"]["categories"]
        # 获取分类下的filters 与sorts
        filters = normal_category["object"]["filters"]
        sorts = normal_category["object"]["sorts"]
        products = normal_category["object"]["contents"]

        # 单选各sort筛选验证流程
        # 最高的价格最高的时候大于20
        # 进行分类下的filters 与sorts 过滤
        self.category_sorts_product_assertion(_porder["zipcode"], _porder["delivery_pickup_date"],
                                              category.get("num"), normal_category["object"]["sorts"],
                                              headers=ec_login_header)

    @weeeTest.mark.list('B2B','Regression',  'Transaction')
    @pytest.mark.parametrize("category", category_lists)
    def test_b2b_explorer_normal_category_filters(self, category, ec_login_header):
        """ 分类-普通分类下单选各filters筛选验证流程"""

        # 访问普通分类
        # 访问普通分类并返回
        _porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]

        normal_category = SearchByCatalogueContent().search_by_catalogue_content(
            headers=ec_login_header,
            date=_porder["delivery_pickup_date"],
            filter_sub_category=category.get("num"),
            filters=json.dumps(
                {"delivery_type": "delivery_type_local"}
            )
        )

        if normal_category.get('object').get('total_count') > 0 and normal_category.get('object').get('contents') is None:
            time.sleep(10)
            normal_category = SearchByCatalogueContent().search_by_catalogue_content(
                headers=ec_login_header,
                date=_porder["delivery_pickup_date"],
                filter_sub_category=category.get("num"),
                filters=json.dumps(
                    {"delivery_type": "delivery_type_local"}
                )
            )

        assert normal_category["object"]["total_count"] > 0, f"分类{category}:返回没有数据{normal_category}，请确认"
        assert len(
            normal_category["object"]["contents"]) > 0, f"分类{category}:返回没有数据{normal_category}，请确认"
        search_catalogue_num = normal_category["object"]["search_catalogue_num"]
        assert search_catalogue_num == category.get(
            "num"), f'返回子分类不对{search_catalogue_num}，应该是{category.get("num")}，请确认'
        assert normal_category["object"]["show_categories"] is True
        categories = normal_category["object"]["categories"]
        # 获取分类下的filters 与sorts
        filters = normal_category["object"]["filters"]
        sorts = normal_category["object"]["sorts"]
        products = normal_category["object"]["contents"]

        # 单选各sort筛选验证流程
        # 最高的价格最高的时候大于20
        # 进行分类下的filters 与sorts 过滤
        self.category_filters_product_assertion(_porder["zipcode"], _porder["delivery_pickup_date"],
                                                category.get("num"), normal_category["object"]["filters"],
                                                headers=ec_login_header)


    # def get_explore_normal_category(self, headers, date, zipcode):
    #     # 获取所有一级普通分类,不包括特殊分类
    #     catalogue_home = ApiCatalogues().catalogue_home(headers=headers, date=date,
    #                                                     zipcode=zipcode)
    #     category_lists = catalogue_home["object"]["category_list"]
    #     assert len(category_lists) > 0, f"首页分类{category_lists}返回没有数据，请确认"
    #     assert catalogue_home["object"]["see_all_img_url"] is not None
    #     # 筛选出满足条件的分类-普通分类
    #     normal_category = [
    #         category_list for category_list in category_lists
    #         if category_list['type'] == 0 and category_list['num'] != "global"
    #     ]
    #     if zipcode == "98011":
    #         assert any(item['num'] == "alcohol" for item in category_lists)
    #
    #     # print(normal_category)
    #     return normal_category

    def categories_res_assertion(self, category, normal_category):
        # 访问普通分类
        assert normal_category["total_count"] > 0, f"分类{normal_category}返回没有数据，请确认"
        search_catalogue_num = normal_category["search_catalogue_num"]
        assert search_catalogue_num == category, f'返回分类不对{search_catalogue_num}，应该是{category}，请确认'
        assert normal_category["show_categories"] is True

    def sec_categories_assertion(self, category, categories: list | Any):

        # 对分类返回二级分类结果断言
        for index, categorie in enumerate(categories):
            assert category in categorie.get("catalogue_num", ""), f'这个{categories}分类下的二级分类返回不对，请确认'

    def category_sorts_product_assertion(self, zipcode, date, category, sorts: list | Any, headers):
        # 对返回结果Sort进行操作及断言
        for sort in sorts:
            # 精选、热销、价格：低到高、价格：高到底
            sorts_res = SearchByCatalogueContent().search_by_catalogue_content(
                headers=headers, date=date,
                zipcode=zipcode, lang="en", filter_sub_category=category,
                sort=sort["sort_key"],
                filters=json.dumps(
                    {"delivery_type": "delivery_type_local"}
                )
            )
            if sorts_res.get('object').get('total_count') > 0 and sorts_res.get('object').get('contents') is None:
                time.sleep(10)
                sorts_res = SearchByCatalogueContent().search_by_catalogue_content(
                    headers=headers, date=date,
                    zipcode=zipcode, lang="en", filter_sub_category=category,
                    sort=sort["sort_key"],
                    filters=json.dumps(
                        {"delivery_type": "delivery_type_local"}
                    )
                )
            # 断言每个分类下必须要有数据
            assert sorts_res["object"] is not None, f'Sort返回异常{sorts_res["object"]}'
            assert sorts_res["object"]["total_count"] > 0, f'Sort返回异常{sorts_res["object"]}'
            assert len(sorts_res["object"]["contents"]) > 0, f'Sort返回异常{sorts_res["object"]}'
            # 筛选出类型为 'product' 的内容
            products = [content for content in sorts_res["object"]["contents"] if content["type"] == "product"
                        and content["data"]["sold_status"] == "available"]
            assert products, f"该filter下没有可售商品category={category}, zipcode={zipcode}, sort={sort}"
            # products = sorts_res["object"]["contents"]

            sort_1 = products[0]["data"]["price"]
            sort_2 = products[-1]["data"]["price"]
            if sort["sort_key"] == "price_asc":
                # 断言商品"价格：低到高"
                assert sort_1 <= sort_2, f"{zipcode}下{category}商品价格：低到高 排序不对：{sort_1}<={sort_2}"
            elif sort["sort_key"] == "price_desc":
                # print(products)
                # 断言商品"价格：高到低"
                assert sort_1 >= sort_2, f"{zipcode}下{category}商品价格：高到低排序不对：{sort_1}>={sort_2}"

    def category_filters_product_assertion(self, zipcode, date, category, filters: list | Any, headers):
        # 对返回结果Filter进行操作及断言
        for filter in filters:
            property_key = filter["property_key"]
            property_values = filter["property_values"]
            for property_value in property_values:
                # 过滤filter
                filters_res = SearchByCatalogueContent().search_by_catalogue_content(
                    headers=headers, date=date,
                    zipcode=zipcode, lang="en", filter_sub_category=category,
                    sort="recommend", filters=json.dumps({property_key: property_value["value_key"]})
                )
                # 这个接口有时会超时，total > 0但contents为None,所以加上重试
                if filters_res.get("object").get("total_count") > 0 and filters_res.get('object').get('contents') is None:
                    time.sleep(50)
                    log.info("再次获取filters_res数据")
                    filters_res = SearchByCatalogueContent().search_by_catalogue_content(
                        headers=headers, date=date,
                        zipcode=zipcode, lang="en", filter_sub_category=category,
                        sort="recommend", filters=json.dumps({property_key: property_value["value_key"]})
                    )
                if property_value["value_key"] == "product_type_new":
                    assert filters_res.get("object"), f"category={category}, filter={property_value['value_key']}没有正确返回结果，filters_res={filters_res['object']}"
                    continue
                # filter不为product_type_new
                assert filters_res.get("object").get("contents"), f'Filter返回异常, contents为None, {filters_res["object"]}, category={category}, filter={property_value["value_key"]}'

                # delivery_type
                if filter["property_key"] == "delivery_type":
                    assert filters_res["object"][
                               "total_count"] > 0, f'分类：{category}，Filter：{property_value["value_key"]} 返回异常{filters_res["object"]}'
                    assert len(filters_res['object'][
                                   'contents']) > 0, f'分类：{category}，Filter：{property_value["value_key"]} 返回异常{filters_res["object"]}'

                    products = [content for content in filters_res["object"]["contents"] if
                                content["type"] == "product"]

                    if property_value["value_key"] == "delivery_type_local_mof":
                        # 断言MOF地区的本地配送商品
                        assert products[-1]["data"][
                                   "is_mkpl"] is False, f'分类：{category}，Filter:{property_value["value_key"]}返回异常{filters_res["object"]}'
                        assert products[-1]["data"][
                                   "is_pantry"] is False, f'分类：{category}，Filter:{property_value["value_key"]}返回异常{filters_res["object"]}'
                    elif property_value["value_key"] == "delivery_type_fbw":
                        # 断言MOF商品是本地配送商品
                        assert products[-1]["data"][
                                   "is_mkpl"] is False, f'分类：{category}，Filter:{property_value["value_key"]}返回异常{filters_res["object"]}'
                        assert products[-1]["data"][
                                   "is_pantry"] is False, f'分类：{category}，Filter:{property_value["value_key"]}返回异常{filters_res["object"]}'
                    elif property_value["value_key"] == "delivery_type_local":
                        # 断言非MOF地区的本地配送商品
                        assert products[-1]["data"][
                                   "is_mkpl"] is False, f'分类：{category}，Filter:{property_value["value_key"]}返回异常{filters_res["object"]}'
                        assert products[-1]["data"][
                                   "is_pantry"] is False, f'分类：{category}，Filter:{property_value["value_key"]}返回异常{filters_res["object"]}'
                    elif property_value["value_key"] == "delivery_type_pantry":
                        # 断言商品是pantry 商品
                        assert products[-1]["data"][
                                   "is_pantry"] is True, f'分类：{category}，Filter:{property_value["value_key"]}返回异常{filters_res["object"]}'
                    elif property_value["value_key"] == "delivery_type_global":
                        # 断言商品是global+ 商品
                        assert products[-1]["data"][
                                   "is_mkpl"] is True, f'分类：{category}，Filter:{property_value["value_key"]}返回异常{filters_res["object"]}'
                        assert products[-1]["data"][
                                   "biz_type"] == "seller", f'分类：{category}，Filter:{property_value["value_key"]}返回异常{filters_res["object"]}'

                # product_type
                elif filter["property_key"] == "product_type":
                    if filters_res["object"]["total_count"] > 0:
                        products = [content for content in filters_res["object"]["contents"] if
                                    content["type"] == "product"]

                        if property_value["value_key"] == "product_type_sale":
                            # 断言商品是on sale商品,有折扣价
                            for index, product in enumerate(products):
                                if product["data"]["volume_price_support"] is True:
                                    assert product["data"]["price"] < product["data"]["volume_price"]
                                else:
                                    pass
                                    # 20250613报错，需要优化
                                    # assert product["data"][
                                    #            "base_price"] is not None, f'商品{product["data"]["id"]}base_price返回为空，请确认{product}'
                                    # assert product["data"]["base_price"] > product["data"][
                                    #     "price"], f'商品{product["data"]["id"]}:base_price{product["data"]["base_price"]},price:{product["data"]["price"]}'
                                if index == 2:
                                    break

                        elif property_value["value_key"] == "product_type_new":
                            # 断言商品是new 商品
                            # 断言 'label_key' 为 'new' 的元素存在
                            assert any(item['label_key'] == "new" for item in products[-1]["data"][
                                'label_list']), f'分类：{category}，Filter:{property_value["value_key"]},{products[-1]["data"]["id"]}:label_key="new" does not exist'
                            # assert products[-1]["data"]['label_list'][0]['label_key'] == 'new', f"商品返回不是new商品，请确认~"
                        elif property_value["value_key"] == "product_type_cold_pack":
                            # 断言商品是 冷链商品
                            assert products[-1]["data"][
                                       "is_colding_package"] is True, f'分类：{category}，Filter:{property_value["value_key"]}返回异常{filters_res["object"]}'
                # 产地过滤
                elif filter["property_key"] == "5":
                    assert filters_res["object"][
                               "total_count"] > 0, f'分类：{category}，Filter:产地过滤：{property_value["value_key"]} 返回异常{filters_res["object"]}'
                    assert filters_res.get('object').get('contents'), f'分类：{category}，Filter:产地过滤：{property_value["value_key"]} 返回异常{filters_res["object"]}'

                # 价格区间
                elif filter["property_key"] == "6":
                    assert filters_res["object"][
                               "total_count"] > 0, f'分类：{category}，Filter:价格区间：{property_value["value_key"]} 返回异常{filters_res["object"]}'
                    assert len(filters_res['object'][
                                   'contents']) > 0, f'分类：{category}，Filter:价格区间：{property_value["value_key"]} 返回异常{filters_res["object"]}'
                    products = [content for content in filters_res["object"]["contents"] if
                                content["type"] == "product"]
                    # $ 价格区间小于$5
                    if property_value["value_key"] == "1":
                        assert all(product["data"]["price"] < 5 for product in
                                   products), f'分类：{category}，Filter:{property_value["value_key"]}返回异常{filters_res["object"]},Not all prices are less than $5'
                    # $ 价格区间 $5-10
                    elif property_value["value_key"] == "2":
                        assert all(5 <= product["data"]["price"] < 10 for product in
                                   products), f'分类：{category}，Filter:{property_value["value_key"]}返回异常{filters_res["object"]},Not all prices are in $5-10'

                    # $ 价格区间 $10-15
                    elif property_value["value_key"] == "3":
                        assert all(10 <= product["data"]["price"] < 15 for product in
                                   products), f'分类：{category}，Filter:{property_value["value_key"]}返回异常{filters_res["object"]},Not all prices are in $10-15'

                    # $ 价格区间 $15-25
                    elif property_value["value_key"] == "4":
                        assert all(15 <= product["data"]["price"] < 25 for product in
                                   products), f'分类：{category}，Filter:{property_value["value_key"]}返回异常{filters_res["object"]},Not all prices are in $15-25'
                    # $ 价格区间高于 $25
                    elif property_value["value_key"] == "5":
                        assert all(product["data"]["price"] >= 25 for product in
                                   products), f'分类：{category}，Filter:{property_value["value_key"]}返回异常{filters_res["object"]},Not all prices are more than $25'
                # 商家过滤
                elif filter["property_key"] == "8":
                    assert filters_res["object"][
                               "total_count"] > 0, f'分类：{category}，Filter:商家过滤：{property_value["value_key"]} 返回异常{filters_res["object"]}'
                    assert len(filters_res.get("object").get(
                        "contents")) > 0, f'分类：{category}，Filter:商家过滤：{property_value["value_key"]} 返回异常{filters_res["object"]}'
                    products = [content for content in filters_res["object"]["contents"] if
                                content["type"] == "product"]
                    # 断言商品是第三方商家商品
                    if property_value["value_key"] != "0":
                        assert all(str(product["data"]["vender_id"]) == property_value["value_key"] for product in
                                   products), f'分类：{category}，Filter:{property_value["value_key"]}返回异常{filters_res["object"]},'
