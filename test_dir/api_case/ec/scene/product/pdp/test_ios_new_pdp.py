# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  zhongyuan.xu
@Version        :  V1.0.0
------------------------------------
@File           :  test_ios_new_pdp.py
@Description    :  PDP 亮点，review接口 、ai（app 端）:https://docs.google.com/spreadsheets/d/1kqZefY0tmvg3fJI7YM2fqqOuOPnpNcWZGFfWljn4A9g/edit?usp=sharing
@CreateTime     :  2025/5/20 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/5/21 15:23
"""

import pytest
import weeeTest

from test_dir.api.ec.ec_item.product.ios_new_pdp_detail import IOSNewPdpDetail
from test_dir.api.ec.ec_social.post_info.post_info import PostInfo
from test_dir.api.ec.ec_social.review_info.review_info import ReviewInfo
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction
from test_dir.api_case.ec.common.common_check import CommonCheck


class TestIosNewPDP(weeeTest.TestCase):
    @pytest.fixture(scope='class', autouse=True)
    def change_header(self, ec_login_header):
        """
        前置条件 header的Platform为ios, app_version为20.20，否则用H5的header无法获取数据
        """
        ec_login_header['platform'] = 'ios'
        # ec_login_header['device-id'] = "E80DA969-57D5-4267-8410-58642A42B5C7"
        ec_login_header['app_version'] = '20.20'
        yield ec_login_header

    @weeeTest.mark.list('Regression')
    def test_new_ios_pdp_summary(self, change_header):
        """ 【114087】 PDP-product summary亮点 """

        new_ios_pdp = IOSNewPdpDetail().ios_new_product_summary(headers=change_header)
        assert new_ios_pdp.get('object') and new_ios_pdp.get('object').get('summary') and new_ios_pdp.get('object').get('contents'), f"获取ios pdp页面summary数据失败，new_ios_pdp={new_ios_pdp}"
        summary_contents = new_ios_pdp.get('object').get('contents')
        for item in summary_contents:
            assert CommonCheck.list_check(['id', 'status', 'link', 'user_name'], item.keys()), f"获取ios pdp页面summary数据不正确，item={item}"
            assert item.get('id'), f"获取ios pdp页面summary数据不正确，item={item}"
            assert item.get('link'), f"获取ios pdp页面summary数据的link不正确，item={item}"
            CommCheckFunction().comm_check_link(view_link=item.get('link'), headers=change_header)

    @weeeTest.mark.list('Regression')
    def test_new_ios_pdp_review(self, change_header):
        """ 114086 PDP-review组件验证 ios pdp亮点-ai review """

        ios_new_review = ReviewInfo().query_review_list(headers=change_header, product_id=25652)
        assert ios_new_review.get('object') and ios_new_review.get('object').get('list'), f"获取ios pdp页面review数据失败，ios_new_review={ios_new_review}"
        assert CommonCheck.list_check(['total', 'list', 'explore_more_url', 'sort', 'top_reviews'], ios_new_review.get('object').keys()), f"获取ios pdp页面review数据失败，ios_new_review={ios_new_review}"
        review_list = ios_new_review.get('object').get('list')
        for item in review_list:
            assert CommonCheck().list_check(['id', 'status', 'link', 'user_name', 'quantity'], item.keys()), f"获取ios pdp页面review数据不正确，item={item}"
            assert item.get('id'), f"获取ios pdp页面review数据不正确，item={item}"
            assert item.get('link'), f"获取ios pdp页面review数据的link不正确，item={item}"
            CommCheckFunction().comm_check_link(view_link=item.get('link'), headers=change_header)

    @weeeTest.mark.list('Regression')
    def test_new_ios_pdp_video(self, change_header):
        """ ios pdp亮点-video """

        ios_new_video = PostInfo().post_pdp_video(headers=change_header, product_id=25652, type="video")
        assert ios_new_video.get('object') and ios_new_video.get('object').get('list'), f"获取ios pdp页面video数据失败，ios_new_video={ios_new_video}"
        assert CommonCheck.list_check(['total', 'list', 'categories','explore_more_url'], ios_new_video.get('object').keys()), f"获取ios pdp页面video数据失败，ios_new_video={ios_new_video}"
        video_list = ios_new_video.get('object').get('list')
        for item in video_list:
            assert CommonCheck().list_check(['id', 'status', 'link', 'user_name'], item.keys()), f"获取ios pdp页面video数据不正确，item={item}"
            assert item.get('id'), f"获取ios pdp页面video数据不正确，item={item}"
            assert item.get('link'), f"获取ios pdp页面video数据的link不正确，item={item}"
            CommCheckFunction().comm_check_link(view_link=item.get('link'), headers=change_header)
