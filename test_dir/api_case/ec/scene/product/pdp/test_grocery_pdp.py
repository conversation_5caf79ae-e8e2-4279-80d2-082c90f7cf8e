# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import copy
import json
from typing import Any

import pytest
import weeeTest
from weeeTest import jmespath

from test_dir.api.ec.ec_item.product.pdp_detail import PdpDetail
from test_dir.api.ec.ec_item.product_v2.get_buy_together_products import GetBuyTogetherProducts
from test_dir.api.ec.ec_item.product_v2.get_products_modules import GetProductsModules
from test_dir.api.ec.ec_item.product_v2.get_products_modules_second import GetProductsModulesSecond
from test_dir.api.ec.ec_item.product_v2.get_products_related import GetProductsRelated
from test_dir.api.ec.ec_item.recommend.recommend_vender import RecommendVender
from test_dir.api.ec.ec_item.search_v3.search_by_catalogue_content import SearchByCatalogueContent
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_promotion.promotion.query_promotion_info import QueryPromotionInfo
from test_dir.api.ec.ec_promotion.coupon.get_coupon_info import Couponinfo
from test_dir.api.ec.ec_social.post_info.post_info import PostInfo
from test_dir.api.ec.ec_social.review_info.review_info import ReviewInfo
from test_dir.api_case.ec.common.comm_check_products_with_cart import CommCheckProductsWithCart
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction
from test_dir.api_case.ec.common.common_check import CommonCheck
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder


class TestGroceryPdp(weeeTest.TestCase):
    @pytest.fixture(scope='class', autouse=True)
    def teardown(self, ec_login_header):
        yield
        # 切回98011组织地区
        SetUserPorder().set_user_zipcode(headers=ec_login_header, zipcode="98011")

    # @weeeTest.mark.list('cold_package_product', 'Regression', 'Smoke', 'Transaction')
    # mof需求变动，待恢复
    def test_cold_package_product(self, ec_login_header):
        """ Product-冷链商品PDP验证流程 """
        # 切换到MOF地区,获取用户的porder
        porder = SetUserPorder().set_user_zipcode(headers=ec_login_header, zipcode="49417")
        catalogue_content = SearchByCatalogueContent().search_by_catalogue_content(
            headers=ec_login_header,
            date=porder["delivery_pickup_date"],
            zipcode=porder["zipcode"],
            filter_sub_category="sale",
            filters=json.dumps(
                {"delivery_type": "delivery_type_local_mof",
                 "product_type": "product_type_cold_pack"})
        )
        assert catalogue_content["object"][
                   "total_count"] > 0, f'sales分类返回数据异常，请确认{catalogue_content["object"]}'
        items = jmespath(catalogue_content, "object.contents")

        # 获取PDP 信息
        product = self.catalogue_content_assertion(items=items, zipcode="49417", sales_org_id=porder["sales_org_id"],
                                                   headers=ec_login_header)
        # pdp 基础断言
        self.pdp_product_assertion(product=product, category="sale", filters="cold_pack", headers=ec_login_header)
        # pdp 商品状态验证
        self.pdp_product_status_assertion(product=product, deal_date=porder["delivery_pickup_date"],
                                          source="product-cart", headers=ec_login_header)

    @weeeTest.mark.list('pantry_product', 'Regression', 'Smoke', 'Transaction')
    def test_pantry_product(self, ec_login_header):
        """ Product-pantry商品PDP验证流程 """
        # 获取用户的porder
        porder = SetUserPorder().set_user_zipcode(headers=ec_login_header, zipcode="98011")
        catalogue_content = SearchByCatalogueContent().search_by_catalogue_content(
            headers=ec_login_header,
            date=porder["delivery_pickup_date"],
            zipcode=porder["zipcode"],
            filter_sub_category="sale",
            filters=json.dumps(
                {"delivery_type": "delivery_type_pantry"})
        )
        assert catalogue_content["object"][
                   "total_count"] > 0, f'sales分类返回数据异常，请确认{catalogue_content["object"]}'
        items = jmespath(catalogue_content, "object.contents")

        # 获取PDP 信息
        product = self.catalogue_content_assertion(items=items, zipcode="98011", sales_org_id=porder["sales_org_id"],
                                                   headers=ec_login_header)
        # pdp 基础断言
        self.pdp_product_assertion(product=product, category="sale", filters="pantry", headers=ec_login_header)
        # pdp 商品状态验证
        self.pdp_product_status_assertion(product=product, deal_date=porder["delivery_pickup_date"],
                                          source="product-cart", headers=ec_login_header)
        # # 验证返回相关商品
        # self.products_modules_assertion(product=product, deal_date=porder["delivery_pickup_date"])
        # # 验证历史预览商品
        # self.modules_second_assertion(product=product, deal_date=porder["delivery_pickup_date"])
        # 验证活动商品
        self.promotion_product_assertion(product=product, headers=ec_login_header)

    @weeeTest.mark.list('global_product', 'Regression', 'Smoke', 'Transaction')
    def test_global_product(self, ec_login_header):
        """ Product-Global+商品PDP验证流程 """
        # 获取用户的porder
        porder = SetUserPorder().set_user_zipcode(headers=ec_login_header, zipcode="98011")
        catalogue_content = SearchByCatalogueContent().search_by_catalogue_content(
            headers=ec_login_header,
            date=porder["delivery_pickup_date"],
            zipcode=porder["zipcode"],
            filter_sub_category="sale",
            filters=json.dumps(
                {"delivery_type": "delivery_type_global"})
        )
        assert catalogue_content["object"][
                   "total_count"] > 0, f'sales分类返回数据异常，请确认{catalogue_content["object"]}'
        items = jmespath(catalogue_content, "object.contents")
        # 获取PDP 信息
        product = self.catalogue_content_assertion(items=items, zipcode="98011", sales_org_id=porder["sales_org_id"],
                                                   headers=ec_login_header)
        # pdp 基础断言
        self.pdp_product_assertion(product=product, category="sale", filters="global", headers=ec_login_header)

        # pdp 调用店铺推荐信息及断言
        self.vender_info_assertion(vender_id=product["vender_info_view"]["vender_id"], product_id=product["id"],
                                   deal_date=porder["delivery_pickup_date"], source="same-vendor",
                                   headers=ec_login_header)

        # pdp 搜藏
        CommCheckFunction().comm_set_favorites(headers=ec_login_header, target_id=product["id"])
        # pdp 商品状态验证
        self.pdp_product_status_assertion(product, porder["deal_date"], "product-cart", headers=ec_login_header)
        # 验证历史预览商品
        self.modules_second_assertion(product, porder["deal_date"], headers=ec_login_header)
        # 验证活动商品
        self.promotion_product_assertion(product, headers=ec_login_header)

    @weeeTest.mark.list('test_local_fbw_bakery_product', 'Regression', 'Smoke', 'Transaction')
    def test_local_fbw_bakery_product(self, ec_login_header):
        """ Product-Local FBW商品验证流程 """
        # 获取用户的porder
        porder = SetUserPorder().set_user_zipcode(headers=ec_login_header, zipcode="98011")
        catalogue_content = SearchByCatalogueContent().search_by_catalogue_content(
            headers=ec_login_header,
            date=porder["delivery_pickup_date"],
            zipcode=porder["zipcode"],
            filter_sub_category="freshbakery"
        )
        assert catalogue_content["object"][
                   "total_count"] > 0, f'sales分类返回数据异常，请确认{catalogue_content["object"]}'
        items = jmespath(catalogue_content, "object.contents")
        # 获取PDP 信息
        product = self.catalogue_content_assertion(items=items, zipcode="98011", sales_org_id=porder["sales_org_id"],
                                                   headers=ec_login_header)
        # 商家截单之后，product会没有数据，全是change date的数据，所以加上这个判断
        porder_new = CommonCheck().check_product_status_assertion(headers=ec_login_header,
                                                                  deal_date=porder["delivery_pickup_date"],
                                                                  source="pdp",
                                                                  product=product)

        # 6月6日调试时获取的product为None,加上此assert
        assert CommonCheck.list_check(['id', 'name', 'sold_status'],
                                      product.keys()), f"获取category=freshbakery的商品失败，product={product}"
        # pdp 基础断言
        self.pdp_product_assertion(product=product, category="freshbakery", headers=ec_login_header)
        # 验证返回相关商品
        self.products_modules_assertion(product=product, deal_date=porder_new["delivery_pickup_date"],
                                        category="freshbakery", headers=ec_login_header)

    @weeeTest.mark.list('local_fbw_product', 'Regression', 'Smoke', 'Transaction')
    def test_local_fbw_gourmet_product(self, ec_login_header):
        """ Product-Local FBW商品验证流程 """
        # 获取用户的porder
        porder = SetUserPorder().set_user_zipcode(headers=ec_login_header, zipcode="98011")
        catalogue_content = SearchByCatalogueContent().search_by_catalogue_content(
            headers=ec_login_header,
            date=porder["delivery_pickup_date"],
            zipcode=porder["zipcode"],
            filter_sub_category="freshgourmet"
        )
        assert catalogue_content["object"][
                   "total_count"] > 0, f'sales分类返回数据异常，请确认{catalogue_content["object"]}'
        items = jmespath(catalogue_content, "object.contents")
        # 获取PDP 信息
        product = self.catalogue_content_assertion(items=items, zipcode="98011",
                                                   sales_org_id=porder["sales_org_id"], headers=ec_login_header)
        # 商家截单之后，product会没有数据，全是change date的数据，所以加上这个判断
        porder_new = CommonCheck().check_product_status_assertion(headers=ec_login_header,
                                                                  deal_date=porder["delivery_pickup_date"],
                                                                  source="pdp",
                                                                  product=product)
        # pdp 基础断言
        self.pdp_product_assertion(product=product, category="freshgourmet", headers=ec_login_header)
        # 验证返回相关商品
        self.products_modules_assertion(product=product, deal_date=porder_new["delivery_pickup_date"],
                                        category="freshgourmet", headers=ec_login_header)

    @weeeTest.mark.list('global_fbw_product', 'Regression', 'Smoke', 'Transaction', 'single')
    def test_global_fbw_product(self, ec_login_header):
        """ Product-Global+ FBW商品验证流程 """
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 给指定gloabl fbw 商品 获取PDP 信息
        product_id = 2120380
        # 返回冷链商品pdp的所有信息，包括product group
        pdp_detail = PdpDetail().pdp_detail(headers=ec_login_header, product_id=product_id,
                                            zipcode=porder["zipcode"], sales_org_id=porder["sales_org_id"])
        assert pdp_detail["object"][
                   "product"] is not None, f'该商品{product_id}未返回商品信息，请确认{pdp_detail["object"]}'

        product = pdp_detail["object"]["product"]
        # pdp 基础断言
        self.pdp_product_assertion(product=product, category="global_fbw", headers=ec_login_header)

    @weeeTest.mark.list('Regression', 'Smoke', 'Transaction')
    def test_grocery_product(self, ec_login_header):
        """ Product-生鲜商品验证流程 """
        # 获取用户的porder
        porder = SetUserPorder().set_user_zipcode(headers=ec_login_header, zipcode="98011")
        catalogue_content = SearchByCatalogueContent().search_by_catalogue_content(
            headers=ec_login_header,
            date=porder["delivery_pickup_date"],
            zipcode=porder["zipcode"],
            filter_sub_category="green",
            filters=json.dumps(
                {"delivery_type": "delivery_type_local"})

        )
        assert catalogue_content["object"][
                   "total_count"] > 0, f'sales分类返回数据异常，请确认{catalogue_content["object"]}'
        items = jmespath(catalogue_content, "object.contents")
        # 获取蔬菜商品PDP 信息
        product = self.catalogue_content_assertion(items=items, zipcode="98011",
                                                   sales_org_id=porder["sales_org_id"], headers=ec_login_header)
        # pdp 基础断言
        self.pdp_product_assertion(product=product, category="green", filters="delivery", headers=ec_login_header)

        # pdp 搜藏
        CommCheckFunction().comm_set_favorites(headers=ec_login_header, target_id=product["id"])
        # pdp 商品状态验证
        self.pdp_product_status_assertion(product=product, deal_date=porder["delivery_pickup_date"],
                                          source="product-cart", headers=ec_login_header)
        # 验证返回相关商品+经常一起购买
        self.products_modules_assertion(product=product, deal_date=porder["delivery_pickup_date"],
                                        headers=ec_login_header)
        self.buy_together_assertion(product=product, deal_date=porder["delivery_pickup_date"], headers=ec_login_header)
        self.products_related_assertion(product=product, deal_date=porder["delivery_pickup_date"],
                                        headers=ec_login_header)
        # 验证历史预览商品
        self.modules_second_assertion(product=product, deal_date=porder["delivery_pickup_date"],
                                      headers=ec_login_header)
        # 验证活动商品
        # self.promotion_product_assertion(product=product)

    @weeeTest.mark.list('Regression', 'Transaction')
    def test_grocery_product_group(self, ec_login_header):
        """ Product-Product Group验证流程 """
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 给特定的有product的商品,生鲜、mkpl 各找一个
        products = [84307, 2041813]
        # 获取蔬菜商品PDP 信息
        for product_id in products:
            # 返回冷链商品pdp的所有信息，包括product group
            pdp_detail = PdpDetail().pdp_detail(headers=ec_login_header, product_id=product_id,
                                                zipcode=porder["zipcode"], sales_org_id=porder["sales_org_id"])
            assert pdp_detail["object"][
                       "product"] is not None, f'该商品{product_id}未返回商品信息，请确认{pdp_detail["object"]}'
            assert pdp_detail["object"]["product"][
                       "group"] is not None, f'该商品{product_id}未返回商品信息，请确认{pdp_detail["object"]["product"]}'
            self.product_group_assertion(product_id=product_id,
                                         group=pdp_detail["object"]["product"]["group"], headers=ec_login_header)

            assert pdp_detail["object"]["product"][
                       "group"][
                       "propertyList"] is not None, f'该商品{product_id}未返回商品信息，请确认{pdp_detail["object"]["product"]}'

            assert pdp_detail["object"]["product"][
                       "group"][
                       "groupProductList"] is not None, f'该商品{product_id}未返回商品信息，请确认{pdp_detail["object"]["product"]}'

    @weeeTest.mark.list('B2B','Regression', 'Smoke', 'Transaction')
    def test_b2b_grocery_product(self, ec_login_header):
        """ Product-生鲜商品验证流程 """
        # 获取用户的porder
        porder = SetUserPorder().set_user_zipcode(headers=ec_login_header, zipcode="94538")
        catalogue_content = SearchByCatalogueContent().search_by_catalogue_content(
            headers=ec_login_header,
            date=porder["delivery_pickup_date"],
            zipcode=porder["zipcode"],
            filter_sub_category="green",
            filters=json.dumps(
                {"delivery_type": "delivery_type_local"})

        )
        assert catalogue_content["object"][
                   "total_count"] > 0, f'sales分类返回数据异常，请确认{catalogue_content["object"]}'
        items = jmespath(catalogue_content, "object.contents")
        # 获取蔬菜商品PDP 信息
        product = self.catalogue_content_assertion(items=items, zipcode="98011",
                                                   sales_org_id=porder["sales_org_id"], headers=ec_login_header)
        # pdp 基础断言
        self.pdp_product_assertion(product=product, category="green", filters="delivery", headers=ec_login_header)

        # pdp 搜藏
        CommCheckFunction().comm_set_favorites(headers=ec_login_header, target_id=product["id"])
        # pdp 商品状态验证
        self.pdp_product_status_assertion(product=product, deal_date=porder["delivery_pickup_date"],
                                          source="product-cart", headers=ec_login_header)
        # 验证返回相关商品+经常一起购买
        self.products_modules_assertion(product=product, deal_date=porder["delivery_pickup_date"],
                                        headers=ec_login_header)
        self.buy_together_assertion(product=product, deal_date=porder["delivery_pickup_date"], headers=ec_login_header)
        self.products_related_assertion(product=product, deal_date=porder["delivery_pickup_date"],
                                        headers=ec_login_header)
        # 验证历史预览商品
        self.modules_second_assertion(product=product, deal_date=porder["delivery_pickup_date"],
                                      headers=ec_login_header)
        # 验证活动商品
        # self.promotion_product_assertion(product=product)

    @weeeTest.mark.list('B2B', 'Regression', 'Transaction')
    def test_b2b_grocery_product_group(self, ec_login_header):
        """ Product-Product Group验证流程 """
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 给特定的有product的商品各找一个
        products = [2821598, 2041813]
        # 获取蔬菜商品PDP 信息
        for product_id in products:
            # 返回冷链商品pdp的所有信息，包括product group
            pdp_detail = PdpDetail().pdp_detail(headers=ec_login_header, product_id=product_id,
                                                zipcode=porder["zipcode"], sales_org_id=porder["sales_org_id"])
            assert pdp_detail["object"][
                       "product"] is not None, f'该商品{product_id}未返回商品信息，请确认{pdp_detail["object"]}'
            assert pdp_detail["object"]["product"][
                       "group"] is not None, f'该商品{product_id}未返回商品信息，请确认{pdp_detail["object"]["product"]}'
            self.product_group_assertion(product_id=product_id,
                                         group=pdp_detail["object"]["product"]["group"], headers=ec_login_header)

            assert pdp_detail["object"]["product"][
                       "group"][
                       "propertyList"] is not None, f'该商品{product_id}未返回商品信息，请确认{pdp_detail["object"]["product"]}'

            assert pdp_detail["object"]["product"][
                       "group"][
                       "groupProductList"] is not None, f'该商品{product_id}未返回商品信息，请确认{pdp_detail["object"]["product"]}'


    @weeeTest.mark.list('Regression', 'Smoke', 'Transaction')
    def test_pdp_65_warming_product(self, ec_login_header):
        """ PDP-65警告验证流程 """
        # 只有湾区才有这个
        # 切换到94538地区,获取用户的porder
        p_header = copy.deepcopy(ec_login_header)
        porder = SetUserPorder().set_user_zipcode(headers=p_header, zipcode="94538")
        # 返回商品pdp的所有信息，包括product group
        pdp_detail = PdpDetail().pdp_detail(headers=p_header,
                                            product_id=92306, zipcode=porder["zipcode"],
                                            sales_org_id=porder["sales_org_id"])
        assert pdp_detail["object"]["product"] is not None, f'该商品{92306}未返回商品信息，请确认{pdp_detail["object"]}'
        product = pdp_detail["object"]["product"]
        # 断言65警告
        assert product["additionalMap"][
                   "warning"] is not None, f'湾区商品{product["id"]} pdp 警告内容返回空，请确认{product}'
        self.pdp_product_assertion(product=product, headers=ec_login_header)

        # 切回98011
        SetUserPorder().set_user_zipcode(headers=p_header, zipcode="98011")

    @weeeTest.mark.list('Regression', 'Smoke', 'Transaction')
    def test_pdp_disclaimer_product(self, ec_login_header):
        """ PDP-声明验证流程 """
        # 只有湾区才有这个
        # 切换到94538地区,获取用户的porder
        p_header = copy.deepcopy(ec_login_header)
        porder = SetUserPorder().set_user_zipcode(headers=p_header, zipcode="94538")
        # SetUserPorder().set_user_new_porder(headers=ec_login_header, zipcode=94538, lang="en")
        # porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 返回商品pdp的所有信息，包括product group
        pdp_detail = PdpDetail().pdp_detail(headers=p_header,
                                            product_id=92306, zipcode=porder["zipcode"],
                                            sales_org_id=porder["sales_org_id"])
        assert pdp_detail["object"]["product"] is not None, f'该商品{92306}未返回商品信息，请确认{pdp_detail["object"]}'
        product = pdp_detail["object"]["product"]
        # # 断言65警告
        assert product["additionalMap"][
                   "disclaimer"] is not None, f'湾区商品{product["id"]} pdp 声明内容返回空，请确认{product}'
        self.pdp_product_assertion(product=product, headers=ec_login_header)

        # 切回98011
        SetUserPorder().set_user_zipcode(headers=p_header, zipcode="98011")

    @weeeTest.mark.list('Regression', 'Smoke', 'Transaction')
    def test_pdp_return_policy(self, ec_login_header):
        """ PDP-退款政策验证流程 """
        # 只有湾区才有这个
        # 切换到94538地区,获取用户的porder
        p_header = copy.deepcopy(ec_login_header)
        porder = SetUserPorder().set_user_zipcode(headers=p_header, zipcode="94538")
        # 返回商品pdp的所有信息，包括product group
        pdp_detail = PdpDetail().pdp_detail(headers=p_header,
                                            product_id=92306, zipcode=porder["zipcode"],
                                            sales_org_id=porder["sales_org_id"])
        assert pdp_detail["object"]["product"] is not None, f'该商品{92306}未返回商品信息，请确认{pdp_detail["object"]}'
        product = pdp_detail["object"]["product"]
        # 断言65警告
        assert product["additionalMap"][
                   "return_policy"] is not None, f'湾区商品{product["id"]} pdp 退款政策内容返回空，请确认{product}'
        self.pdp_product_assertion(product=product, headers=ec_login_header)

        # 切回98011
        SetUserPorder().set_user_zipcode(headers=p_header, zipcode="98011")

    @weeeTest.mark.list('alcohol_product', 'Regression', 'Smoke', 'Transaction')
    def test_alcohol_product(self, ec_login_header):
        """ Product-酒商品验证流程 """
        # 获取用户的porder
        porder = SetUserPorder().set_user_zipcode(headers=ec_login_header, zipcode="98011")
        catalogue_content = SearchByCatalogueContent().search_by_catalogue_content(
            headers=ec_login_header,
            date=porder["delivery_pickup_date"],
            zipcode=porder["zipcode"],
            filter_sub_category="alcohol",
            filters=json.dumps(
                {"delivery_type": "delivery_type_local_mof"})

        )
        assert catalogue_content["object"][
                   "total_count"] > 0, f'sales分类返回数据异常，请确认{catalogue_content["object"]}'
        items = jmespath(catalogue_content, "object.contents")
        # 获取PDP 信息
        product = self.catalogue_content_assertion(items=items, zipcode="98011",
                                                   sales_org_id=porder["sales_org_id"], headers=ec_login_header)
        # pdp 基础断言
        self.pdp_product_assertion(product=product, category="alcohol", headers=ec_login_header)

    @weeeTest.mark.list('test_pdp_recommend_waterfall', 'Regression', 'Smoke', 'Transaction')
    def test_pdp_recommend_waterfall(self, ec_login_header):
        """ Product-pdp推荐waterfall验证流程 """
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]

        product_id = 94776
        # 返回冷链商品pdp的所有信息，包括product group
        pdp_detail = PdpDetail().pdp_detail(headers=ec_login_header,
                                            product_id=product_id, zipcode=porder["zipcode"],
                                            sales_org_id=porder["sales_org_id"])
        assert pdp_detail["object"][
                   "product"] is not None, f'该商品{product_id}未返回商品信息，请确认{pdp_detail["object"]}'

        pdp_waterfall = GetProductsModulesSecond().recently_view_waterfall(headers=ec_login_header,
                                                                           product_id=product_id)
        assert len(pdp_waterfall["object"]["contents"]) > 0, f'pdp 推荐waterfall模块数据返回异常{pdp_waterfall}'
        assert pdp_waterfall["object"][
                   "module_key"] == "pdp_also_viewed", f'pdp 推荐waterfall模块数据返回异常{pdp_waterfall}'
        assert pdp_waterfall["object"]["title"] is not None, f'pdp 推荐waterfall模块数据返回异常{pdp_waterfall}'

        contents = pdp_waterfall["object"]["contents"]
        for content in contents:
            product = content["data"]["product"]
            CommonCheck().check_product_info(ec_login_header, category_type="others", source="others", product=product)

    def catalogue_content_assertion(self, items, zipcode, sales_org_id, headers):
        """ 商品验证流程 """
        for item in items:
            product = item["data"]
            product_id = product["id"]
            # 返回冷链商品pdp的所有信息，包括product group
            pdp_detail = PdpDetail().pdp_detail(headers=headers,
                                                product_id=item["data"]["id"],
                                                zipcode=zipcode, sales_org_id=sales_org_id)
            assert pdp_detail["object"][
                       "product"] is not None, f'该商品{product_id}未返回商品信息，请确认{pdp_detail["object"]}'
            return pdp_detail["object"]["product"]

    def pdp_product_status_assertion(self, product: dict | Any, deal_date, source, headers):
        if product["sold_status"] == "sold_out":
            # 售罄状态
            assert product["restock_tip"] is not None
            CommCheckFunction().comm_set_favorites(headers=headers, target_id=product["id"])

        if product["sold_status"] == "available":
            # 可加够状态
            CommCheckProductsWithCart().product_add_to_cart(headers=headers,
                                                            product=product, porder_deal_date=deal_date,
                                                            product_source=source,
                                                            quantity=product["min_order_quantity"])
        if product["sold_status"] == "change_other_day":
            # 切换日期
            pass

    def pdp_product_assertion(self, headers, product: dict | Any, category: str = "others", filters: str = None):
        # 对分类返回结果商品操作及断言
        assert product['name'] is not None, f'商品{product["id"]}name返回为空，请确认{product}'
        assert product['img'] is not None, f'商品{product["id"]}img返回为空，请确认{product}'
        assert "weeecdn" in product['img'], f'商品{product["id"]}img返回为空，请确认{product}'
        assert product['square_img_url'] is not None, f'商品{product["id"]}img返回为空，请确认{product}'
        assert product['img_urls'] is not None, f'商品{product["id"]}img_urls返回为空，请确认{product}'
        assert product['media_urls'] is not None, f'商品{product["id"]}media_urls返回为空，请确认{product}'
        assert product['sold_status'] is not None, f'商品{product["id"]}sold_status返回为空，请确认{product}'
        assert product['price'] is not None, f'商品{product["id"]}price返回为空，请确认{product}'
        # assert product['max_order_quantity'] <= 99
        assert product['min_order_quantity'] >= 1
        assert str(product["id"]) in product[
            'slug'], f'商品{product["id"]}这个链接{product["slug"]}返回的不是这个产品的链接，请确认~'
        assert product['product_properties'] is not None, f'商品{product["id"]}产地、数量信息返回为空，请确认{product}'
        # 有些商品的description就是为空，取决于商品的配置，与陈磊讨论过，此项为非必填项
        # assert product['description'] is not None, f'商品{product["id"]}描述信息返回为空，请确认{product}'
        # assert product['description_html'] is not None, f'商品{product["id"]}描述信息返回为空，请确认{product}'
        # assert product['unit_info_content'] is not None, f'商品{product["id"]}单位价返回空，请确认{product}'
        assert product['product_area_info'] is not None, f'商品{product["id"]}产地返回空，请确认{product}'
        if product["additionalMap"] is not None:
            if product.get("additionalMap").get("warning"):
                assert product["additionalMap"][
                           "warning"] is not None, f'商品{product["id"]} pdp 警告内容返回空，请确认{product}'
                assert product["additionalMap"]["warning"][
                           "content"] is not None, f'商品{product["id"]} pdp 警告内容返回空，请确认{product}'
                assert product["additionalMap"]["warning"][
                           "title"] is not None, f'商品{product["id"]} pdp 警告内容返回空，请确认{product}'
                assert product["additionalMap"]["warning"][
                           "type"] == "warning", f'商品{product["id"]} pdp 警告内容返回空，请确认{product}'
                assert "www.P65Warnings" in product["additionalMap"]["warning"]["content"]
            elif product.get("additionalMap").get("disclaimer"):
                assert product["additionalMap"][
                           "disclaimer"] is not None, f'商品{product["id"]} pdp 声明内容返回空，请确认{product}'
                assert product["additionalMap"]["disclaimer"][
                           "content"] is not None, f'商品{product["id"]} pdp 声明内容返回空，请确认{product}'
                assert product["additionalMap"]["disclaimer"][
                           "title"] is not None, f'商品{product["id"]} pdp 声明内容返回空，请确认{product}'
                assert product["additionalMap"]["disclaimer"][
                           "type"] == "normal", f'商品{product["id"]} pdp 声明内容返回空，请确认{product}'
                assert product["additionalMap"]["disclaimer"][
                           "brief_html"] is not None, f'商品{product["id"]} pdp 声明内容返回空，请确认{product}'
            elif product.get("additionalMap").get("return_policy"):
                assert product["additionalMap"][
                           "return_policy"] is not None, f'商品{product["id"]} pdp 退款政策内容返回空，请确认{product}'
                assert product["additionalMap"]["return_policy"][
                           "content"] is not None, f'商品{product["id"]} pdp 退款政策内容返回空，请确认{product}'
                assert product["additionalMap"]["return_policy"][
                           "title"] is not None, f'商品{product["id"]} pdp 退款政策内容返回空，请确认{product}'
                assert product["additionalMap"]["return_policy"][
                           "type"] == "normal", f'商品{product["id"]} pdp 退款政策内容返回空，请确认{product}'
                assert product["additionalMap"]["return_policy"][
                           "brief_html"] is not None, f'商品{product["id"]} pdp 退款政策内容返回空，请确认{product}'

        if product["biz_type"] == "seller":

            assert product["seller_id"] is not None, f'商品{product["id"]}seller_id返回空，请确认{product}'
            assert product["is_mkpl"] is True, f'商品{product["id"]}产地返回空，请确认{product}'
            assert product["vender_info_view"] is not None, f'pdp店铺信息获取异常，请确认{product}'
            assert product["vender_info_view"]["descriptions"][0][
                       "title"] is not None, f'pdp店铺信息获取异常，请确认{product}'
            assert product["vender_info_view"]["vender_logo_url"] is not None, f'pdp店铺信息获取异常，请确认{product}'

            seller_free_threshold = product["vender_info_view"]["free_threshold"]
            price = product["price"]
            product_id = product["id"]
            min_order_quantity = product["min_order_quantity"]
            label_list = product["label_list"]
            total_price = price * min_order_quantity
            # 如果商家的免运费门槛设置的是$0 则默认该seller的商品都返回一件包邮key
            if seller_free_threshold == 0:
                assert any(
                    item["label_key"] == "free_shipping" for item in label_list), f'pdp信息获取异常，请确认{product}'
            # 断言Global+商品的price * min qty ≥ 商家免运费门槛时，PDP需要展示一件包邮label 如果不满足条件，label list里不可包含这些信息
            else:
                if total_price >= seller_free_threshold:
                    assert any(
                        item["label_key"] == "free_shipping" for item in label_list), f'pdp信息获取异常，请确认{product}'
                else:
                    assert all(item["label_key"] != "free_shipping" for item in
                               label_list), f"'free_shipping' label key 不应该返回.请确认{product}"

            # 调用活动接口 判端商品是否有sku coupon信息
            global_product_promotion = QueryPromotionInfo().query_promotion_product(
                headers=headers, product_id=product_id)
            sku_coupon = global_product_promotion['object']['coupons']

            if len(sku_coupon) > 0:
                coupon_status = global_product_promotion['object']['coupons'][0]['coupon_status']
                plan_id = global_product_promotion['object']['coupons'][0]['plan_id']
                if coupon_status != "can_claim":
                    print(product, f"不满足领取条件,sku coupon数据请查看：{global_product_promotion}", )

                else:
                    sku_coupon_claimed_detail = QueryPromotionInfo().promotion_mkpl_coupon_obtain(
                        headers=headers, plan_id=plan_id, vendor_id=product["seller_id"])
                    sku_coupon_plan_id = sku_coupon_claimed_detail['object']['coupon_plan_id']
                    sku_coupon_code = sku_coupon_claimed_detail['object']['code']
                    sku_coupon_status = sku_coupon_claimed_detail['object']['status']

                    assert sku_coupon_plan_id is not None and isinstance(sku_coupon_plan_id,
                                                                         int), f"{product} sku_coupon_plan_id 返回异常，请检查{global_product_promotion}"
                    assert sku_coupon_code is not None and isinstance(sku_coupon_code,
                                                                      str), f"{product} sku_coupon_code 返回异常，请检查{global_product_promotion}"
                    assert sku_coupon_status == "A", f"{product} sku_coupon_status 状态返回异常，请检查{global_product_promotion}"

                    my_coupon_list = Couponinfo().get_coupon_info(headers=headers, status="A")
                    active_coupon_list = my_coupon_list['object']['coupon_list']

                    for coupon in active_coupon_list:
                        if coupon['code'] == sku_coupon_plan_id:
                            assert coupon['coupon_plan_id'] == sku_coupon_plan_id and isinstance(
                                coupon['coupon_plan_id'], int)
                            assert coupon['code'] == sku_coupon_code and isinstance(coupon['coupon_plan_id'], str)

                        else:
                            print(product, "no sku coupon matches my coupon list:", global_product_promotion)

                for items in sku_coupon:
                    assert coupon_status is not None and isinstance(coupon_status,
                                                                    str), f"{product}sku coupon状态返回异常，请检查{global_product_promotion}"
                    assert items['plan_id'] is not None and isinstance(items['plan_id'],
                                                                       int), f"{product}sku coupon plan_id返回异常，请检查{global_product_promotion}"
                    assert items['type'] is not None and isinstance(items['type'],
                                                                    str), f"{product}sku coupon type返回异常，请检查{global_product_promotion}"
                    assert items['coupon_scope'] == 'skus'
                    assert items['vendor_id'] is not None and isinstance(items['vendor_id'],
                                                                         int), f"{product}sku coupon vendor_id返回异常，请检查{global_product_promotion}"
                    assert items['icon_url'] is not None and isinstance(items['icon_url'],
                                                                        str), f"{product}sku coupon icon_url返回异常，请检查{global_product_promotion}"
                    assert items['title'] is not None and isinstance(items['title'],
                                                                     str), f"{product}sku coupon title返回异常，请检查{global_product_promotion}"
                    assert items['title_color'] is not None and isinstance(items['title_color'],
                                                                           str), f"{product}sku coupon title_color返回异常，请检查{global_product_promotion}"
                    assert items['pdp_sub_title_html'] is not None and isinstance(items['pdp_sub_title_html'],
                                                                                  str), f"{product}sku coupon pdp_sub_title_html返回异常，请检查{global_product_promotion}"
                    assert str(plan_id) in items[
                        'direction_url'], f"{global_product_promotion} The direction_url '{items['direction_url']}' does not contain the plan_id 'items['plan_id'],请检查{product}"
                    assert items['relate_products_total_count'] is not None and isinstance(
                        items['relate_products_total_count'],
                        int), f"{product}sku coupon relate_products_total_count返回异常，请检查{global_product_promotion}"

            else:
                assert global_product_promotion['result'] is True
                assert global_product_promotion['message_id'] == "10000"

        # local fbw 商品
        elif product["biz_type"] == "fbw":
            assert product["seller_id"] is not None, f'商品{product["id"]}seller_id返回空，请确认{product}'
            assert product["policy_show"] is True, f'商品{product["id"]}每日现做栏返回空，请确认{product}'
            assert product["policy_title"] is not None, f'商品{product["id"]}每日现做栏返回空，请确认{product}'
            assert product[
                       "policy_icon_title"] is not None, f'商品{product["id"]}每日现做icon文案返回空，请确认{product}'
            assert product[
                       "policy_pop_config_key"] == "fresh_guarantee_config_fbw", f'商品{product["id"]}pdp页面每日现做栏返回空，请确认{product}'
            if category == "freshbakery":
                assert "/mkpl/bakery/landing" in product[
                    "policy_url"], f'商品{product["id"]}pdp页面每日现做栏位跳转链接不对，请确认{product}'

            elif category == "freshgourmet":
                assert "/mkpl/freshdeli/landing" in product[
                    "policy_url"], f'商品{product["id"]}pdp页面每日现做栏位跳转链接不对，请确认{product}'
            # 验证点击pdp FBW转正常
            CommCheckFunction().comm_check_link(product["policy_url"], headers=headers)
        elif product["biz_type"] == "normal":
            assert product['description'] is not None, f'商品{product["id"]}描述信息返回为空，请确认{product}'
            assert product['description_html'] is not None, f'商品{product["id"]}描述信息返回为空，请确认{product}'
            # 生鲜商品
            if category == "green" and filters == "delivery":
                assert product[
                           "policy_pop_config_key"] == "fresh_guarantee_config", f'商品{product["id"]}新鲜度保障返回空，请确认{product}'
            # cold_pack 商品
            elif category == "sale" and filters == "cold_pack":
                # assert len(product["selling_points"]) > 0, f'商品{product["id"]}卖点返回空，请确认{product}'
                assert product["package_tip"] is not None, f'商品{product["id"]}数据异常，请确认{product}'
                assert product["is_colding_package"] is True, f'商品{product["id"]}数据异常，请确认{product}'
                assert product[
                           "policy_pop_config_key"] == "fresh_guarantee_config", f'商品{product["id"]}新鲜度保证返回为空，请确认{product}'
            # pantry 商品
            elif category == "sale" and filters == "pantry":
                assert product["is_pantry"] is True, f'商品{product["id"]}数据异常，请确认{product}'
            # global+ 商品
            elif category == "sale" and filters == "global":
                assert product["is_mkpl"] is True, f'商品{product["id"]}数据异常，请确认{product}'
                assert product["vender_info_view"] is not None, f'pdp店铺信息获取异常，请确认{product}'
                assert product["vender_info_view"]["descriptions"][0][
                           "title"] is not None, f'pdp店铺信息获取异常，请确认{product}'
                assert product["vender_info_view"][
                           "vender_logo_url"] is not None, f'pdp店铺信息获取异常，请确认{product}'
            elif category == "alcohol":
                assert product["item_type"] == "alcohol", f'商品{product["id"]}数据异常，请确认{product}'
            elif category == "others":
                pass

            # assert product["additionalMap"]["return_policy"]["content"] is not None,f'global+ pdp 没有返回退款政策，请确认{product}'
            # assert product["additionalMap"]["return_policy"]["title"] is not None,f'global+ pdp 没有返回退款政策，请确认{product}'
            # assert product["additionalMap"]["return_policy"]["key"] == "return_policy",f'global+ pdp 没有返回退款政策，请确认{product}'
        elif product["biz_type"] == "mkpl_fbw":
            # global fbw 商品
            if category == "global_fbw":
                assert product[
                           "fullfillment_info"] is not None, f'global+fbw 商品{product["id"]}配送信息返回空，请确认{product}'
                assert product["fullfillment_info"][
                           "icon"] is not None, f'global+fbw 商品{product["id"]}配送信息返回空，请确认{product}'
                assert product["fullfillment_info"][
                           "sub_title_html"] is not None, f'global+fbw 商品{product["id"]}配送信息返回空，请确认{product}'
                assert product["fullfillment_info"][
                           "title_html"] is not None, f'global+fbw 商品{product["id"]}配送信息返回空，请确认{product}'
                assert product["seller_id"] is not None, f'商品{product["id"]}seller_id返回空，请确认{product}'
                assert product['description'] is not None, f'商品{product["id"]}描述信息返回为空，请确认{product}'
                assert product['description_html'] is not None, f'商品{product["id"]}描述信息返回为空，请确认{product}'
                assert product["vender_info_view"] is not None, f'pdp店铺信息获取异常，请确认{product}'
                assert product["vender_info_view"]["delivery_desc"] is not None, f'pdp店铺信息获取异常，请确认{product}'
                assert product["vender_info_view"][
                           "delivery_full_desc"] is not None, f'pdp店铺信息获取异常，请确认{product}'
                assert product["vender_info_view"]["eta_range_desc"] is not None, f'pdp店铺信息获取异常，请确认{product}'
                assert product["vender_info_view"]["descriptions"][0][
                           "title"] is not None, f'pdp店铺信息获取异常，请确认{product}'
                assert product["vender_info_view"]["descriptions"][0][
                           "title_full"] is not None, f'pdp店铺信息获取异常，请确认{product}'
                assert product["vender_info_view"][
                           "vender_logo_url"] is not None, f'pdp店铺信息获取异常，请确认{product}'

        # 酒 商品

        if len(product["label_list"]) > 0:
            pass
        # pdp 品牌/topx 入口
        if product["barInfoModules"]:
            for item in product["barInfoModules"]:
                if item["module_type"] == "brand":
                    # 普通商品品牌
                    if item["key"] == "brand":
                        assert item["more_link"], f"more_link 不能为空"
                        # 定义两种可能的链接模式
                        # pattern1 = r"^https://www\.sayweee\.com/brand/detail/"+product["brand_slug"]+[^/]+/[^/]+$"
                        # pattern2 = r"^https://www\.sayweee\.com/en/cms/page/brand/[^/]+$"
                        #
                        # # 使用正则表达式匹配
                        # match1 = re.match(pattern1, more_link)
                        # match2 = re.match(pattern2, more_link)
                        #
                        # # 断言 more_link 匹配其中一种模式
                        # assert match1 or match2, f"more_link 格式不正确: {more_link}"

                        assert any(link in item["more_link"] for link in ["/cms/page/brand/",
                                                                          "/brand/detail/" + product[
                                                                              "brand_slug"] + "/" + product[
                                                                              "brand_key"]]), f'商品{product["id"]}请确认~'
                    elif item["key"] == "fresh_deli":
                        assert "/mkpl/vendor/" + str(product["seller_id"]) in item[
                            "more_link"], f'商品{product["id"]}请确认~'
                    elif item["key"] == "bakery":
                        assert "/mkpl/vendor/" + str(product["seller_id"]) in item[
                            "more_link"], f'商品{product["id"]}请确认~'
                elif item["module_type"] == "top_ranking":
                    assert "/promotion/top-x/" + item["key"] in item["more_link"], f'商品{product["id"]}请确认~'

                assert item["icon"], f'商品{product["id"]}请确认~'
                assert item["info_html"], f'商品{product["id"]}请确认~'
                assert item["more_link"], f'商品{product["id"]}请确认~'
        # 品牌
        if product['brand_key'] is not None:
            assert product['brand_img'] is not None, f'当前商品{product["id"]}pdp没有返回品牌入口，请确认{product}'
            assert product['brand_name'] is not None, f'当前商品{product["id"]}pdp没有返回品牌入口，请确认{product}'
            assert product['brand_slug'] is not None, f'当前商品{product["id"]}pdp没有返回品牌入口，请确认{product}'
            assert product['brand_link_url'] is not None, f'当前商品{product["id"]}pdp没有返回品牌入口，请确认{product}'
            # 验证品牌跳转正常
            CommCheckFunction().comm_check_link(product['brand_link_url'], headers=headers)
            if "/cms/page/brand" not in product['brand_link_url']:
                assert product['brand_key'] in product[
                    "brand_link_url"], f'商品{product["id"]}品牌链接返回有误{product["brand_link_url"]}'
            assert product['barInfoModules'] is not None, f'当前商品{product["id"]}pdp没有返回品牌入口，请确认{product}'

    def promotion_product_assertion(self, product, headers):
        # 获取商品活动信息
        promotions = QueryPromotionInfo().query_promotion_product(headers=headers,
                                                                  product_id=product["id"])
        if len(promotions["object"]["promotions"]) > 0:
            for promotion in promotions["object"]["promotions"]:
                if promotion["hot_deal"] is True:
                    # 劲爆价
                    assert promotion[
                               "promote_tip"] is not None, f'{product["id"]}pdp 活动信息返回异常，请确认{promotions}'
                    assert promotion[
                               "promote_title"] is not None, f'{product["id"]}pdp 活动信息返回异常，请确认{promotions}'

                else:
                    # 有活动
                    # assert promotion["type"] == "promo_gift", f'{product["id"]}pdp 活动信息返回异常，请确认{promotions}'
                    assert promotion["promote_tip"] == "Promotion", f'{product["id"]}pdp 活动信息返回异常，请确认{promotions}'
                    assert "/promotion/free-gift/landing" in promotion[
                        "use_url"], f'pdp 活动信息返回异常，请确认{promotions}'
                    assert promotion[
                               "promote_icon"] is not None, f'{product["id"]}pdp 活动信息返回异常，请确认{promotions}'
                    assert promotion[
                               "promote_title"] is not None, f'{product["id"]}pdp 活动信息返回异常，请确认{promotions}'

    def product_group_assertion(self, headers, product_id, group: dict | Any):
        # 对分类返回结果商品操作及断言
        assert group['propertyList'] is not None, f'商品{product_id}返回product_group为空，请确认'
        assert group['groupProductList'] is not None, f'商品{product_id}返回product_group为空，请确认'
        for propertylist in group['propertyList']:
            assert propertylist["property_id"] is not None, f'商品{product_id}返回product_group为空，请确认'
            assert propertylist["property_key"] is not None, f'商品{product_id}返回product_group为空，请确认'
            assert propertylist["property_name"] is not None, f'商品{product_id}返回product_group为空，请确认'
            assert propertylist["priority"] is not None, f'商品{product_id}返回product_group为空，请确认'
            assert propertylist["property_value_list"] is not None, f'商品{product_id}返回product_group为空，请确认'
            for property_value_list in propertylist["property_value_list"]:
                assert property_value_list[
                           "property_value_id"] is not None, f'商品{product_id}返回product_group为空，请确认'
                assert property_value_list[
                           "property_value"] is not None, f'商品{product_id}返回product_group为空，请确认'
                assert property_value_list["image_url"] is not None, f'商品{product_id}返回product_group为空，请确认'
                assert property_value_list["priority"] is not None, f'商品{product_id}返回product_group为空，请确认'
                assert property_value_list["suggest"] is True, f'商品{product_id}返回product_group为空，请确认'

            assert propertylist["property_type"] is not None, f'商品{product_id}返回product_group为空，请确认'
            assert propertylist["is_primary"] is not None, f'商品{product_id}返回product_group为空，请确认'

        for groupproductlist in group['groupProductList']:
            assert groupproductlist["product_id"] is not None, f'商品{product_id}返回product_group为空，请确认'
            assert groupproductlist["view_link"] is not None, f'商品{product_id}返回product_group为空，请确认'
            assert groupproductlist["price"] is not None, f'商品{product_id}返回product_group为空，请确认'
            assert groupproductlist["property_value_ids"] is not None, f'商品{product_id}返回product_group为空，请确认'
            # 点击product group里的商品进入pdp
            CommCheckFunction().comm_check_pdp_link(groupproductlist["product_id"], groupproductlist["view_link"],
                                                    headers=headers)

    def vender_info_assertion(self, vender_id, product_id, deal_date, source, headers):
        # 获取pdp店铺信息
        venders_res = RecommendVender().recommend_vender(headers=headers,
                                                         vender_id=vender_id,
                                                         filter_product_id=product_id
                                                         )
        assert venders_res["object"] is not None, f'{product_id}pdp店铺信息获取异常，请确认{venders_res["object"]}'
        assert len(
            venders_res["object"]["products"]) > 0, f'{product_id}pdp店铺信息获取异常，请确认{venders_res["object"]}'
        assert venders_res["object"][
                   "vender_info_view"] is not None, f'{product_id}pdp店铺信息获取异常，请确认{venders_res["object"]}'
        assert venders_res["object"]["vender_info_view"][
                   "vender_id"] == vender_id, f'{product_id}pdp店铺信息获取异常，请确认{venders_res["object"]}'
        for item in venders_res["object"]["products"]:
            assert item["biz_type"] == "seller", f'{product_id}pdp店铺信息获取异常，请确认{venders_res["object"]}'
            assert item["vender_id"] == vender_id, f'{product_id}pdp店铺信息获取异常，请确认{venders_res["object"]}'

        CommCheckProductsWithCart().products_add_to_cart(headers=headers,
                                                         products=venders_res["object"]["products"],
                                                         porder_deal_date=deal_date,
                                                         product_source=source)

    def products_modules_assertion(self, headers, product, deal_date, category: str = None):

        # 获取pdp相关商品,并加购
        products_modules = GetProductsModules().get_products_modules(headers=headers,
                                                                     product_id=product["id"])

        if product["sold_status"] == "available":
            assert len(products_modules["object"][
                           "modules"]) > 0, f'商品：{product["id"]}未返回相关商品+推荐搭配，请确认{products_modules["object"]}'
        modules = products_modules["object"]["modules"]

        for module in modules:
            assert module["title"] is not None

            if module["module_key"] == "related":
                # 如果是fbw 商品，相关商品的查看更多链接跳转对应landing页面
                if category == "freshbakery":
                    assert "/mkpl/bakery/landing" in module[
                        "more_link"], f'商品：{product["id"]} 查看全部链接不对，module={module}, 请确认'
                    # 访问查看更多
                    CommCheckFunction().comm_check_link(view_link=module["more_link"], headers=headers)
                elif category == "freshgourmet":
                    assert "/mkpl/freshdeli/landing" in module[
                        "more_link"], f'商品：{product["id"]} 查看全部链接不对，module={module}, 请确认'
                    # 访问查看更多
                    CommCheckFunction().comm_check_link(view_link=module["more_link"], headers=headers)
                # 加购
                CommCheckProductsWithCart().products_add_to_cart(headers=headers,
                                                                 products=module["product_list"],
                                                                 porder_deal_date=deal_date,
                                                                 product_source="mweb_product-related-" + str(
                                                                     product["id"])
                                                                 )

            elif module["module_key"] == "buytogether":
                # 加购
                CommCheckProductsWithCart().products_add_to_cart(headers=headers,
                                                                 products=module["product_list"],
                                                                 porder_deal_date=deal_date,
                                                                 product_source="mweb_product-buytogether-" + str(
                                                                     product["id"])
                                                                 )
            elif module["module_key"] == "often_paired_with":
                # 加购
                CommCheckProductsWithCart().products_add_to_cart(headers=headers,
                                                                 products=module["product_list"],
                                                                 porder_deal_date=deal_date,
                                                                 product_source="mweb_product-often_paired_with"
                                                                 )

    def buy_together_assertion(self, product, deal_date, headers):

        # 获取pdp推荐搭配商品
        buy_together = GetBuyTogetherProducts().get_buy_together_products(headers=headers,
                                                                          product_id=product["id"])
        # 获取pdp推荐搭配商品,并加购
        assert buy_together["object"]["total_count"] > 0, f'商品未返回推荐搭配，请确认{buy_together["object"]}'
        products = buy_together["object"]["products"]
        # 加购
        CommCheckProductsWithCart().products_add_to_cart(headers=headers,
                                                         products=buy_together["object"]["products"],
                                                         porder_deal_date=deal_date,
                                                         product_source="mweb_product-buytogether-" + str(product["id"])
                                                         )

    def products_related_assertion(self, product, deal_date, headers):
        # 获取pdp猜你相似商品
        products_related = GetProductsRelated().get_products_related(headers=headers,
                                                                     product_id=product["id"])

        # 获取pdp相似商品商品,并加购
        assert products_related["object"]["total_count"] > 0, f'商品未返回相似搭配，请确认{products_related["object"]}'
        # 加购
        CommCheckProductsWithCart().products_add_to_cart(headers=headers,
                                                         products=products_related["object"]["products"],
                                                         porder_deal_date=deal_date,
                                                         product_source="mweb_product-related-" + str(product["id"])
                                                         )

    def modules_second_assertion(self, product, deal_date, headers):

        # pdp 最近浏览模块"""
        modules_second = GetProductsModulesSecond().get_products_modules_second(headers=headers,
                                                                                product_id=product["id"]
                                                                                )
        # 获取pdp最近浏览商品,并加购
        assert modules_second["object"] is not None, f'商品未返回最近浏览，请确认{modules_second["object"]}'
        assert len(modules_second["object"]["modules"]) > 0, f'商品未返回最近浏览，请确认{modules_second["object"]}'
        modules = modules_second["object"]["modules"]
        for module in modules:
            assert module["title"] is not None
            assert module["module_key"] == "view_history"
            # 加购
            CommCheckProductsWithCart().products_add_to_cart(headers=headers,
                                                             products=module["product_list"],
                                                             porder_deal_date=deal_date,
                                                             product_source="mweb_product-view_history"
                                                             )

    def pdp_share_assertion(self, share: dict | Any):
        for content in share["share_content"]:
            assert content["share_img_url"] is not None, f'pdp 分享信息返回异常，请确认{share}'
            assert content["title"] is not None, f'pdp 分享信息返回异常，请确认{share}'
            assert content["link_url"] is not None, f'pdp 分享信息返回异常，请确认{share}'
            assert content["description"] is not None, f'pdp 分享信息返回异常，请确认{share}'
            assert content["language"] is not None, f'pdp 分享信息返回异常，请确认{share}'
        # 返利联盟文案
        assert share["share_tips"] is not None, f'pdp 分享信息返回异常，请确认{share}'

        assert share["show_language"] is True, f'pdp 分享信息返回异常，请确认{share}'
        assert len(share["share_channels"]) > 0, f'pdp 分享信息返回异常，请确认{share}'
        assert "copyLink" in share["share_channels"], f'pdp 分享信息返回异常，请确认{share}'
        assert "saveImage" in share["share_channels"], f'pdp 分享信息返回异常，请确认{share}'
        assert "/product/share_view" in share["view_link"], f'pdp 分享信息返回异常，请确认{share}'

    def pdp_post_video(self, product_id, headers):

        pdp_video = PostInfo().post_pdp_video(headers=headers,
                                              product_id=product_id, type="video")
        assert pdp_video["object"] is not None, f'pdp{product_id} 视频返回数据异常，请确认{pdp_video}'

        # 点击查看更多跳转社区视频
        assert "/review/promotion/pdp-post-explore-more" in pdp_video["object"]["explore_more_url"]
        CommCheckFunction().comm_check_link(pdp_video["object"]["explore_more_url"], headers=headers)

    def pdp_post_review(self, product_id, headers):
        pdp_review = ReviewInfo().query_review_list(headers=headers, product_id=product_id)
        assert pdp_review["result"] is True, f'pdp {product_id}晒单数据返回异常，请确认{pdp_review["object"]}'
        assert pdp_review["object"] is not None, f'pdp {product_id}晒单数据返回异常，请确认{pdp_review["object"]}'
        assert pdp_review["object"]["total"] > 0, f'pdp {product_id}晒单数据返回异常，请确认{pdp_review["object"]}'
        review_lists = pdp_review["object"]["list"]
        for review_list in review_lists:
            assert review_list[
                       "user_name"] is not None, f'pdp {product_id}晒单数据返回异常，请确认{pdp_review["object"]["list"]}'
            assert review_list[
                       "user_avatar"] is not None, f'pdp {product_id}晒单数据返回异常，请确认{pdp_review["object"]["list"]}'
            assert review_list[
                       "start_id"] is not None, f'pdp {product_id}晒单数据返回异常，请确认{pdp_review["object"]["list"]}'
            assert "/social/review/more" in review_list[
                "link"], f'pdp {product_id}晒单数据返回异常，请确认{pdp_review["object"]["list"]}'
            CommCheckFunction().comm_check_link(review_list["link"], headers=headers)
