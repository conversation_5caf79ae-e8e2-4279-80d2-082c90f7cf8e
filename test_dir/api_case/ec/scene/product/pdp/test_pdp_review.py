# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON> <PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import pytest
import weeeTest
from test_dir.api.ec.ec_social.post_info.post_info import PostInfo
from test_dir.api.ec.ec_social.review_info.review_info import ReviewInfo
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction
from test_dir.api_case.ec.common.common_check import CommonCheck
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder


class TestPdpReview(weeeTest.TestCase):
    @pytest.fixture(scope='class', autouse=True)
    def setup(self, ec_login_header):
        """
        前置条件 更新账号语言为en

        """
        global product_id
        product_id = 9819
        SetUserPorder().set_user_porder(headers=ec_login_header)

    @weeeTest.mark.list('pdp_review_product', 'Regression', 'Smoke', 'Transaction')
    def test_pdp_post_review(self, ec_login_header):
        """ Product-PDP晒单&视频验证流程 """
        # 每个分类下找了一个商品，给特定的商品
        products = [33580, 68991, 58583, 85163, 94525, 39334, 10727, 96942, 105529, 88108, 21293, 2802, 13762, 81842,
                    89996]
        # 获取商品PDP 信息
        for product in products:
            # 验证pdp review
            self.pdp_post_review(product_id=product, headers=ec_login_header)
    @weeeTest.mark.list('B2B', 'Regression', 'Smoke', 'Transaction')
    def test_b2b_pdp_post_review(self, ec_login_header):
        """ Product-PDP晒单&视频验证流程 """
        # 每个分类下找了一个商品，给特定的商品
        products = [105142,51261]
        # 获取商品PDP 信息
        for product in products:
            # 验证pdp review
            self.pdp_post_review(product_id=product, headers=ec_login_header)

    @weeeTest.mark.list('pdp_review_product', 'Regression', 'Transaction')
    def test_pdp_post_sns_review(self, ec_login_header):
        """ Product-PDP晒单POP验证流程 """
        # 每个分类下找了一个商品，给特定的商品
        # 获取商品PDP 信息
        # 验证pdp review
        pdp_review = ReviewInfo().query_sns_review_list(headers=ec_login_header)
        assert pdp_review["result"] is True, f'pdp晒单数据返回异常，请确认{pdp_review["object"]}'
        assert pdp_review["object"] is not None, f'pdp {product_id}晒单数据返回异常，请确认{pdp_review["object"]}'
        assert pdp_review["object"]["total"] > 0, f'pdp {product_id}晒单数据返回异常，请确认{pdp_review["object"]}'
        explore_more_url = pdp_review["object"]["explore_more_url"]

        top_reviews = pdp_review["object"]["top_reviews"]
        _list = pdp_review["object"]["list"]
        assert len(_list) <= 6, f'pdp {product_id}晒单数据返回异常，请确认{pdp_review}'
        assert explore_more_url is not None, f'pdp {product_id}晒单数据返回异常，请确认{pdp_review}'
        assert "/social/review/more/" + str(
            product_id) in explore_more_url, f'pdp {product_id}晒单数据返回异常，请确认{pdp_review}'
        for item in top_reviews:
            assert item["comment"] is not None, f'pdp {product_id}晒单数据返回异常{pdp_review["object"]}'
            assert item["link"] is not None, f'pdp {product_id}晒单数据返回异常{pdp_review["object"]}'
            # assert item["title"] is not None, f'pdp {product_id}晒单数据返回异常{pdp_review["object"]}'
            assert item["user_name"] is not None, f'pdp {product_id}晒单数据返回异常{pdp_review["object"]}'
            assert item["user_id"] is not None, f'pdp {product_id}晒单数据返回异常{pdp_review["object"]}'
            assert item["user_avatar"] is not None, f'pdp {product_id}晒单数据返回异常{pdp_review["object"]}'

    # @weeeTest.mark.list('pdp_review_product', 'Regression', 'Transaction')
    def test_pdp_post_ai_review(self, ec_login_header):
        """ Product-PDP晒单AI卖点验证流程 """
        # 每个分类下找了一个商品，给特定的商品
        # 实验已关闭，去掉regression标签

        # 验证pdp AI review
        pdp_review = ReviewInfo().post_ai_review(headers=ec_login_header)
        assert pdp_review["result"] is True, f'pdp {product_id}晒单数据返回异常，请确认{pdp_review["object"]}'
        assert pdp_review["object"] is not None, f'pdp {product_id}晒单数据返回异常，请确认{pdp_review["object"]}'
        ai_sell_points = pdp_review["object"]["ai_sell_points"]
        ai_summary = pdp_review["object"]["ai_summary"]
        if ai_summary is not None:
            assert ai_summary[
                       "description"] is not None, f'pdp {product_id}晒单数据返回异常，请确认{pdp_review["object"]}'
            assert ai_summary["tip"] is not None, f'pdp {product_id}晒单数据返回异常，请确认{pdp_review["object"]}'
            assert ai_summary["title"] is not None, f'pdp {product_id}晒单数据返回异常，请确认{pdp_review["object"]}'
            assert ai_summary[
                       "tip"] == "AI-generated from the text of customer reviews", f'pdp {product_id}晒单数据返回异常，请确认{pdp_review["object"]}'
            assert ai_summary[
                       "title"] == "Customers say", f'pdp {product_id}晒单数据返回异常，请确认{pdp_review["object"]}'
        if len(ai_sell_points) > 0:
            for item in ai_sell_points:
                assert item["count"] > 0, f'pdp {product_id}晒单数据返回异常，请确认{pdp_review["object"]}'
                assert item["count_label"] == str(
                    item["count"]), f'pdp {product_id}晒单数据返回异常，请确认{pdp_review["object"]}'
                assert item["link"] is not None, f'pdp {product_id}晒单数据返回异常，请确认{pdp_review["object"]}'
                assert item["usp_id"] is not None, f'pdp {product_id}晒单数据返回异常，请确认{pdp_review["object"]}'
                assert item["word"] is not None, f'pdp {product_id}晒单数据返回异常，请确认{pdp_review["object"]}'
                assert "/social/review/more/" + str(product_id) + "?usp_id=" + str(item["usp_id"]) in item[
                    "link"], f'pdp {product_id}晒单数据返回异常，请确认{pdp_review["object"]}'

    @weeeTest.mark.list('pdp_review_product', 'Regression', 'Transaction')
    def test_pdp_review_privilege(self, ec_login_header):
        """ Product-PDP review pop 去晒单按钮验证流程 """
        pdp_review = ReviewInfo().review_privilege(headers=ec_login_header)
        assert pdp_review["result"] is True, f'pdp {product_id}晒单数据返回异常，请确认{pdp_review["object"]}'
        if pdp_review["object"]["show"] is True:
            assert pdp_review["object"]["order_id"] != 0, f'pdp {product_id}晒单数据返回异常，请确认{pdp_review}'
        else:
            assert pdp_review["object"]["order_id"] == 0, f'pdp {product_id}晒单数据返回异常，请确认{pdp_review}'

    @weeeTest.mark.list('pdp_review_product', 'Regression', 'Smoke', 'Transaction')
    def test_pdp_post_video(self, ec_login_header):
        """ Product-PDP视频验证流程 """
        # 每个分类下找了一个商品，给特定的商品
        # 获取商品PDP 信息
        # 验证pdp 视频
        pdp_video = PostInfo().post_pdp_video(headers=ec_login_header,
                                              product_id=product_id, type="video")
        assert pdp_video["object"] is not None, f'pdp{product_id} 视频返回数据异常，请确认{pdp_video}'
        assert pdp_video["object"]["total"] > 0, f'pdp {product_id}晒单数据返回异常，请确认{pdp_video}'

        explore_more_url = pdp_video["object"]["explore_more_url"]
        categories = pdp_video["object"]["categories"]
        _list = pdp_video["object"]["list"]
        pop_button = pdp_video["object"]["pop_button"]
        pop_title = pdp_video["object"]["pop_title"]
        pop_description = pdp_video["object"]["pop_description"]
        assert pop_button is not None, f'pdp {product_id}晒单数据返回异常，请确认{pdp_video}'
        assert pop_title is not None, f'pdp {product_id}晒单数据返回异常，请确认{pdp_video}'
        assert pop_description is not None, f'pdp {product_id}晒单数据返回异常，请确认{pdp_video}'
        for item in pop_description:
            assert item is not None, f'pdp {product_id}晒单数据返回异常，请确认{pdp_video}'

        if len(categories) > 0:
            for item in categories:
                assert item["label"] is not None, f'pdp {product_id}晒单数据返回异常，请确认{pdp_video}'
                assert item["num"] is not None, f'pdp {product_id}晒单数据返回异常，请确认{pdp_video}'
        # 点击查看更多跳转社区视频
        assert "/social/promotion/pdp-post-explore-more" in explore_more_url, f'pdp {product_id}晒单数据返回异常，请确认{pdp_video}'
        CommCheckFunction().comm_check_link(explore_more_url, headers=ec_login_header)
        # 每个帖子分类下的视频
        for index, post in enumerate(_list):
            CommonCheck().check_video_data(video_list=post, headers=ec_login_header)
            if index == 5:
                break

    @weeeTest.mark.list('B2B', 'Regression', 'Smoke', 'Transaction')
    def test_b2b_pdp_post_video(self, ec_login_header):
        """ Product-PDP视频验证流程 """
        # 每个分类下找了一个商品，给特定的商品
        # 获取商品PDP 信息
        # 验证pdp 视频
        pdp_video = PostInfo().post_pdp_video(headers=ec_login_header,
                                              product_id=27605, type="video")
        assert pdp_video["object"] is not None, f'pdp{product_id} 视频返回数据异常，请确认{pdp_video}'
        assert pdp_video["object"]["total"] > 0, f'pdp {product_id}晒单数据返回异常，请确认{pdp_video}'

        explore_more_url = pdp_video["object"]["explore_more_url"]
        categories = pdp_video["object"]["categories"]
        _list = pdp_video["object"]["list"]
        pop_button = pdp_video["object"]["pop_button"]
        pop_title = pdp_video["object"]["pop_title"]
        pop_description = pdp_video["object"]["pop_description"]
        assert pop_button is not None, f'pdp {product_id}晒单数据返回异常，请确认{pdp_video}'
        assert pop_title is not None, f'pdp {product_id}晒单数据返回异常，请确认{pdp_video}'
        assert pop_description is not None, f'pdp {product_id}晒单数据返回异常，请确认{pdp_video}'
        for item in pop_description:
            assert item is not None, f'pdp {product_id}晒单数据返回异常，请确认{pdp_video}'

        if len(categories) > 0:
            for item in categories:
                assert item["label"] is not None, f'pdp {product_id}晒单数据返回异常，请确认{pdp_video}'
                assert item["num"] is not None, f'pdp {product_id}晒单数据返回异常，请确认{pdp_video}'
        # 点击查看更多跳转社区视频
        assert "/social/promotion/pdp-post-explore-more" in explore_more_url, f'pdp {product_id}晒单数据返回异常，请确认{pdp_video}'
        CommCheckFunction().comm_check_link(explore_more_url, headers=ec_login_header)
        # 每个帖子分类下的视频
        for index, post in enumerate(_list):
            CommonCheck().check_video_data(video_list=post, headers=ec_login_header)
            if index == 5:
                break


    # 匿名用户不显示视频

    def pdp_post_review(self, product_id, headers):
        pdp_review = ReviewInfo().query_review_list(headers=headers, product_id=product_id)
        assert pdp_review["result"] is True, f'pdp {product_id}晒单数据返回异常，请确认{pdp_review["object"]}'
        assert pdp_review["object"] is not None, f'pdp {product_id}晒单数据返回异常，请确认{pdp_review["object"]}'
        assert pdp_review["object"]["total"] > 0, f'pdp {product_id}晒单数据返回异常，请确认{pdp_review["object"]}'
        review_lists = pdp_review["object"]["list"]
        for item in review_lists:
            CommonCheck().check_social_review_list(review_list=item, source="pdp", headers=headers)


