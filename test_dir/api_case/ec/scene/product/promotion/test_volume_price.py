# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import json

import weeeTest

from test_dir.api.ec.ec_item.search_v3.search_by_catalogue_content import SearchByCatalogueContent
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine
from test_dir.api_case.ec.common.comm_check_products_with_cart import CommCheckProductsWithCart
from test_dir.api_case.ec.common.remove_all_products_in_cart import RemoveAllProductsInCart


class TestVolumePrice(weeeTest.TestCase):

    @weeeTest.mark.list('volume_price_product', 'Regression', 'Smoke',  'Transaction')
    def test_volume_price_product(self, ec_login_header):
        """ Product-volume price商品验证流程 """
        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=ec_login_header)
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 从sale分类下找找到volume price 商品
        catalogue_content = SearchByCatalogueContent().search_by_catalogue_content(
            headers=ec_login_header, date=porder["delivery_pickup_date"],
            zipcode=porder["zipcode"], lang="en", filter_sub_category="sale",
            sort="recommend", filters=json.dumps({"delivery_type": "delivery_type_local"}),
            limit=60
        )
        # 使用列表推导式找到所有符合条件的项目
        volume_price_items = [
            item["data"] for item in catalogue_content["object"]["contents"]
            if item["data"].get("volume_price_support") is True and item["data"].get("sold_status") == "available"
        ]
        # print(volume_price_items)

        # 打印结果
        for item in volume_price_items:
            # 如果剩余量不足，就不会是volume price
            if item.get("volume_threshold") <= item.get("remaining_count"):
                # volume_price 是原价，price 才是折扣的价格
                volume_threshold = item["volume_threshold"]
                assert item["volume_price"] > item["price"]

                # 加购volume price 商品
                UpdatePreOrderLine().porder_items_v3(headers=ec_login_header,
                                                     product_id=item["id"],
                                                     biz_type=item["biz_type"],
                                                     date=porder["delivery_pickup_date"],
                                                     min_order_quantity=item["min_order_quantity"],
                                                     quantity=item["volume_threshold"],
                                                     is_pantry=item["is_pantry"],
                                                     is_mkpl=item["is_mkpl"],
                                                     refer_type="normal",
                                                     source="mweb_category-item_list-null",
                                                     volume_price_support=item["volume_price_support"])

                # 判断购物车且价格按照volume price 商品价格
                CommCheckProductsWithCart().check_product_exists_and_price(headers=ec_login_header,
                                                                           cart_domain="grocery",
                                                                           product_id=item["id"],
                                                                           quantity=item["volume_threshold"],
                                                                           price=item["price"]
                                                                           )
                # 减购volume price 商品
                UpdatePreOrderLine().porder_items_v3(headers=ec_login_header,
                                                     product_id=item["id"],
                                                     biz_type=item["biz_type"],
                                                     date=porder["delivery_pickup_date"],
                                                     min_order_quantity=item["min_order_quantity"],
                                                     quantity=item["min_order_quantity"],
                                                     is_pantry=item["is_pantry"],
                                                     is_mkpl=item["is_mkpl"],
                                                     refer_type="normal",
                                                     source="mweb_category-item_list-null",
                                                     volume_price_support=item["volume_price_support"])

                # 判断购物车价格不是volume price 价格
                CommCheckProductsWithCart().check_product_exists_and_price(headers=ec_login_header,
                                                                           cart_domain="grocery",
                                                                           product_id=item["id"],
                                                                           quantity=item["min_order_quantity"],
                                                                           price=item["volume_price"]
                                                                           )




