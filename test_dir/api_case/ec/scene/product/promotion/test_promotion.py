# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import json
import math
import time

import pytest
import weeeTest
from test_dir.api.ec.ec_promotion.promotion.query_promotion_info import QueryPromotionInfo
from test_dir.api.ec.ec_so.preorder.query_preorder import QueryPreOrder
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api_case.ec.common.categorycheck import CategoryCheck
from test_dir.api_case.ec.common.comm_check_products_with_cart import CommCheckProductsWithCart
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction
from test_dir.api_case.ec.common.remove_all_products_in_cart import RemoveAllProductsInCart
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder


class TestPromotion(weeeTest.TestCase):

    @pytest.fixture(scope='class', autouse=True)
    def setup(self, ec_login_header):
        # 切换测试销售组织地区
        SetUserPorder().set_user_zipcode(headers=ec_login_header, zipcode=99991)
        yield
        # 切回98011组织地区
        SetUserPorder().set_user_zipcode(headers=ec_login_header, zipcode="98011")

    @pytest.fixture(scope='function', autouse=True)
    def set(self, ec_login_header):
        # 确保程序执行通过，每次情况一下购物车数据
        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=ec_login_header)

    @weeeTest.mark.list('test_promotion_buy_x_pieces_get_y_free', 'Regression', 'Transaction')
    def test_promotion_buy_x_pieces_get_y_free(self, ec_login_header):
        """ Promotion-Buy X pieces get free item Y（加购5件canned分类商品赠送商品98192） """
        # 获取用户的porder
        gift_product_id = 98192
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 从canned分类下找找到商品
        normal_category = CategoryCheck().category_product(headers=ec_login_header, zipcode=99991,
                                                           deal_date=porder["delivery_pickup_date"],
                                                           filter_sub_category="canned",
                                                           filters=json.dumps({"delivery_type": "delivery_type_local"}),
                                                           )

        # 加购5件商品赠送商品98192

        # 使用列表推导式找到所有符合条件的项目
        promotion_items = [
            item["data"] for item in normal_category["object"]["contents"]
            if
            item["data"].get("sold_status") == "available" and item["data"].get("is_presale") is False and item[
                "data"].get("min_order_quantity") == 1
        ]
        # print(volume_price_items)

        # 打印结果
        for index, item in enumerate(promotion_items):
            # 加购商品
            # 加购
            CommCheckProductsWithCart().product_add_to_cart(
                headers=ec_login_header,
                product=item,
                porder_deal_date=porder["delivery_pickup_date"],
                product_source="mweb_category-item_list-null"
            )

            if index < 5:

                # 判断购物车加购成功
                CommCheckProductsWithCart().check_product_add_to_cart_success(headers=ec_login_header,
                                                                              cart_domain="grocery",
                                                                              product_id=item["id"]
                                                                              )
                # 判断购物车没有赠品 98192
                CommCheckProductsWithCart().check_product_add_to_cart_fail(headers=ec_login_header,
                                                                           cart_domain="grocery",
                                                                           product_id=gift_product_id
                                                                           )

            elif index > 5:

                # 判断购物车加购成功
                CommCheckProductsWithCart().check_product_add_to_cart_success(headers=ec_login_header,
                                                                              cart_domain="grocery",
                                                                              product_id=item["id"]
                                                                              )

                # 判断购物车有赠品 98192
                CommCheckProductsWithCart().check_product_add_to_cart_success(headers=ec_login_header,
                                                                              cart_domain="grocery",
                                                                              product_id=gift_product_id
                                                                              )

            elif index == 6:
                break

    @weeeTest.mark.list('Regression', 'Transaction', 'test_promotion_buy_x_dollar_get_y_free')
    def test_promotion_buy_x_dollar_get_y_free(self, ec_login_header):
        """ Promotion-Buy $X, get free item Y（加购满 $30 dried good分类商品赠送商品1528） """
        gift_product_id = 1528
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 从dried分类下找找到商品
        normal_category = CategoryCheck().category_product(headers=ec_login_header, zipcode=99991,
                                                           deal_date=porder["delivery_pickup_date"],
                                                           filter_sub_category="dried",
                                                           filters=json.dumps({"delivery_type": "delivery_type_local"}),
                                                           )

        promotion_items = [
            item["data"] for item in normal_category["object"]["contents"]
            if
            item["data"].get("sold_status") == "available" and item["data"].get("is_presale") is False and item[
                "data"].get("min_order_quantity") == 1
        ]
        # 查询购物车
        preorder_v5 = self.query_preorder_v5_with_retry(headers=ec_login_header,
                                                        cart_domain="grocery", products=promotion_items,
                                                        delivery_pickup_date=porder["delivery_pickup_date"]
                                                        )
        self.assert_cart_not_empty(preorder_v5)

        # 检查是否需要继续加购
        for section in preorder_v5["object"]["sections"]:
            if section["cart_id"] == "normal":

                sub_total_price = float(section["fee_info"]["sub_total_price"])
                if sub_total_price > 30.00:
                    # 如果商品金额大于30，有赠品 1528
                    self.check_product_add_to_cart_success(gift_product_id, headers=ec_login_header)
                    # 判断购物车promotion 提示信息返回
                    activity_info = section["activity_info"]
                    assert any(activity["original_type"] == "promo_gift" for activity in activity_info) is True
                    for activity in activity_info:
                        if activity["original_type"] == "promo_gift":
                            # 断言 赠品信息、tag 信息、文案 有返回
                            assert len(activity["gift_items"]) > 0, f'购物车接口{preorder_v5}'
                            assert activity["tag"] is not None, f'购物车接口{preorder_v5}'
                            assert activity["offer_content"] is not None, f'购物车接口{preorder_v5}'

                else:
                    # 如果商品金额小于30，没有赠品 1528，继续加购
                    self.add_products_to_cart_and_verify(products=promotion_items,
                                                         delivery_pickup_date=porder["delivery_pickup_date"], headers=ec_login_header)
                    self.check_product_add_to_cart_fail(gift_product_id, headers=ec_login_header)

    @weeeTest.mark.list('Regression', 'Transaction', 'test_promotion_buy_x_dollar_get_y_off')
    def test_promotion_buy_x_dollar_get_y_off(self, ec_login_header):
        """ Promotion-Buy $X, get Y% off（加购bakery分类商品满$40折扣10%） """
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 从bakery分类下找找到商品
        normal_category = CategoryCheck().category_product(headers=ec_login_header, zipcode=99991,
                                                           deal_date=porder["delivery_pickup_date"],
                                                           filter_sub_category="bakery",
                                                           filters=json.dumps({"delivery_type": "delivery_type_local"}),
                                                           )
        promotion_items = [
            item["data"] for item in normal_category["object"]["contents"]
            if
            item["data"].get("sold_status") == "available" and item["data"].get("is_presale") is False and item[
                "data"].get("min_order_quantity") == 1
        ]
        # 查询购物车
        preorder_v5 = self.query_preorder_v5_with_retry(headers=ec_login_header,
                                                        cart_domain="grocery", products=promotion_items,
                                                        delivery_pickup_date=porder["delivery_pickup_date"]
                                                        )
        self.assert_cart_not_empty(preorder_v5)

        # 检查是否需要继续加购
        for section in preorder_v5["object"]["sections"]:
            if section["cart_id"] == "normal":
                activity_info = section["activity_info"]
                # 判断购物车promotion 提示信息返回
                assert any(activity["original_type"] == "promo_discount" for activity in activity_info) is True
                for activity in activity_info:
                    if activity["original_type"] == "promo_gift":
                        # 断言 赠品信息、tag 信息、文案 有返回
                        assert len(activity["gift_items"]) > 0, f'购物车接口{preorder_v5}'
                        assert activity["tag"] is not None, f'购物车接口{preorder_v5}'
                        assert activity["offer_content"] is not None, f'购物车接口{preorder_v5}'

                sub_total_price = float(section["fee_info"]["sub_total_price"])
                discount = float(section["fee_info"]["discount"])
                if sub_total_price > 40.00:
                    # 如果商品金额大于40，就进行折扣10%
                    discount = sub_total_price * 0.1
                    # 使用一个小的容差来比较浮点数
                    tolerance = 0.00001
                    # 断言discount约等于sub_total_price
                    assert math.isclose(discount, sub_total_price * 0.1,
                                        rel_tol=tolerance), f'加购满40 之后没有折扣10%{preorder_v5}'

                else:
                    # 如果商品金额小于40，就不会进行折扣10%
                    assert discount == 0.00
                    # 继续加购
                    self.add_products_to_cart_and_verify(products=promotion_items,
                                                         delivery_pickup_date=porder["delivery_pickup_date"], headers=ec_login_header)

    @weeeTest.mark.list('Regression', 'Transaction', 'test_promotion_buy_x_dollar_get_y')
    def test_promotion_buy_x_dollar_get_y_dollar(self, ec_login_header):
        """ Promotion-Buy $X, get $Y off（加购instant分类商品满$25减$10） """
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 从instant分类下找找到商品
        normal_category = CategoryCheck().category_product(headers=ec_login_header, zipcode=99991,
                                                           deal_date=porder["delivery_pickup_date"],
                                                           filter_sub_category="instant",
                                                           filters=json.dumps({"delivery_type": "delivery_type_local"}),
                                                           )
        if not normal_category.get('object').get('contents') or normal_category == "upstream request timeout":
            time.sleep(60)
            normal_category = CategoryCheck().category_product(headers=ec_login_header, zipcode=99991,
                                                               deal_date=porder["delivery_pickup_date"],
                                                               filter_sub_category="instant",
                                                               filters=json.dumps(
                                                                   {"delivery_type": "delivery_type_local"}),
                                                               )
            assert normal_category.get('object').get('contents'), f"normal_category={normal_category}"
            promotion_items = [
                item["data"] for item in normal_category["object"]["contents"]
                if
                item["data"].get("sold_status") == "available" and item["data"].get("is_presale") is False and item[
                    "data"].get("min_order_quantity") == 1
            ]

        else:
            promotion_items = [
                item["data"] for item in normal_category["object"]["contents"]
                if
                item["data"].get("sold_status") == "available" and item["data"].get("is_presale") is False and item[
                    "data"].get("min_order_quantity") == 1
            ]

        # 查询购物车
        preorder_v5 = self.query_preorder_v5_with_retry(headers=ec_login_header,
                                                        cart_domain="grocery", products=promotion_items,
                                                        delivery_pickup_date=porder["delivery_pickup_date"]
                                                        )
        self.assert_cart_not_empty(preorder_v5)

        # 检查是否需要继续加购
        for section in preorder_v5["object"]["sections"]:
            if section["cart_id"] == "normal":
                activity_info = section["activity_info"]
                # 判断购物车promotion 提示信息返回
                assert any(activity["original_type"] == "promo_reduce" for activity in activity_info) is True
                for activity in activity_info:
                    if activity["original_type"] == "promo_reduce":
                        # 断言 赠品信息、tag 信息、文案 有返回
                        assert activity["tag"] is not None, f'购物车接口{preorder_v5}'
                        assert activity["offer_content"] is not None, f'购物车接口{preorder_v5}'

                sub_total_price = float(section["fee_info"]["sub_total_price"])
                discount = float(section["fee_info"]["discount"])
                if sub_total_price > 25.00:
                    # 如果商品金额大于25，就减10
                    discount = 10.00
                    assert discount == 10.00, f'加购满$25 之后没有减10{preorder_v5}'

                else:
                    # 如果商品金额小于25，就不会减10
                    assert discount == 0.00, f'加购未满$25，没有折扣{preorder_v5}'
                    # 继续加购
                    self.add_products_to_cart_and_verify(products=promotion_items,
                                                         delivery_pickup_date=porder["delivery_pickup_date"], headers=ec_login_header)

    @weeeTest.mark.list('Regression', 'Transaction','test_promotion_buy_x_dollar_items_get_y_dollar')
    def test_promotion_buy_x_dollar_items_get_y_dollar(self, ec_login_header):
        """ Promotion-Buy $X, get $Y off（加购指定商品满$25减$10） """
        tag_id = 10760
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 配置指定商品活动tag_id=10758
        landing_products = QueryPromotionInfo().promotion_landing_products(headers=ec_login_header,
                                                                           tag_id=tag_id)
        assert len(landing_products["object"]["products"]) > 0, f'活动tag_id=10758 页面数据返回异常，{landing_products}'

        # 指定商品
        promotion_items = [
            item for item in landing_products["object"]["products"]
            if item.get("sold_status") == "available" and item.get("is_presale") is False
        ]
        # 判断pdp 返回活动入口
        self.promotion_product_pdp(headers=ec_login_header, promotion_items=promotion_items)

        # 查询购物车
        preorder_v5 = self.query_preorder_v5_with_retry(headers=ec_login_header,
                                                        cart_domain="grocery", products=promotion_items,
                                                        delivery_pickup_date=porder["delivery_pickup_date"]
                                                        )
        self.assert_cart_not_empty(preorder_v5)

        # 检查是否需要继续加购
        for section in preorder_v5["object"]["sections"]:
            if section["cart_id"] == "normal":

                sub_total_price = float(section["fee_info"]["sub_total_price"])
                sub_total_price = float(section["fee_info"]["sub_total_price"])
                discount = float(section["fee_info"]["discount"])
                if sub_total_price > 25.00:
                    # 如果商品金额大于25，就减10
                    assert discount == 10.00, f'加购满$25 之后没有减10{preorder_v5}'

                    # 判断购物车promotion 提示信息返回
                    activity_info = section["activity_info"]
                    self.check_cart_promotion_info(activity_info=section["activity_info"], tag_id=tag_id,
                                                   promo_type="promo_reduce")

                    promotion_landing = self.promotion_gift_landing(headers=ec_login_header,
                                                                    ps_id=tag_id, promo_type="promo_reduce")
                    # 断言解锁活动成功
                    assert promotion_landing["object"][
                               "promo_status"] == "AVAILABLE", f'活动landing 赠品数据返回异常{promotion_landing}'

                else:
                    # 如果商品金额小于25，就不会减10
                    assert discount == 0.00, f'加购未满$25，没有折扣{preorder_v5}'
                    # 继续加购
                    self.add_products_to_cart_and_verify(products=promotion_items,
                                                         delivery_pickup_date=porder["delivery_pickup_date"], headers=ec_login_header)
                    # 每次加购页面都会访问活动landing页面
                    promotion_landing = self.promotion_gift_landing(headers=ec_login_header,
                                                                    ps_id=tag_id, promo_type="promo_reduce")
                    # 断言未解锁活动
                    assert promotion_landing["object"][
                               "promo_status"] == "NOT_VALID", f'活动landing 赠品数据返回异常{promotion_landing}'

    @weeeTest.mark.list('Regression','Transaction', 'test_promotion_buy_x_dollar_get_y_free')
    def test_promotion_buy_x_dollar_items_get_y_free(self, ec_login_header):
        """ Promotion-Buy $X, get free item Y（加购指定商品满 $30赠送商品30630） """
        gift_product_id = 30630
        tag_id = 10758
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 配置指定商品活动tag_id=10758
        landing_products = QueryPromotionInfo().promotion_landing_products(headers=ec_login_header,
                                                                           tag_id=tag_id)
        assert len(landing_products["object"]["products"]) > 0, f'活动tag_id=10758 页面数据返回异常，{landing_products}'

        # 指定商品
        promotion_items = [
            item for item in landing_products["object"]["products"]
            if item.get("sold_status") == "available" and item.get("is_presale") is False
        ]
        # 判断pdp 返回活动入口
        self.promotion_product_pdp(headers=ec_login_header, promotion_items=promotion_items)

        # 查询购物车
        preorder_v5 = self.query_preorder_v5_with_retry(headers=ec_login_header,
                                                        cart_domain="grocery", products=promotion_items,
                                                        delivery_pickup_date=porder["delivery_pickup_date"]
                                                        )
        self.assert_cart_not_empty(preorder_v5)

        # 检查是否需要继续加购
        for section in preorder_v5["object"]["sections"]:
            if section["cart_id"] == "normal":

                sub_total_price = float(section["fee_info"]["sub_total_price"])
                if sub_total_price > 30.00:
                    # 如果商品金额大于30，有赠品 30630
                    self.check_product_add_to_cart_success(gift_product_id, headers=ec_login_header)
                    # 判断购物车promotion 提示信息返回
                    activity_info = section["activity_info"]
                    self.check_cart_promotion_info(activity_info=section["activity_info"], tag_id=tag_id,
                                                   promo_type="promo_gift")

                    promotion_landing = self.promotion_gift_landing(headers=ec_login_header,
                                                                    ps_id=tag_id, promo_type="promo_gift")
                    # 断言解锁赠品成功
                    assert promotion_landing["object"][
                               "promo_status"] == "AVAILABLE", f'活动landing 赠品数据返回异常{promotion_landing}'

                    # # 如果商品金额小于30，没有赠品 30630，继续加购
                    # self.add_products_to_cart_and_verify(products=promotion_items,
                    #                                      delivery_pickup_date=porder["delivery_pickup_date"])
                else:

                    # 如果商品金额小于30，没有赠品 30630，继续加购
                    self.add_products_to_cart_and_verify(products=promotion_items,
                                                         delivery_pickup_date=porder["delivery_pickup_date"], headers=ec_login_header)
                    self.check_product_add_to_cart_fail(gift_product_id, headers=ec_login_header)
                    # 每次加购页面都会访问活动landing页面
                    promotion_landing = self.promotion_gift_landing(headers=ec_login_header,
                                                                    ps_id=tag_id, promo_type="promo_gift")
                    # 断言未解锁赠品
                    assert promotion_landing["object"][
                               "promo_status"] == "NOT_VALID", f'活动landing 赠品数据返回异常{promotion_landing}'

    def query_preorder_v5_with_retry(self, headers, cart_domain, products, delivery_pickup_date):
        try:
            preorder_v5 = QueryPreOrder().query_preorder_v5(headers=headers, cart_domain=cart_domain)
            if len(preorder_v5["object"]["sections"]) == 0:
                # 如果购物车为空，则走加购接口
                self.add_products_to_cart(products=products, delivery_pickup_date=delivery_pickup_date, headers=headers)
                preorder_v5 = QueryPreOrder().query_preorder_v5(headers=headers, cart_domain=cart_domain)
            return preorder_v5
        except Exception as e:
            # 这里应该有一些日志记录异常
            print(f"Error querying preorder v5: {e}")
            raise

    def assert_cart_not_empty(self, preorder_v5):
        assert len(preorder_v5["object"]["sections"]) > 0, f'购物车商品为空，请确认{preorder_v5}'

    def add_products_to_cart(self, products, delivery_pickup_date, headers):
        # 加购
        CommCheckProductsWithCart().products_add_to_cart(
            headers=headers,
            products=products,
            porder_deal_date=delivery_pickup_date,
            product_source="mweb_category-item_list-null"
        )

    def check_product_add_to_cart_success(self, product_id, headers):
        # 加购是否成功
        CommCheckProductsWithCart().check_product_add_to_cart_success(
            headers=headers,
            cart_domain="grocery",
            product_id=product_id
        )

    def check_product_add_to_cart_fail(self, product_id, headers):
        # 购物车没有这个商品
        CommCheckProductsWithCart().check_product_add_to_cart_fail(
            headers=headers,
            cart_domain="grocery",
            product_id=product_id
        )

    def check_cart_promotion_info(self, activity_info, tag_id, promo_type):
        # 判断购物车promotion 提示信息返回
        # 查找ID为"10760"的活动
        activity = next((item for item in activity_info if item['id'] == str(tag_id)), None)
        # 断言找到的活动的original_type字段值为"promo_reduce"
        assert activity is not None, f"购物车里活动{tag_id}信息未返回"

        assert any(activity["original_type"] == promo_type for activity in activity_info) is True
        for activity in activity_info:
            if activity["id"] == tag_id:
                # 断言 赠品信息、tag 信息、文案 有返回
                assert activity["original_type"] == promo_type, f'购物车接口{activity_info}'
                assert activity["tag"] is not None, f'购物车接口{activity_info}'
                assert activity["offer_content"] is not None, f'购物车接口{activity_info}'
                if promo_type == "promo_gift":
                    assert len(activity["gift_items"]) > 0, f'购物车接口{activity_info}'

    def add_products_to_cart_and_verify(self, products, delivery_pickup_date, headers):
        self.add_products_to_cart(products=products, delivery_pickup_date=delivery_pickup_date, headers=headers)
        # 再次查询购物车以验证商品是否成功加入
        preorder_v5 = self.query_preorder_v5_with_retry(headers=headers,
                                                        cart_domain="grocery",
                                                        products=products,
                                                        delivery_pickup_date=delivery_pickup_date
                                                        )
        self.assert_cart_not_empty(preorder_v5)

    def promotion_gift_landing(self, headers, ps_id, promo_type):
        promo_landing = QueryPromotionInfo().promotion_landing(headers=headers, ps_id=ps_id)
        assert promo_landing["object"]["promo_description"] is not None, f'活动landing 赠品数据返回异常{promo_landing}'
        assert promo_landing["object"]["promo_icon"] is not None, f'活动landing 赠品数据返回异常{promo_landing}'
        assert promo_landing["object"]["promo_tip"] is not None, f'活动landing 赠品数据返回异常{promo_landing}'
        assert promo_landing["object"]["promo_title"] is not None, f'活动landing 赠品数据返回异常{promo_landing}'
        if promo_type == "promo_gift":
            assert promo_landing["object"]["type"] == "promo_gift", f'活动landing 赠品数据返回异常{promo_landing}'
            # 赠品活动
            assert len(promo_landing["object"]["gifts"]) > 0, f'活动landing 赠品数据返回异常{promo_landing}'
            # 满减活动 promo_reduce
            # 赠品活动 promo_gift
        elif promo_type == "promo_reduce":
            assert promo_landing["object"]["type"] == "promo_reduce", f'活动landing 赠品数据返回异常{promo_landing}'

        return promo_landing

    def promotion_product_pdp(self, headers, promotion_items):
        for item in promotion_items:
            promotion_product = QueryPromotionInfo().query_promotion_product(headers=headers,
                                                                             product_id=item["id"])
            assert len(promotion_product["object"][
                           "promotions"]) > 0, f'该商品{item["id"]}pdp未返回活动信息，请确认{promotion_product["object"]}'
            promotions = promotion_product["object"]["promotions"]
            for promotion in promotions:
                assert promotion[
                           "promote_title"] is not None, f'该商品{item["id"]}pdp未返回活动信息，请确认{promotion_product["object"]}'
                assert promotion[
                           "rule_desc"] is not None, f'该商品{item["id"]}pdp未返回活动信息，请确认{promotion_product["object"]}'
                assert promotion[
                           "use_url"] is not None, f'该商品{item["id"]}pdp未返回活动信息，请确认{promotion_product["object"]}'
                assert promotion[
                           "use_url_text"] is not None, f'该商品{item["id"]}pdp未返回活动信息，请确认{promotion_product["object"]}'
                assert promotion[
                           "promote_icon"] is not None, f'该商品{item["id"]}pdp未返回活动信息，请确认{promotion_product["object"]}'
                assert promotion[
                           "promote_tip"] is not None, f'该商品{item["id"]}pdp未返回活动信息，请确认{promotion_product["object"]}'
                assert "/en/promotion/free-gift/landing" in promotion[
                    "use_url"], f'该商品{item["id"]}pdp未返回活动信息，请确认{promotion_product["object"]}'
                # 访问查看更多
                CommCheckFunction().comm_check_link(view_link=promotion["use_url"], headers=headers)
