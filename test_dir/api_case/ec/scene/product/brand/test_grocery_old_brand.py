# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import json
import weeeTest

from test_dir.api.ec.ec_item.product.pdp_detail import PdpDetail
from test_dir.api.ec.ec_item.search_v3.search_by_brand import SearchByBrand
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api_case.ec.common.common_check import CommonCheck


class TestGroceryOldBrand(weeeTest.TestCase):

    @weeeTest.mark.list('grocery_old_brand', 'Regression',  'Transaction')
    def test_grocery_old_brand(self, ec_login_header):
        """ 品牌-老品牌页面验证流程 """
        # 获取用户的preorder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        product_id = 9819
        # 根据指定商品访问商品pdp的所有信息，获取品牌信息
        pdp_detail = PdpDetail().pdp_detail(headers=ec_login_header, product_id=product_id,
                                            zipcode=porder["zipcode"], sales_org_id=porder["sales_org_id"])
        assert pdp_detail["object"][
                   "product"] is not None, f'该商品{product_id}未返回商品信息，请确认{pdp_detail["object"]}'
        assert pdp_detail["object"]["product"]["brand_key"] is not None
        assert pdp_detail["object"]["product"]["brand_name"] is not None

        brand_key = pdp_detail["object"]["product"]["brand_key"]
        brand_slug = pdp_detail["object"]["product"]["brand_slug"]
        brand_name = pdp_detail["object"]["product"]["brand_name"]
        # 1、访问品牌列表页面
        brand_res = self.brand_res_assertion(headers=ec_login_header, zipcode=porder["zipcode"],
                                             date=porder["delivery_pickup_date"], brand_key=brand_key,
                                             brand_name=brand_name, product_id=product_id
                                             )
        # brand_info_vo = brand_res["brand_info_vo"]
        # categories = brand_res["categories"]
        # filters = brand_res["filters"]
        # products = brand_res["products"]
        # sorts = brand_res["sorts"]
        # venders = brand_res["venders"]
        # 2、对商品信息进行基础校验
        # CommonCheck().check_products_info(category_type="others", source="brand", products=brand_res["products"])
        # 检查商品基础信息
        for index, product in enumerate(brand_res["products"]):
            CommonCheck().check_product_info(ec_login_header, product, category_type="others", source="others", filters="others")

        # 3、对商品不同状态进程操作
        # 验证商品状态公共断言方法
        for product in brand_res["products"]:
            CommonCheck().check_product_status_assertion(headers=ec_login_header,
                                                         deal_date=porder["delivery_pickup_date"],
                                                         source="mweb_brand-item_list-null",
                                                         product=product
                                                         )

        # 4、切换品牌子分类
        self.search_categories_assertion(headers=ec_login_header, zipcode=porder["zipcode"],
                                         date=porder["delivery_pickup_date"], brand_key=brand_key,
                                         brand_name=brand_name, product_id=product_id,
                                         categories=brand_res["categories"])
        # 5、品牌排序
        self.search_sorts_assertion(headers=ec_login_header, zipcode=porder["zipcode"],
                                    date=porder["delivery_pickup_date"], brand_key=brand_key,
                                    brand_name=brand_name, product_id=product_id,
                                    sorts=brand_res["sorts"]
                                    )
    @weeeTest.mark.list('B2B', 'Regression',  'Transaction')
    def test_b2b_grocery_old_brand(self, ec_login_header):
        """ 品牌-老品牌页面验证流程 """
        # 获取用户的preorder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        product_id = 9819
        # 根据指定商品访问商品pdp的所有信息，获取品牌信息
        pdp_detail = PdpDetail().pdp_detail(headers=ec_login_header, product_id=product_id,
                                            zipcode=porder["zipcode"], sales_org_id=porder["sales_org_id"])
        assert pdp_detail["object"][
                   "product"] is not None, f'该商品{product_id}未返回商品信息，请确认{pdp_detail["object"]}'
        assert pdp_detail["object"]["product"]["brand_key"] is not None
        assert pdp_detail["object"]["product"]["brand_name"] is not None

        brand_key = pdp_detail["object"]["product"]["brand_key"]
        brand_slug = pdp_detail["object"]["product"]["brand_slug"]
        brand_name = pdp_detail["object"]["product"]["brand_name"]
        # 1、访问品牌列表页面
        brand_res = self.brand_res_assertion(headers=ec_login_header, zipcode=porder["zipcode"],
                                             date=porder["delivery_pickup_date"], brand_key=brand_key,
                                             brand_name=brand_name, product_id=product_id
                                             )
        # brand_info_vo = brand_res["brand_info_vo"]
        # categories = brand_res["categories"]
        # filters = brand_res["filters"]
        # products = brand_res["products"]
        # sorts = brand_res["sorts"]
        # venders = brand_res["venders"]
        # 2、对商品信息进行基础校验
        # CommonCheck().check_products_info(category_type="others", source="brand", products=brand_res["products"])
        # 检查商品基础信息
        for index, product in enumerate(brand_res["products"]):
            CommonCheck().check_product_info(ec_login_header, product, category_type="others", source="others", filters="others")

        # 3、对商品不同状态进程操作
        # 验证商品状态公共断言方法
        for product in brand_res["products"]:
            CommonCheck().check_product_status_assertion(headers=ec_login_header,
                                                         deal_date=porder["delivery_pickup_date"],
                                                         source="mweb_brand-item_list-null",
                                                         product=product
                                                         )

        # 4、切换品牌子分类
        self.search_categories_assertion(headers=ec_login_header, zipcode=porder["zipcode"],
                                         date=porder["delivery_pickup_date"], brand_key=brand_key,
                                         brand_name=brand_name, product_id=product_id,
                                         categories=brand_res["categories"])
        # 5、品牌排序
        self.search_sorts_assertion(headers=ec_login_header, zipcode=porder["zipcode"],
                                    date=porder["delivery_pickup_date"], brand_key=brand_key,
                                    brand_name=brand_name, product_id=product_id,
                                    sorts=brand_res["sorts"]
                                    )

    def brand_res_assertion(self, headers, zipcode, date, brand_key, product_id, brand_name, filters: str = None):
        brand_res = SearchByBrand().search_by_brand(headers=headers,
                                                    zipcode=zipcode, date=date,
                                                    filterBrand=brand_key,
                                                    filters=filters)

        assert brand_res["result"] is True
        assert brand_res["object"][
                   "total_count"] > 0, f'该商品：{product_id}品牌：{brand_name}未返回商品，请确认{brand_res["object"]}'
        assert brand_res["object"]["brand_info_vo"][
                   "brand_key"] == brand_key, f'该商品：{product_id}品牌：{brand_key}返回不对，请确认{brand_res["object"]}'
        assert brand_res["object"]["brand_info_vo"][
                   "brand_name"] == brand_name, f'该商品：{product_id}品牌：{brand_name}返回不对，请确认{brand_res["object"]}'

        return brand_res["object"]

    def search_categories_assertion(self, headers, categories: list, zipcode, date, brand_key, product_id, brand_name
                                    ):
        # 对返回结果categories 排序操作及断言
        for category in categories:
            category_res = SearchByBrand().search_by_brand(headers=headers, zipcode=zipcode, date=date,
                                                           filterBrand=brand_key,
                                                           filters=json.dumps(
                                                               {"catalogue_num": category["catalogue_num"]}))

            assert category_res["result"] is True
            assert category_res["object"][
                       "total_count"] > 0, f'该商品：{product_id}品牌：{brand_name}未返回商品，请确认{category_res["object"]}'

    def search_sorts_assertion(self, headers, sorts: list, zipcode, date, brand_key, product_id, brand_name,
                               filters: str = None):
        # 对返回结果sorts 排序操作及断言
        for sort in sorts:
            # 过滤filter
            sorts_res = SearchByBrand().search_by_brand(headers=headers, zipcode=zipcode, date=date,
                                                        filterBrand=brand_key,
                                                        sort=sort["sort_key"])

            assert sorts_res["object"][
                       "total_count"] > 0, f'该商品：{product_id}品牌：{brand_name}未返回商品，请确认{sorts_res["object"]}'
            products = sorts_res["object"]["products"]
            # 筛选出类型为 'product' 的内容
            products = [content for content in sorts_res["object"]["products"] if content["sold_status"] == "available"]
            sort_1 = products[0]["price"]
            sort_2 = products[-1]["price"]
            if sort["sort_key"] == "price_asc":
                # 断言商品"价格：低到高"
                assert sort_1 <= sort_2, f"商品价格：低到高 排序不对：{sort_1}<={sort_2}"
            if sort["sort_key"] == "price_desc":
                # print(products)
                # 断言商品"价格：高到低"
                assert sort_1 >= sort_2, f"商品价格：高到低排序不对：{sort_1}>={sort_2}"


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
