# !/usr/bin/python3
# -*- coding: utf-8 -*-

import click

import weeeTest

from test_dir.api.ec.ec_content.admin.generate_collection_data import GenerateCollectionData
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder


class TestGenerateCollectionData(weeeTest.TestCase):
    # @weeeTest.mark.list('Transaction', 'dev')
    def test_generate_collection_data(self, ec_login_header):
        """ # 单接口测试/ec/content/admin/generateCollectionData """
        # 经与huimin讨论，该接口已废弃
        # 获取用户preorder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        GenerateCollectionData().generate_collection_data(headers=ec_login_header)
        # 断言
        assert self.response["result"] is True


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
