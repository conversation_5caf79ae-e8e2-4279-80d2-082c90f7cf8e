"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_message_home_header.py
@Description    :  
@CreateTime     :  2023/11/10 17:10
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/11/10 17:10
"""
import weeeTest

from test_data.ec.simple.common import Header
from test_dir.api.ec.ec_customer.message.message_home_header import MessageHomeHeader
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder


class TestMessageHomeHeader(weeeTest.TestCase):
    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    # @weeeTest.mark.list('Transaction', 'tb1', 'dev')
    def test_message_home_header(self, *args, ec_login_header):
        """获取首页的top message"""
        # 线上没有数据
        # headers = Header().login_header(email=args[0]["login"]["email"], password=args[0]["login"]["password"])
        # 获取用户的preorder
        # preorder = QuerySimplePreOrder().query_simple_preorder_v1(headers=headers)["object"]
        MessageHomeHeader().message_home_header(headers=ec_login_header, version="v2", lang="zh",
                                                       date=preorder["deal_date"])["object"][0]["message"]["short_message"]
        # 断言
        assert self.response["result"] is True

