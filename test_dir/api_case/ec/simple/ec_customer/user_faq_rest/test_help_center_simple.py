# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/7/31
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from test_dir.api.ec.ec_customer.user_faq_rest.user_faq_rest import Userfaqrest


class TestHelpCenter(weeeTest.TestCase):
    @weeeTest.mark.list('Social', 'product', 'dev')
    def test_help_center(self, ec_jiufen_header):
        """帮助中心"""
        article_list = Userfaqrest().help_center(headers=ec_jiufen_header, data={"categoryId":270})
        assert article_list.get("object"), f"获取帮助中心文章列表失败, {article_list}"
