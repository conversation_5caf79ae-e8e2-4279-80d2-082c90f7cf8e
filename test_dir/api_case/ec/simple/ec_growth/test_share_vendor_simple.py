# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
<AUTHOR>  <PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  seller_desc.py
@Description    :
@CreateTime     :  2023/7/20 20:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/20 20:06
"""
import weeeTest
from test_dir.api.ec.ec_growth.share_vendor import ShareVendor


class TestVendorShareSimple(weeeTest.TestCase):

    @weeeTest.data.file(file_name='ec_marketplace_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_vendor_share(self, *args, ec_login_header):
        """
              #test_vendor_share 测试商家分享信息
        """
        ShareVendor().share_vendor(headers=ec_login_header, vendor_id=args[0]["seller_info"]["vendor_id"])
        # 断言
        assert self.response["result"] is True
