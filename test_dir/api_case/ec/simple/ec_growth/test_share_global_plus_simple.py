import uuid
import weeeTest
from test_dir.api.ec.ec_growth.share_global_plus import ShareGlobalPlus


class TestShareGlobalPlusSimple(weeeTest.TestCase):
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_v1_content_feed_search_global(self, ec_login_header):
        """ # 获取waterfall的瀑布流分享信息 """
        ShareGlobalPlus().share_global_plus(headers=ec_login_header)
        assert self.response["result"] is True
