import weeeTest
from test_dir.api.ec.ec_growth.get_global_plus_banner import GetGlobalPlusBanner


class TestGetGlobalPlusBannerSimple(weeeTest.TestCase):
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_v1_content_feed_search_global(self, ec_login_header):
        """ # 获取waterfall的瀑布流分享信息 """
        GetGlobalPlusBanner().get_global_plus_banner(headers=ec_login_header)
        assert self.response["result"] is True

