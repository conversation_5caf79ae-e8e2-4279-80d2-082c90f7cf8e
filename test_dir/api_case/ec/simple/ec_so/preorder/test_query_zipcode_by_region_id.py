"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_query_zipcode_by_region_id.py
@Description    :  
@CreateTime     :  2023/10/19 18:01
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/10/19 18:01
"""
import weeeTest

from test_dir.api.ec.ec_so.preorder.query_zipcode_by_region_id import QueryZipcodeByRegionId


class TestQueryZipcodeByRegionId(weeeTest.TestCase):
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_query_zipcode_by_region_id(self, ec_login_header):
        """ test_query_zipcode_by_region_id """
        QueryZipcodeByRegionId().query_zipcode_by_region_id(headers=ec_login_header,zipcode="98011")
        assert self.response["result"] is True

