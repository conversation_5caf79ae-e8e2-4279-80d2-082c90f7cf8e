import weeeTest
from test_dir.api.ec.ec_mkt.banner.global_trending_list import QueryTrendingListByDsKey
from test_dir.api.ec.ec_so.seller.update_seller_preOrderLine_v2 import SellerPreOrderV2


class TestSellerPreOrderV2(weeeTest.TestCase):

    @weeeTest.data.file(file_name='ec_marketplace_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_seller_preorderline_v2(self, *args, ec_login_header):
        """# Global热销商家加购商品"""
        sale = QueryTrendingListByDsKey().global_trending_list(ec_login_header, args[0]["dataobject_key_global"]["source_key"],
                                                               args[0]["dataobject_key_global"][
                                                                   "dataobject_key_treding"])

        SellerPreOrderV2().seller_preorderline_v2(ec_login_header, vendor_id=sale["object"][0]["seller_id"],
                                                  product_id=sale["object"][0]["products"][0]["id"])
        assert self.response["result"] is True

