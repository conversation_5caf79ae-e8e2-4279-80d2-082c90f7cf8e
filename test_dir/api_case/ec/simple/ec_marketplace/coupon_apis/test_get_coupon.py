import weeeTest

from test_dir.api.ec.ec_marketplace.global_puls_interfaces.get_global_welcome_coupons import GetGlobalWelcomeCoupons
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api_case.ec.common.common_check_mkpl import Welcome<PERSON>ouponCheck
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder


class TestGetCoupon(weeeTest.TestCase):
    @weeeTest.mark.list("product", 'dev')
    def test_get_coupon(self, ec_login_header):
        """landing页面的优惠券列表 fou you"""
        SetUserPorder().set_user_new_porder(headers=ec_login_header, zipcode="98011", lang="en")
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]

        coupon = GetGlobalWelcomeCoupons().get_global_welcome_coupons(headers=ec_login_header,
                                                                      zipcode=porder["zipcode"],
                                                                      source_key=None, tab_key="all")
        WelcomeCouponCheck.all_welcome_coupons(coupon, headers=ec_login_header)
        assert coupon['result'] is True
