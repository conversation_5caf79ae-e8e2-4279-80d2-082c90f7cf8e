# -*- coding:utf-8 -*-
import weeeTest

from test_dir.api.ec.ec_marketplace.mkpl_top_x.mkpl_top_x import MkplTopX
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder


class TestMkplTopTrendingSimple(weeeTest.TestCase):

    @weeeTest.data.file(file_name='ec_marketplace_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_mkpl_top_trending_landing(self, *args, ec_login_header):
        """# 获取落地页top trending数据"""

        SetUserPorder().set_user_new_porder(headers=ec_login_header, zipcode="98011", lang="en")
        top_trending_landing=MkplTopX().mkpl_top_ranking(headers=ec_login_header, tab=args[0]["topX"]["toptrending"],
                                    key=args[0]["waterfall_category"]["default"][1], page_num=1)
        assert self.response["result"] is True
        # 新增断言：确保top_selling_landing["object"][1]["contents"]不为空
        assert top_trending_landing["object"][1][
            "contents"], "top_selling_landing['object'][1]['contents'] should not be empty"
