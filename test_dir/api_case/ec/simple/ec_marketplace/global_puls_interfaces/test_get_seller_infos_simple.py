import weeeTest

from test_dir.api.ec.ec_marketplace.global_puls_interfaces.get_seller_infos import GetSellerInfos
from test_dir.api.ec.ec_marketplace.global_puls_interfaces.list_all_available_seller_ids import \
    ListAllAvailableSellerIds


class TestGetSellerInfosSimple(weeeTest.TestCase):
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_get_seller_infos(self, ec_login_header):
        """  # 获取waterfall的商家卡片信息  """
        data = ListAllAvailableSellerIds().list_all_available_seller_ids(headers=ec_login_header, zipcode="98011")
        seller = data["object"]
        assert isinstance(seller, list) and seller, f"seller不是列表或为空seller={seller}"
        GetSellerInfos().get_seller_infos(ec_login_header, seller_ids=seller, zipcode="98011")
        assert self.response["result"] is True and self.response.get("object")
