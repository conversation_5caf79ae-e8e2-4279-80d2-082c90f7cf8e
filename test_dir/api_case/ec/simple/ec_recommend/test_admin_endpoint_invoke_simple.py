import weeeTest
from test_dir.api.ec.ec_recommend.admin_endpoint_invoke import AdminEndpointInvoke
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder


class TestAdminEndpointInvoke(weeeTest.TestCase):

    @weeeTest.data.file(file_name='ec_marketplace_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_admin_endpoint_invoke(self, *args, ec_login_header):
        """# 获取waterfall大数据返回的商品 admin_endpoint_invoke"""
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        sales_org_id = porder["sales_org_id"]
        AdminEndpointInvoke().admin_endpoint_invoke(ec_login_header, category=args[0]["waterfall_category"]["default"][1],
                                                    category_lib_id=0,
                                                    user_id=args[0]["waterfall_category"]["user_id"],
                                                    device_id=args[0]["waterfall_category"]["device_id"],
                                                    sales_org_id=sales_org_id)
        assert self.response["result"] is True

