import weeeTest
from test_dir.api.ec.ec_recommend.record_marketplace_content import RecordMarketplaceContent


class TestRecordMarketplaceContent(weeeTest.TestCase):

    @weeeTest.data.file(file_name='ec_marketplace_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_record_marketplace_content(self, *args, ec_login_header):
        """# 获取waterfall大数据返回的商品 admin_endpoint_invoke"""
        RecordMarketplaceContent().record_marketplace_content(ec_login_header, recommend_session="123",
                                                              parent_nums=args[0]["waterfall_category"]["in"][1],
                                                              type=args[0]["waterfall_category"]["category_type"][1],
                                                              special_nums=None
                                                              )
        assert self.response["result"] is True

