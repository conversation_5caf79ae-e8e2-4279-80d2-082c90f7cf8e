import uuid
import weeeTest
from test_dir.api.ec.ec_item.search_vender_rest.v1_content_feed_search_global import V1ContentFeedSearchGlobal


recommend_session = str(uuid.uuid4())

class TestV1ContentFeedSearchGlobal(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_v1_content_feed_search_global(self, ec_login_header):
        """ # 获取waterfall的瀑布流 test_v1_content_feed_search_global """
        V1ContentFeedSearchGlobal().v1_content_feed_search_global(headers=ec_login_header, recommend_session=recommend_session)
        assert self.response["result"] is True
