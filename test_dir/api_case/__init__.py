# !/usr/bin/python3
# -*- coding: utf-8 -*-
import json
import time

from weeeTest import log

from test_data.ec.simple.common import Header
from test_dir.api_case.ec.common.get_root_dir import get_project_dir

# import os.path
# import shutil
autotest_header = Header.login_header()
log.debug(f"autotest_header: {autotest_header}")
with open(get_project_dir() + "/test_data/autotest_token.json", "w", encoding='utf-8') as f:
    f.write(json.dumps(autotest_header))
    time.sleep(2)

zhuli_header = Header.login_header(email='<EMAIL>', password='1234qwer')
log.debug(f"zhuli_header: {zhuli_header}")
with open(get_project_dir() + "/test_data/lizhu2_token.json", "w", encoding='utf-8') as f:
    f.write(json.dumps(zhuli_header))
    time.sleep(2)


#
# def delete_log_files():
#     try:
#         if os.path.exists("./logs"):
#             shutil.rmtree("./logs")
#         os.mkdir("./logs")
#     except Exception as e:
#         print("delete dir failed or dir doesn't exists.")
#
#     if os.path.exists("./fail_token.json"):
#         os.remove("./fail_token.json")
#
#     if os.path.exists("./login_request.txt"):
#         os.remove("./login_request.txt")
#
#     if os.path.exists("./login_resp_case.txt"):
#         os.remove("./login_resp_case.txt")
#
#     if os.path.exists("./login_response_request.txt"):
#         os.remove("./login_response_request.txt")
#
#     if os.path.exists("./porder.txt"):
#         os.remove("./porder.txt")
#
#
# delete_log_files()

