# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  user_bak.py
@Description    :
@CreateTime     :  2023/5/30 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/30 11:06
"""

import datetime

import weeeTest
from weeeTest import weeeConfig

class UpdatePreOrderLine(weeeTest.TestCase):

    def porder_items_v2(self, headers, product_id,
                        date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
                        refer_type: str = "normal", is_pantry: bool = False, is_alcohol: bool = False,
                        is_mkpl: bool = False,
                        source: str = "portal-recommend", quantity: int = 1):
        """# 非购物车页面加购使用，加购接口v2"""
        data = [{
            "product_id": product_id,
            "source": "product-cart",
            "source_store": "cn",
            "refer_type": refer_type,
            "refer_value": "",
            "delivery_date": date,
            "min_order_quantity": 1,
            "is_mkpl": is_mkpl,
            "positionInfoT2": {
                # "modSecPos": {
                #     "mod_nm": "product_detail",
                #     "mod_pos": 0
                # },
                # "prodPos": 0,
                # "context": {
                #     "page_target": product_id,
                #     "global_vendor": None
                # }
            },
            "ctx": {
                #     "page_target": product_id,
                #     "global_vendor": None
            },
            "quantity": quantity,
        }]

        self.put(url='/ec/so/porder/items/v2', headers=headers, json=data)
        return self.response

    def porder_items_v3_tradin(self, headers, product_id, biz_type: str = "normal",
                               date: str = (datetime.datetime.now() + datetime.timedelta(days=2)).strftime(
                                   '%Y-%m-%d'),
                               refer_type: str = "normal", is_pantry: bool = False, is_alcohol: bool = False,
                               is_mkpl: bool = False,
                               source: str = "trade_in", quantity: int = 1):
        """#加购换购的商品，加购接口v3"""
        data = [{"product_id": product_id,
                 "biz_type": biz_type,
                 "delivery_date": date,
                 "is_pantry": is_pantry,
                 "is_alcohol": is_alcohol,
                 "is_mkpl": is_mkpl,
                 "item_type": "",
                 "min_order_quantity": 1,
                 "new_source": "",  # 埋点
                 "positionInfoT2": "",  # 埋点
                 "source": source,  # 加购来源
                 "source_store": "",
                 "quantity": quantity,
                 "refer_type": refer_type,
                 "refer_value": ""
                 }]
        self.put(url='/ec/so/porder/items/v3', headers=headers, json=data)
        return self.response

    def porder_items_v3(self, headers, product_id, biz_type: str = "normal",
                        date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
                        refer_type: str = "normal", is_pantry: bool = False, is_alcohol: bool = False,
                        item_type: str = "", min_order_quantity: int = 1,
                        is_mkpl: bool = False, source_store: str = "cn",
                        source: str = "portal-recommend", quantity: int = 1, refer_value: str = None,
                        vender_id: int = None,
                        volume_price_support: bool = False

                        ):
        """# 非购物车页面加购使用，加购接口v3"""
        data = [{"product_id": product_id,
                 "biz_type": biz_type,
                 "delivery_date": date,
                 "is_pantry": is_pantry,
                 "is_alcohol": is_alcohol,
                 "is_mkpl": is_mkpl,
                 "item_type": item_type,
                 "min_order_quantity": min_order_quantity,
                 "new_source": "",  # 埋点
                 "positionInfoT2": "",  # 埋点
                 "source": source,  # 加购来源
                 "source_store": source_store,
                 "quantity": quantity,
                 "refer_type": refer_type,
                 "refer_value": refer_value,
                 "vender_id": vender_id,  # mkpl 商品传
                 "volume_price_support": volume_price_support
                 }]
        # 目前17833商品需要更新zipcode==98011才可以购买或加入购物车
        self.put(url='/ec/so/porder/items/v3', headers=headers, json=data)
        return self.response

    def porder_items_cart_v2(self, headers, product_id,
                             date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
                             refer_type: str = "normal", is_pantry: bool = False, is_alcohol: bool = False,
                             is_mkpl: bool = False,
                             source: str = "portal-recommend", quantity: int = 1
                             ):
        """# 购物车页面加购使用，加购接口v2"""
        data = [{"product_id": product_id,
                 "delivery_date": date,
                 "is_pantry": is_pantry,
                 "is_alcohol": is_alcohol,
                 "is_mkpl": is_mkpl,
                 "item_type": "",
                 "min_order_quantity": 1,
                 "new_source": "",  # 埋点
                 "positionInfoT2": "",  # 埋点
                 "source": source,  # 加购来源
                 "source_store": "",
                 "quantity": quantity,
                 "refer_type": refer_type,
                 "refer_value": ""
                 }]
        self.put(url='/ec/so/porder/items/cart/v2', headers=headers, json=data)
        return self.response

    def porder_items_cart_upsell(self, headers, product_id,
                             date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
                             refer_type: str = "normal", is_pantry: bool = False, is_alcohol: bool = False,
                             is_mkpl: bool = False,
                             source: str = "portal-recommend", quantity: int = 1
                             ):
        """# 购物车页面加购使用，加购接口v2"""
        data = [{"product_id": product_id,
                 "delivery_date": date,
                 "is_pantry": is_pantry,
                 "is_alcohol": is_alcohol,
                 "is_mkpl": is_mkpl,
                 "item_type": "",
                 "min_order_quantity": 1,
                 "new_source": "",  # 埋点
                 "positionInfoT2": "",  # 埋点
                 "source": source,  # 加购来源
                 "source_store": "",
                 "quantity": quantity,
                 "refer_type": refer_type,
                 "refer_value": ""
                 }]
        self.put(url='/ec/so/porder/items/v2', headers=headers, json=data)
        return self.response

    def porder_items_cart_v3(self, headers, product_id, biz_type: str = "normal",
                             date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
                             refer_type: str = "normal", is_pantry: bool = False, is_alcohol: bool = False,
                             is_mkpl: bool = False,
                             source: str = "portal-recommend", quantity: int = 1
                             ):
        """# 购物车页面加购使用，加购接口v3"""
        data = [{"product_id": product_id,
                 "biz_type": biz_type,
                 "delivery_date": date,
                 "is_pantry": is_pantry,
                 "is_alcohol": is_alcohol,
                 "is_mkpl": is_mkpl,
                 "item_type": "",
                 "min_order_quantity": 1,
                 "new_source": "",  # 埋点
                 "positionInfoT2": "",  # 埋点
                 "source": source,  # 加购来源
                 "source_store": "",
                 "quantity": quantity,
                 "refer_type": refer_type,
                 "refer_value": ""
                 }]
        self.put(url='/ec/so/porder/items/cart/v3', headers=headers, json=data)
        return self.response

    def add_to_cart_v3(self, headers, data):
        self.put(url='/ec/so/porder/items/v3', headers=headers, json=data)
        return self.response

    def porder_items_cart_v4(self, headers, product_id, refer_type: str = "normal",
                             delivery_date: datetime = "2023-11-05", quantity: int = 1,
                             source: str = "cart"):
        """PC购物车页面加购使用，加购接口v4"""
        # V4 废弃
        data = [{"product_id": product_id,
                 "source": source,
                 "refer_type": refer_type,
                 "refer_value": "",
                 "delivery_date": delivery_date,
                 "min_order_quantity": 1,
                 "product_key": product_id,
                 "is_mkpl": False,
                 "positionInfoT2": {"modSecPos": {"mod_nm": "cart", "mod_pos": 0, "sec_nm": "normal", "sec_pos": 0},
                                    "prodPos": 0},
                 "quantity": quantity}]
        self.put(url='/ec/so/porder/items/cart/v4', headers=headers, json=data)
        return self.response

    def remove_cart_v2(self, headers, product_id,
                       date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
                       refer_type: str = "normal", is_pantry: bool = False, is_alcohol: bool = False,
                       is_mkpl: bool = False,
                       source: str = "portal-recommend"):
        """#删除购物车商品 v2,可以不用了
        """
        data = [{"product_id": product_id,
                 "delivery_date": date,
                 "is_pantry": is_pantry,
                 "is_alcohol": is_alcohol,
                 "is_mkpl": is_mkpl,
                 "item_type": "",
                 "min_order_quantity": 1,
                 "new_source": "",  # 埋点
                 "positionInfoT2": "",  # 埋点
                 "source": source,  # 加购来源
                 "source_store": "",
                 "quantity": 0,
                 "refer_type": refer_type,
                 "refer_value": ""
                 }]
        self.put(url='/ec/so/porder/items/v2', headers=headers, json=data)
        return self.response

    # def clear_cart_items(self, items):
    #     self.put(url='/ec/so/porder/items/v2', headers=headers, json=items)
    #     return self.response

    def remove_non_cart_v3(self, headers, product_id, biz_type: str = "normal",
                           date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
                           refer_type: str = "normal",
                           source: str = "portal-recommend"):
        """#非购物车页面删除购物车商品 v3"""
        data = [{"product_id": product_id,
                 "biz_type": biz_type,
                 "delivery_date": date,
                 "product_key": product_id,
                 "new_source": "",  # 埋点
                 "source": source,  # 加购来源
                 "quantity": 0,
                 "refer_type": refer_type
                 }]
        self.put(url='/ec/so/porder/items/cart/v3', headers=headers, json=data)
        return self.response

    def remove_cart_v3(self, headers, product_id, biz_type: str = "normal",
                       date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
                       refer_type: str = "normal",
                       source: str = "portal-recommend"):
        """#购物车页面删除购物车商品 v3"""
        data = [{"product_id": product_id,
                 "biz_type": biz_type,
                 "delivery_date": date,
                 "product_key": product_id,
                 "new_source": "",  # 埋点
                 "source": source,  # 加购来源
                 "quantity": 0,
                 "refer_type": refer_type
                 }]
        self.put(url='/ec/so/porder/items/cart/v3', headers=headers, json=data)
        return self.response

    def remove_cart_v3_debug(self, headers, product_id,
                             date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
                             refer_type: str = "normal",
                             source: str = "portal-recommend"):
        """#购物车页面删除购物车商品 v3"""
        data = [{"product_id": product_id,
                 "delivery_date": date,
                 "product_key": str(product_id),
                 "new_source": "",  # 埋点
                 "source": source,  # 加购来源
                 "quantity": 0,
                 "refer_type": refer_type,
                 "biz_type": 'normal'
                 }]
        self.put(url='/ec/so/porder/items/cart/v3', headers=headers, json=data)
        return self.response

    def remove_save_for_later(self, headers, product_keys):
        """#购物车页面save4later商品"""
        data = {
            "product_keys": [product_keys]
        }
        self.delete(url='/ec/so/save4later', headers=headers, json=data)
        return self.response

    def add_to_cart_v3(self, headers, data):
        self.put(url='/ec/so/porder/items/v3', headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = UpdatePreOrderLine()

    # user.search_v3()
    # user.email_login(email='<EMAIL>', password='aa123456')

    # user.email_register(email='test.' + user.random_word(num=5) + '@sayweee.com', password='aa123456')
